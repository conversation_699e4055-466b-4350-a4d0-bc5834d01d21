# مكتبتي الحرة - المكتبة الرقمية المجانية

## نظرة عامة

مكتبتي الحرة هي مكتبة رقمية مجانية تتيح للمستخدمين تصفح وقراءة وتحميل الكتب في مختلف المجالات. تم تصميم الموقع باللغة العربية مع دعم كامل للنصوص من اليمين إلى اليسار (RTL) ووضع ليلي جميل.

## الميزات الرئيسية

### للمستخدمين العاديين:
- ✅ تصفح الكتب حسب التصنيفات
- ✅ البحث المتقدم عن الكتب
- ✅ قراءة الكتب أونلاين مع قارئ 3D Flipbook
- ✅ تحميل الكتب (للمسجلين فقط)
- ✅ نظام المفضلة والعلامات المرجعية
- ✅ الوضع الليلي والنهاري
- ✅ تصميم متجاوب للجوال والأجهزة اللوحية
- ✅ واجهة عربية جميلة مع خط Cairo الأنيق

### للمديرين:
- ✅ لوحة تحكم شاملة
- ✅ إدارة الكتب (إضافة، تعديل، حذف)
- ✅ إدارة التصنيفات
- ✅ إدارة المستخدمين (تعليق، حذف)
- ✅ تقارير وإحصائيات مفصلة
- ✅ رسوم بيانية تفاعلية

## التقنيات المستخدمة

### الواجهة الأمامية:
- **HTML5** - هيكل الصفحات
- **CSS3** - التصميم والتنسيق
- **JavaScript ES6** - التفاعل والوظائف
- **Bootstrap 5.3** - إطار العمل للتصميم المتجاوب
- **Font Awesome 6.4** - الأيقونات
- **Google Fonts (Cairo)** - الخط العربي الأنيق

### المكتبات الإضافية:
- **PDF.js** - عرض ملفات PDF
- **Turn.js** - تأثير تقليب الصفحات 3D
- **Chart.js** - الرسوم البيانية
- **Bootstrap Icons** - أيقونات إضافية

## هيكل المشروع

```
مكتبتي الحرة/
├── index.html              # الصفحة الرئيسية
├── book-details.html       # صفحة تفاصيل الكتاب
├── reader.html            # قارئ الكتب
├── login.html             # صفحة تسجيل الدخول
├── admin.html             # لوحة التحكم
├── assets/
│   ├── css/
│   │   ├── style.css      # التصميم الرئيسي
│   │   ├── reader.css     # تصميم القارئ
│   │   └── admin.css      # تصميم لوحة التحكم
│   ├── js/
│   │   ├── main.js        # الوظائف الرئيسية
│   │   ├── auth.js        # نظام المصادقة
│   │   ├── reader.js      # قارئ الكتب
│   │   ├── book-details.js # تفاصيل الكتاب
│   │   └── admin.js       # لوحة التحكم
│   └── images/
│       ├── books/         # أغلفة الكتب
│       └── avatars/       # صور المستخدمين
├── public/
│   ├── uploads/           # ملفات الكتب المرفوعة
│   └── covers/            # أغلفة الكتب
└── README.md              # هذا الملف
```

## كيفية التشغيل

### 1. تحضير البيئة
```bash
# تأكد من وجود خادم ويب محلي
# يمكن استخدام Live Server في VS Code
# أو Python HTTP Server
python -m http.server 8000
```

### 2. فتح الموقع
```
http://localhost:8000
```

### 3. الحسابات التجريبية

#### مستخدم عادي:
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** password123

#### مدير النظام:
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** admin123

## الوظائف المتاحة

### الصفحة الرئيسية
- عرض أحدث الكتب
- الكتب الأكثر قراءة
- اختيارات المحرر
- البحث المتقدم
- تصفح التصنيفات

### صفحة تفاصيل الكتاب
- معلومات مفصلة عن الكتاب
- تقييمات وتعليقات المستخدمين
- أزرار القراءة والتحميل
- اقتراح كتب مشابهة

### قارئ الكتب
- عرض الكتب بتقنية 3D Flipbook
- أدوات التحكم (تكبير، تصغير، ملء الشاشة)
- العلامات المرجعية والملاحظات
- الوضع الليلي للقراءة
- اختصارات لوحة المفاتيح

### لوحة التحكم
- إحصائيات شاملة مع رسوم بيانية
- إدارة الكتب والتصنيفات
- إدارة المستخدمين
- تقارير مفصلة

## الميزات التقنية

### الوضع الليلي
- تبديل سلس بين الوضع النهاري والليلي
- حفظ تفضيل المستخدم في التخزين المحلي
- تطبيق الوضع على جميع عناصر الواجهة

### التصميم المتجاوب
- يعمل بشكل مثالي على الجوال والأجهزة اللوحية
- قوائم تنقل متكيفة
- أحجام خطوط وأزرار مناسبة لكل جهاز

### الأمان
- تشفير كلمات المرور (محاكاة)
- التحقق من صلاحيات المستخدم
- حماية صفحات الإدارة

### الأداء
- تحميل الصور بشكل تدريجي (Lazy Loading)
- ضغط وتحسين الملفات
- استخدام CDN للمكتبات الخارجية

## التطوير المستقبلي

### المرحلة التالية:
- [ ] ربط قاعدة بيانات حقيقية (MySQL/PostgreSQL)
- [ ] تطوير API خلفي بـ PHP أو Node.js
- [ ] نظام رفع الملفات الفعلي
- [ ] نظام إشعارات متقدم
- [ ] تطبيق جوال مصاحب
- [ ] دعم تنسيقات كتب إضافية (EPUB, MOBI)

### تحسينات مقترحة:
- [ ] نظام تقييم وتعليقات متقدم
- [ ] خوارزمية اقتراح كتب ذكية
- [ ] نظام مشاركة اجتماعية
- [ ] دعم لغات إضافية
- [ ] نظام نسخ احتياطي تلقائي

## المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. تطبيق التغييرات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- إنشاء Issue في GitHub
- التواصل عبر البريد الإلكتروني
- مراجعة الوثائق

## شكر وتقدير

- **Bootstrap** - إطار العمل للتصميم
- **Font Awesome** - مكتبة الأيقونات
- **Google Fonts** - خط Cairo العربي
- **Chart.js** - مكتبة الرسوم البيانية
- **PDF.js** - عارض ملفات PDF
- **Turn.js** - تأثيرات تقليب الصفحات

---

**مكتبتي الحرة** - نشر المعرفة للجميع 📚
