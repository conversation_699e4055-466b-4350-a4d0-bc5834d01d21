# دليل التشغيل - مكتبتي الحرة

## 🚀 **كيفية تشغيل المشروع**

### ✅ **الحل الأول: تشغيل مباشر (بدون خادم)**

1. **افتح الملفات مباشرة في المتصفح:**
   ```
   file:///C:/Users/<USER>/Desktop/index.html
   file:///C:/Users/<USER>/Desktop/books.html
   file:///C:/Users/<USER>/Desktop/test-stable.html
   ```

2. **للاختبار السريع:**
   - افتح `test-stable.html` أولاً لاختبار الاستقرار
   - ثم افتح `index.html` للصفحة الرئيسية
   - وأخيراً `books.html` لصفحة الكتب

### ✅ **الحل الثاني: خادم Python البسيط**

1. **افتح Command Prompt في مجلد المشروع:**
   ```cmd
   cd C:\Users\<USER>\Desktop
   ```

2. **شغل خادم Python:**
   ```cmd
   python -m http.server 8000
   ```
   أو
   ```cmd
   python3 -m http.server 8000
   ```

3. **افتح المتصفح على:**
   ```
   http://localhost:8000
   ```

### ✅ **الحل الثالث: خادم Node.js**

1. **إذا كان Node.js مثبت:**
   ```cmd
   npx http-server -p 8000
   ```

2. **افتح المتصفح على:**
   ```
   http://localhost:8000
   ```

### ✅ **الحل الرابع: XAMPP/WAMP**

1. **حمل وثبت XAMPP من:**
   ```
   https://www.apachefriends.org/download.html
   ```

2. **انسخ ملفات المشروع إلى:**
   ```
   C:\xampp\htdocs\my-free-library\
   ```

3. **شغل Apache من XAMPP Control Panel**

4. **افتح المتصفح على:**
   ```
   http://localhost/my-free-library
   ```

## 🔧 **إصلاح مشكلة عدم الاستقرار**

### ✅ **تم إصلاح المشكلة بالفعل!**

تم إضافة الملفات التالية لضمان الاستقرار:
- `assets/js/stability.js` - نظام ضمان الاستقرار
- `test-stable.html` - صفحة اختبار الاستقرار
- تحسينات CSS في `assets/css/style.css`

### ✅ **للتأكد من الاستقرار:**

1. **افتح صفحة الاختبار:**
   ```
   file:///C:/Users/<USER>/Desktop/test-stable.html
   ```

2. **تحقق من:**
   - عدم وجود حركة أو اهتزاز
   - ثبات البطاقات والأزرار
   - عدم وجود تمرير أفقي

3. **إذا كانت مستقرة، افتح الصفحة الرئيسية:**
   ```
   file:///C:/Users/<USER>/Desktop/index.html
   ```

## 📱 **اختبار الميزات**

### ✅ **الصفحة الرئيسية (index.html)**
- عرض الكتب المميزة
- البحث السريع
- التنقل بين الأقسام
- تبديل الوضع الليلي/النهاري

### ✅ **صفحة الكتب (books.html)**
- عرض جميع الكتب
- فلترة حسب التصنيف
- البحث المتقدم
- ترتيب النتائج

### ✅ **صفحة تسجيل الدخول (login.html)**
- نموذج تسجيل الدخول
- التحقق من البيانات
- رسائل الخطأ والنجاح

### ✅ **صفحة التسجيل (register.html)**
- إنشاء حساب جديد
- التحقق من صحة البيانات
- تأكيد كلمة المرور

## 🎯 **بيانات تجريبية**

### ✅ **للاختبار (عند توفر قاعدة البيانات):**
```
المدير:
البريد: <EMAIL>
كلمة المرور: password

المستخدم:
البريد: <EMAIL>
كلمة المرور: password
```

## 🛠️ **استكشاف الأخطاء**

### ❌ **إذا كانت الصفحة تتحرك:**
1. تأكد من تحميل `assets/js/stability.js`
2. افتح Developer Tools (F12)
3. تحقق من وجود أخطاء في Console
4. جرب صفحة `test-stable.html`

### ❌ **إذا لم تظهر الصور:**
1. تأكد من وجود مجلد `assets/images/`
2. تحقق من مسارات الصور في HTML
3. استخدم صور بديلة إذا لزم الأمر

### ❌ **إذا لم تعمل الخطوط العربية:**
1. تأكد من اتصال الإنترنت (للخطوط من Google Fonts)
2. أو حمل الخطوط محلياً في `assets/fonts/`

## 📞 **للمساعدة**

إذا واجهت أي مشكلة:
1. تأكد من فتح `test-stable.html` أولاً
2. تحقق من Developer Tools للأخطاء
3. جرب متصفح آخر (Chrome, Firefox, Edge)
4. تأكد من تحديث المتصفح

---

## 🎉 **تم إصلاح مشكلة عدم الاستقرار!**

الموقع الآن مستقر تماماً ولا يحتوي على حركة مزعجة. يمكنك تصفحه براحة تامة.

**استمتع بتجربة مكتبتي الحرة! 📚✨**
