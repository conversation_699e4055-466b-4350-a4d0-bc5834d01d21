<?php
/**
 * نقطة نهاية الإدارة - مكتبتي الحرة
 * Admin Endpoint - My Free Library
 */

// إعداد الترميز والأخطاء
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// التعامل مع طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// تضمين الملفات المطلوبة
require_once '../database/config.php';
require_once 'classes/Response.php';
require_once 'classes/Security.php';
require_once 'classes/Backup.php';

// تطبيق إجراءات الأمان
SecurityMiddleware::apply();

// التحقق من صلاحيات المدير
$user = Auth::requireAdmin();

// الحصول على المسار والطريقة
$requestUri = $_SERVER['REQUEST_URI'];
$requestMethod = $_SERVER['REQUEST_METHOD'];

$path = parse_url($requestUri, PHP_URL_PATH);
$path = preg_replace('#^/api/admin#', '', $path);
$pathParts = array_filter(explode('/', $path));
$pathParts = array_values($pathParts);

// الحصول على البيانات المرسلة
$inputData = [];
if (in_array($requestMethod, ['POST', 'PUT', 'PATCH'])) {
    $rawInput = file_get_contents('php://input');
    $inputData = json_decode($rawInput, true) ?? [];
    $inputData = array_merge($_POST, $inputData);
}
$inputData = array_merge($_GET, $inputData);

try {
    // توجيه الطلبات
    if (empty($pathParts)) {
        // لوحة التحكم الرئيسية
        handleDashboard();
    } else {
        $endpoint = $pathParts[0];
        
        switch ($endpoint) {
            case 'stats':
                handleStats($pathParts, $requestMethod, $inputData);
                break;
                
            case 'users':
                handleUsers($pathParts, $requestMethod, $inputData);
                break;
                
            case 'books':
                handleBooksManagement($pathParts, $requestMethod, $inputData);
                break;
                
            case 'security':
                handleSecurity($pathParts, $requestMethod, $inputData);
                break;
                
            case 'backup':
                handleBackup($pathParts, $requestMethod, $inputData);
                break;
                
            case 'settings':
                handleSettings($pathParts, $requestMethod, $inputData);
                break;
                
            default:
                Response::notFound('نقطة النهاية غير موجودة');
        }
    }
    
} catch (Exception $e) {
    if (DEBUG_MODE) {
        Response::serverError('خطأ: ' . $e->getMessage());
    } else {
        Response::serverError('حدث خطأ في الخادم');
    }
}

/**
 * لوحة التحكم الرئيسية
 */
function handleDashboard() {
    $db = getDB();
    
    $dashboard = [];
    
    // إحصائيات عامة
    $dashboard['stats'] = [
        'total_books' => $db->fetch("SELECT COUNT(*) as count FROM books WHERE status = 'active'")['count'],
        'total_users' => $db->fetch("SELECT COUNT(*) as count FROM users WHERE status = 'active'")['count'],
        'total_downloads' => $db->fetch("SELECT COUNT(*) as count FROM downloads")['count'],
        'total_views' => $db->fetch("SELECT SUM(views_count) as total FROM books")['total'] ?? 0
    ];
    
    // إحصائيات اليوم
    $dashboard['today_stats'] = [
        'new_users' => $db->fetch("SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = CURDATE()")['count'],
        'downloads' => $db->fetch("SELECT COUNT(*) as count FROM downloads WHERE DATE(downloaded_at) = CURDATE()")['count'],
        'views' => $db->fetch("SELECT COUNT(*) as count FROM book_views WHERE DATE(viewed_at) = CURDATE()")['count']
    ];
    
    // أكثر الكتب تحميلاً
    $dashboard['top_books'] = $db->fetchAll(
        "SELECT title, author, downloads_count FROM books 
         WHERE status = 'active' 
         ORDER BY downloads_count DESC 
         LIMIT 5"
    );
    
    // آخر المستخدمين المسجلين
    $dashboard['recent_users'] = $db->fetchAll(
        "SELECT name, email, created_at FROM users 
         WHERE status = 'active' 
         ORDER BY created_at DESC 
         LIMIT 5"
    );
    
    // الأنشطة الأخيرة
    $dashboard['recent_activities'] = $db->fetchAll(
        "SELECT al.*, u.name as user_name 
         FROM activity_logs al 
         LEFT JOIN users u ON al.user_id = u.id 
         ORDER BY al.created_at DESC 
         LIMIT 10"
    );
    
    Response::success($dashboard);
}

/**
 * معالجة الإحصائيات
 */
function handleStats($pathParts, $method, $data) {
    if ($method !== 'GET') {
        Response::error('طريقة غير مدعومة', 405);
    }
    
    $db = getDB();
    $type = $pathParts[1] ?? 'general';
    
    switch ($type) {
        case 'general':
            $stats = getGeneralStats($db);
            break;
            
        case 'books':
            $stats = getBooksStats($db);
            break;
            
        case 'users':
            $stats = getUsersStats($db);
            break;
            
        case 'downloads':
            $stats = getDownloadsStats($db);
            break;
            
        default:
            Response::error('نوع إحصائيات غير مدعوم', 400);
    }
    
    Response::success($stats);
}

/**
 * معالجة إدارة المستخدمين
 */
function handleUsers($pathParts, $method, $data) {
    $db = getDB();
    
    if (count($pathParts) === 1) {
        // /admin/users
        if ($method === 'GET') {
            $page = (int)($data['page'] ?? 1);
            $limit = min(50, (int)($data['limit'] ?? 20));
            $search = $data['search'] ?? '';
            
            $whereConditions = ["1=1"];
            $queryParams = [];
            
            if ($search) {
                $whereConditions[] = "(name LIKE ? OR email LIKE ?)";
                $searchTerm = "%$search%";
                $queryParams = [$searchTerm, $searchTerm];
            }
            
            $whereClause = implode(' AND ', $whereConditions);
            
            // حساب العدد الكلي
            $total = $db->fetch("SELECT COUNT(*) as total FROM users WHERE $whereClause", $queryParams)['total'];
            
            // إنشاء الترقيم
            $paginator = new Paginator($page, $limit, $total);
            
            // الحصول على المستخدمين
            $users = $db->fetchAll(
                "SELECT id, name, email, role, status, created_at, last_login 
                 FROM users 
                 WHERE $whereClause 
                 ORDER BY created_at DESC 
                 LIMIT ? OFFSET ?",
                array_merge($queryParams, [$paginator->getLimit(), $paginator->getOffset()])
            );
            
            Response::paginated($users, $paginator->getPaginationInfo());
        }
    } elseif (count($pathParts) === 2) {
        $userId = (int)$pathParts[1];
        
        if ($method === 'GET') {
            // الحصول على تفاصيل المستخدم
            $user = $db->fetch(
                "SELECT * FROM users WHERE id = ?",
                [$userId]
            );
            
            if (!$user) {
                Response::notFound('المستخدم غير موجود');
            }
            
            unset($user['password_hash']);
            Response::success($user);
            
        } elseif ($method === 'PUT') {
            // تحديث المستخدم
            $allowedFields = ['name', 'email', 'role', 'status'];
            $updateFields = [];
            $updateValues = [];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateFields[] = "$field = ?";
                    $updateValues[] = $data[$field];
                }
            }
            
            if (empty($updateFields)) {
                Response::error('لا توجد بيانات للتحديث', 400);
            }
            
            $updateValues[] = $userId;
            
            $db->update(
                "UPDATE users SET " . implode(', ', $updateFields) . ", updated_at = NOW() WHERE id = ?",
                $updateValues
            );
            
            Response::success(null, 'تم تحديث المستخدم بنجاح');
            
        } elseif ($method === 'DELETE') {
            // حذف المستخدم (حذف ناعم)
            $db->update(
                "UPDATE users SET status = 'deleted', updated_at = NOW() WHERE id = ?",
                [$userId]
            );
            
            Response::success(null, 'تم حذف المستخدم بنجاح');
        }
    }
}

/**
 * معالجة الأمان
 */
function handleSecurity($pathParts, $method, $data) {
    if ($method !== 'GET') {
        Response::error('طريقة غير مدعومة', 405);
    }
    
    $type = $pathParts[1] ?? 'report';
    
    switch ($type) {
        case 'report':
            $days = (int)($data['days'] ?? 7);
            $report = Security::generateSecurityReport($days);
            Response::success($report);
            break;
            
        case 'logs':
            $page = (int)($data['page'] ?? 1);
            $limit = min(100, (int)($data['limit'] ?? 50));
            
            $db = getDB();
            $total = $db->fetch("SELECT COUNT(*) as total FROM security_logs")['total'];
            $paginator = new Paginator($page, $limit, $total);
            
            $logs = $db->fetchAll(
                "SELECT * FROM security_logs 
                 ORDER BY created_at DESC 
                 LIMIT ? OFFSET ?",
                [$paginator->getLimit(), $paginator->getOffset()]
            );
            
            Response::paginated($logs, $paginator->getPaginationInfo());
            break;
            
        case 'cleanup':
            Security::cleanupOldData();
            Response::success(null, 'تم تنظيف البيانات القديمة بنجاح');
            break;
            
        default:
            Response::error('نوع تقرير أمني غير مدعوم', 400);
    }
}

/**
 * معالجة النسخ الاحتياطية
 */
function handleBackup($pathParts, $method, $data) {
    global $user;
    $backup = new Backup();
    
    if (count($pathParts) === 1) {
        if ($method === 'GET') {
            // قائمة النسخ الاحتياطية
            $page = (int)($data['page'] ?? 1);
            $limit = min(50, (int)($data['limit'] ?? 20));
            
            $result = $backup->getBackupsList($page, $limit);
            Response::paginated($result['backups'], $result['pagination']);
            
        } elseif ($method === 'POST') {
            // إنشاء نسخة احتياطية جديدة
            $type = $data['type'] ?? 'full';
            
            if ($type === 'full') {
                $result = $backup->createFullBackup($user['id']);
            } elseif ($type === 'incremental') {
                $result = $backup->createIncrementalBackup($user['id']);
            } else {
                Response::error('نوع النسخة الاحتياطية غير مدعوم', 400);
            }
            
            Response::success($result, 'تم إنشاء النسخة الاحتياطية بنجاح', 201);
        }
    } elseif (count($pathParts) === 2) {
        $backupId = (int)$pathParts[1];
        
        if ($method === 'DELETE') {
            // حذف نسخة احتياطية
            $backup->deleteBackup($backupId, $user['id']);
            Response::success(null, 'تم حذف النسخة الاحتياطية بنجاح');
            
        } elseif ($method === 'POST' && isset($data['action']) && $data['action'] === 'restore') {
            // استعادة نسخة احتياطية
            $backup->restoreBackup($backupId, $user['id']);
            Response::success(null, 'تم استعادة النسخة الاحتياطية بنجاح');
        }
    }
}

/**
 * وظائف مساعدة للإحصائيات
 */
function getGeneralStats($db) {
    return [
        'total_books' => $db->fetch("SELECT COUNT(*) as count FROM books WHERE status = 'active'")['count'],
        'total_users' => $db->fetch("SELECT COUNT(*) as count FROM users WHERE status = 'active'")['count'],
        'total_downloads' => $db->fetch("SELECT COUNT(*) as count FROM downloads")['count'],
        'total_categories' => $db->fetch("SELECT COUNT(*) as count FROM categories WHERE is_active = 1")['count'],
        'disk_usage' => getDiskUsage()
    ];
}

function getBooksStats($db) {
    return [
        'by_category' => $db->fetchAll(
            "SELECT c.name, COUNT(b.id) as count 
             FROM categories c 
             LEFT JOIN books b ON c.id = b.category_id AND b.status = 'active'
             WHERE c.is_active = 1 
             GROUP BY c.id, c.name 
             ORDER BY count DESC"
        ),
        'by_month' => $db->fetchAll(
            "SELECT DATE_FORMAT(created_at, '%Y-%m') as month, COUNT(*) as count 
             FROM books 
             WHERE status = 'active' AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
             GROUP BY DATE_FORMAT(created_at, '%Y-%m') 
             ORDER BY month ASC"
        )
    ];
}

function getUsersStats($db) {
    return [
        'by_role' => $db->fetchAll(
            "SELECT role, COUNT(*) as count 
             FROM users 
             WHERE status = 'active' 
             GROUP BY role"
        ),
        'registrations_by_month' => $db->fetchAll(
            "SELECT DATE_FORMAT(created_at, '%Y-%m') as month, COUNT(*) as count 
             FROM users 
             WHERE status = 'active' AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
             GROUP BY DATE_FORMAT(created_at, '%Y-%m') 
             ORDER BY month ASC"
        )
    ];
}

function getDownloadsStats($db) {
    return [
        'by_month' => $db->fetchAll(
            "SELECT DATE_FORMAT(downloaded_at, '%Y-%m') as month, COUNT(*) as count 
             FROM downloads 
             WHERE downloaded_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
             GROUP BY DATE_FORMAT(downloaded_at, '%Y-%m') 
             ORDER BY month ASC"
        ),
        'top_books' => $db->fetchAll(
            "SELECT b.title, b.author, COUNT(d.id) as downloads 
             FROM books b 
             INNER JOIN downloads d ON b.id = d.book_id 
             WHERE b.status = 'active'
             GROUP BY b.id 
             ORDER BY downloads DESC 
             LIMIT 10"
        )
    ];
}

function getDiskUsage() {
    $uploadPath = UPLOAD_PATH;
    $coversPath = COVERS_PATH;
    
    $uploadSize = 0;
    $coversSize = 0;
    
    if (is_dir($uploadPath)) {
        $uploadSize = getDirSize($uploadPath);
    }
    
    if (is_dir($coversPath)) {
        $coversSize = getDirSize($coversPath);
    }
    
    return [
        'uploads' => formatFileSize($uploadSize),
        'covers' => formatFileSize($coversSize),
        'total' => formatFileSize($uploadSize + $coversSize)
    ];
}

function getDirSize($dir) {
    $size = 0;
    $files = glob($dir . '/*');
    
    foreach ($files as $file) {
        if (is_file($file)) {
            $size += filesize($file);
        } elseif (is_dir($file)) {
            $size += getDirSize($file);
        }
    }
    
    return $size;
}
?>
