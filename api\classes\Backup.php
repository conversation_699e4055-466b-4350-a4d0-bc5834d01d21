<?php
/**
 * فئة النسخ الاحتياطي - مكتبتي الحرة
 * Backup Class - My Free Library
 */

require_once '../database/config.php';
require_once 'Response.php';

class Backup {
    private $db;
    private $backupPath;
    
    public function __construct() {
        $this->db = getDB();
        $this->backupPath = '../backups/';
        
        // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
        if (!is_dir($this->backupPath)) {
            mkdir($this->backupPath, 0755, true);
        }
    }
    
    /**
     * إنشاء نسخة احتياطية كاملة
     */
    public function createFullBackup($userId = null) {
        try {
            $backupId = $this->initializeBackup('full', $userId);
            
            $timestamp = date('Y-m-d_H-i-s');
            $filename = "full_backup_{$timestamp}.sql";
            $filePath = $this->backupPath . $filename;
            
            // تحديث حالة النسخة الاحتياطية
            $this->updateBackupStatus($backupId, 'running');
            
            // إنشاء النسخة الاحتياطية
            $this->createDatabaseBackup($filePath);
            
            // ضغط الملف
            $compressedFile = $this->compressBackup($filePath);
            
            // حساب حجم الملف
            $fileSize = filesize($compressedFile);
            
            // تحديث معلومات النسخة الاحتياطية
            $this->db->update(
                "UPDATE backups SET file_path = ?, file_size = ?, status = 'completed', completed_at = NOW() WHERE id = ?",
                [basename($compressedFile), $fileSize, $backupId]
            );
            
            // حذف الملف غير المضغوط
            if (file_exists($filePath)) {
                unlink($filePath);
            }
            
            // تنظيف النسخ القديمة
            $this->cleanupOldBackups();
            
            return [
                'backup_id' => $backupId,
                'filename' => basename($compressedFile),
                'size' => $fileSize,
                'size_formatted' => formatFileSize($fileSize)
            ];
            
        } catch (Exception $e) {
            if (isset($backupId)) {
                $this->updateBackupStatus($backupId, 'failed');
            }
            throw $e;
        }
    }
    
    /**
     * إنشاء نسخة احتياطية تدريجية
     */
    public function createIncrementalBackup($userId = null) {
        try {
            $backupId = $this->initializeBackup('incremental', $userId);
            
            // الحصول على آخر نسخة احتياطية
            $lastBackup = $this->getLastBackup();
            $lastBackupDate = $lastBackup ? $lastBackup['started_at'] : '1970-01-01 00:00:00';
            
            $timestamp = date('Y-m-d_H-i-s');
            $filename = "incremental_backup_{$timestamp}.sql";
            $filePath = $this->backupPath . $filename;
            
            $this->updateBackupStatus($backupId, 'running');
            
            // إنشاء النسخة التدريجية
            $this->createIncrementalDatabaseBackup($filePath, $lastBackupDate);
            
            $compressedFile = $this->compressBackup($filePath);
            $fileSize = filesize($compressedFile);
            
            $this->db->update(
                "UPDATE backups SET file_path = ?, file_size = ?, status = 'completed', completed_at = NOW() WHERE id = ?",
                [basename($compressedFile), $fileSize, $backupId]
            );
            
            if (file_exists($filePath)) {
                unlink($filePath);
            }
            
            return [
                'backup_id' => $backupId,
                'filename' => basename($compressedFile),
                'size' => $fileSize,
                'size_formatted' => formatFileSize($fileSize)
            ];
            
        } catch (Exception $e) {
            if (isset($backupId)) {
                $this->updateBackupStatus($backupId, 'failed');
            }
            throw $e;
        }
    }
    
    /**
     * استعادة نسخة احتياطية
     */
    public function restoreBackup($backupId, $userId = null) {
        $user = Auth::requireAdmin();
        
        $backup = $this->db->fetch(
            "SELECT * FROM backups WHERE id = ? AND status = 'completed'",
            [$backupId]
        );
        
        if (!$backup) {
            throw new Exception('النسخة الاحتياطية غير موجودة');
        }
        
        $filePath = $this->backupPath . $backup['file_path'];
        
        if (!file_exists($filePath)) {
            throw new Exception('ملف النسخة الاحتياطية غير موجود');
        }
        
        try {
            // فك ضغط الملف
            $extractedFile = $this->extractBackup($filePath);
            
            // استعادة قاعدة البيانات
            $this->restoreDatabase($extractedFile);
            
            // تسجيل العملية
            $this->db->insert(
                "INSERT INTO activity_logs (user_id, action, entity_type, entity_id, details, ip_address, user_agent) 
                 VALUES (?, ?, ?, ?, ?, ?, ?)",
                [
                    $user['id'],
                    'backup_restored',
                    'backups',
                    $backupId,
                    json_encode(['backup_file' => $backup['file_path']]),
                    getClientIP(),
                    getUserAgent()
                ]
            );
            
            // حذف الملف المستخرج
            if (file_exists($extractedFile)) {
                unlink($extractedFile);
            }
            
            return true;
            
        } catch (Exception $e) {
            throw new Exception('فشل في استعادة النسخة الاحتياطية: ' . $e->getMessage());
        }
    }
    
    /**
     * الحصول على قائمة النسخ الاحتياطية
     */
    public function getBackupsList($page = 1, $limit = 20) {
        $paginator = new Paginator($page, $limit);
        
        // حساب العدد الكلي
        $total = $this->db->fetch("SELECT COUNT(*) as total FROM backups")['total'];
        $paginator = new Paginator($page, $limit, $total);
        
        // الحصول على النسخ
        $backups = $this->db->fetchAll(
            "SELECT b.*, u.name as created_by_name 
             FROM backups b 
             LEFT JOIN users u ON b.created_by = u.id 
             ORDER BY b.started_at DESC 
             LIMIT ? OFFSET ?",
            [$paginator->getLimit(), $paginator->getOffset()]
        );
        
        // تنسيق البيانات
        foreach ($backups as &$backup) {
            $backup['file_size_formatted'] = formatFileSize($backup['file_size']);
            $backup['duration'] = $this->calculateDuration($backup['started_at'], $backup['completed_at']);
        }
        
        return [
            'backups' => $backups,
            'pagination' => $paginator->getPaginationInfo()
        ];
    }
    
    /**
     * حذف نسخة احتياطية
     */
    public function deleteBackup($backupId, $userId = null) {
        $user = Auth::requireAdmin();
        
        $backup = $this->db->fetch(
            "SELECT * FROM backups WHERE id = ?",
            [$backupId]
        );
        
        if (!$backup) {
            throw new Exception('النسخة الاحتياطية غير موجودة');
        }
        
        // حذف الملف
        $filePath = $this->backupPath . $backup['file_path'];
        if (file_exists($filePath)) {
            unlink($filePath);
        }
        
        // حذف السجل من قاعدة البيانات
        $this->db->delete("DELETE FROM backups WHERE id = ?", [$backupId]);
        
        // تسجيل العملية
        $this->db->insert(
            "INSERT INTO activity_logs (user_id, action, entity_type, entity_id, details, ip_address, user_agent) 
             VALUES (?, ?, ?, ?, ?, ?, ?)",
            [
                $user['id'],
                'backup_deleted',
                'backups',
                $backupId,
                json_encode(['backup_file' => $backup['file_path']]),
                getClientIP(),
                getUserAgent()
            ]
        );
        
        return true;
    }
    
    /**
     * وظائف مساعدة
     */
    
    private function initializeBackup($type, $userId) {
        return $this->db->insert(
            "INSERT INTO backups (backup_type, file_path, status, created_by) VALUES (?, '', 'pending', ?)",
            [$type, $userId]
        );
    }
    
    private function updateBackupStatus($backupId, $status) {
        $this->db->update(
            "UPDATE backups SET status = ? WHERE id = ?",
            [$status, $backupId]
        );
    }
    
    private function createDatabaseBackup($filePath) {
        $command = sprintf(
            'mysqldump --host=%s --user=%s --password=%s --single-transaction --routines --triggers %s > %s',
            DB_HOST,
            DB_USER,
            DB_PASS,
            DB_NAME,
            escapeshellarg($filePath)
        );
        
        exec($command, $output, $returnCode);
        
        if ($returnCode !== 0) {
            throw new Exception('فشل في إنشاء النسخة الاحتياطية');
        }
    }
    
    private function createIncrementalDatabaseBackup($filePath, $lastBackupDate) {
        // إنشاء نسخة احتياطية للبيانات المحدثة فقط
        $tables = ['books', 'users', 'book_ratings', 'downloads', 'activity_logs'];
        
        $sql = "-- Incremental Backup from {$lastBackupDate}\n\n";
        
        foreach ($tables as $table) {
            $sql .= "-- Table: {$table}\n";
            
            $rows = $this->db->fetchAll(
                "SELECT * FROM {$table} WHERE updated_at > ? OR created_at > ?",
                [$lastBackupDate, $lastBackupDate]
            );
            
            if (!empty($rows)) {
                $sql .= "DELETE FROM {$table} WHERE ";
                $conditions = [];
                
                foreach ($rows as $row) {
                    $conditions[] = "id = {$row['id']}";
                }
                
                $sql .= implode(' OR ', $conditions) . ";\n";
                
                foreach ($rows as $row) {
                    $columns = array_keys($row);
                    $values = array_map(function($value) {
                        return $value === null ? 'NULL' : "'" . addslashes($value) . "'";
                    }, array_values($row));
                    
                    $sql .= "INSERT INTO {$table} (" . implode(', ', $columns) . ") VALUES (" . implode(', ', $values) . ");\n";
                }
            }
            
            $sql .= "\n";
        }
        
        file_put_contents($filePath, $sql);
    }
    
    private function compressBackup($filePath) {
        $compressedFile = $filePath . '.gz';
        
        $command = sprintf('gzip -c %s > %s', escapeshellarg($filePath), escapeshellarg($compressedFile));
        exec($command, $output, $returnCode);
        
        if ($returnCode !== 0) {
            throw new Exception('فشل في ضغط النسخة الاحتياطية');
        }
        
        return $compressedFile;
    }
    
    private function extractBackup($filePath) {
        $extractedFile = str_replace('.gz', '', $filePath);
        
        $command = sprintf('gunzip -c %s > %s', escapeshellarg($filePath), escapeshellarg($extractedFile));
        exec($command, $output, $returnCode);
        
        if ($returnCode !== 0) {
            throw new Exception('فشل في فك ضغط النسخة الاحتياطية');
        }
        
        return $extractedFile;
    }
    
    private function restoreDatabase($filePath) {
        $command = sprintf(
            'mysql --host=%s --user=%s --password=%s %s < %s',
            DB_HOST,
            DB_USER,
            DB_PASS,
            DB_NAME,
            escapeshellarg($filePath)
        );
        
        exec($command, $output, $returnCode);
        
        if ($returnCode !== 0) {
            throw new Exception('فشل في استعادة قاعدة البيانات');
        }
    }
    
    private function getLastBackup() {
        return $this->db->fetch(
            "SELECT * FROM backups WHERE status = 'completed' ORDER BY started_at DESC LIMIT 1"
        );
    }
    
    private function cleanupOldBackups($keepCount = 10) {
        $oldBackups = $this->db->fetchAll(
            "SELECT * FROM backups WHERE status = 'completed' ORDER BY started_at DESC LIMIT 999 OFFSET ?",
            [$keepCount]
        );
        
        foreach ($oldBackups as $backup) {
            $filePath = $this->backupPath . $backup['file_path'];
            if (file_exists($filePath)) {
                unlink($filePath);
            }
            
            $this->db->delete("DELETE FROM backups WHERE id = ?", [$backup['id']]);
        }
    }
    
    private function calculateDuration($startTime, $endTime) {
        if (!$endTime) return null;
        
        $start = new DateTime($startTime);
        $end = new DateTime($endTime);
        $interval = $start->diff($end);
        
        return $interval->format('%H:%I:%S');
    }
}
?>
