<?php
/**
 * فئة الكتاب - مكتبتي الحرة
 * Book Class - My Free Library
 */

require_once '../database/config.php';
require_once 'Response.php';

class Book {
    private $db;
    
    public function __construct() {
        $this->db = getDB();
    }
    
    /**
     * الحصول على جميع الكتب مع الفلترة والبحث
     */
    public function getBooks($params = []) {
        $page = isset($params['page']) ? (int)$params['page'] : 1;
        $limit = isset($params['limit']) ? min(50, (int)$params['limit']) : 12;
        $search = isset($params['search']) ? trim($params['search']) : '';
        $category = isset($params['category']) ? (int)$params['category'] : null;
        $sort = isset($params['sort']) ? $params['sort'] : 'newest';
        $featured = isset($params['featured']) ? (bool)$params['featured'] : null;
        
        // بناء الاستعلام
        $whereConditions = ["b.status = 'active'"];
        $queryParams = [];
        
        // البحث
        if ($search) {
            $whereConditions[] = "(b.title LIKE ? OR b.author LIKE ? OR b.description LIKE ?)";
            $searchTerm = "%$search%";
            $queryParams = array_merge($queryParams, [$searchTerm, $searchTerm, $searchTerm]);
        }
        
        // التصنيف
        if ($category) {
            $whereConditions[] = "b.category_id = ?";
            $queryParams[] = $category;
        }
        
        // المميزة
        if ($featured !== null) {
            $whereConditions[] = "b.is_featured = ?";
            $queryParams[] = $featured ? 1 : 0;
        }
        
        $whereClause = implode(' AND ', $whereConditions);
        
        // ترتيب النتائج
        $orderBy = $this->getOrderByClause($sort);
        
        // حساب العدد الكلي
        $totalQuery = "SELECT COUNT(*) as total 
                       FROM books b 
                       INNER JOIN categories c ON b.category_id = c.id 
                       WHERE $whereClause";
        
        $total = $this->db->fetch($totalQuery, $queryParams)['total'];
        
        // إنشاء الترقيم
        $paginator = new Paginator($page, $limit, $total);
        
        // الاستعلام الرئيسي
        $query = "SELECT b.*, c.name as category_name, c.slug as category_slug,
                         u.name as added_by_name
                  FROM books b 
                  INNER JOIN categories c ON b.category_id = c.id 
                  INNER JOIN users u ON b.added_by = u.id
                  WHERE $whereClause 
                  ORDER BY $orderBy 
                  LIMIT ? OFFSET ?";
        
        $queryParams[] = $paginator->getLimit();
        $queryParams[] = $paginator->getOffset();
        
        $books = $this->db->fetchAll($query, $queryParams);
        
        // تنسيق البيانات
        $formattedBooks = array_map([$this, 'formatBookData'], $books);
        
        Response::paginated($formattedBooks, $paginator->getPaginationInfo());
    }
    
    /**
     * الحصول على كتاب واحد
     */
    public function getBook($id) {
        $book = $this->db->fetch(
            "SELECT b.*, c.name as category_name, c.slug as category_slug,
                    u.name as added_by_name
             FROM books b 
             INNER JOIN categories c ON b.category_id = c.id 
             INNER JOIN users u ON b.added_by = u.id
             WHERE b.id = ? AND b.status = 'active'",
            [$id]
        );
        
        if (!$book) {
            Response::notFound('الكتاب غير موجود');
        }
        
        // زيادة عدد المشاهدات
        $this->incrementViews($id);
        
        // الحصول على التقييمات
        $ratings = $this->getBookRatings($id);
        
        // الحصول على الكتب المشابهة
        $relatedBooks = $this->getRelatedBooks($id, $book['category_id']);
        
        $bookData = $this->formatBookData($book);
        $bookData['ratings'] = $ratings;
        $bookData['related_books'] = $relatedBooks;
        
        Response::success($bookData);
    }
    
    /**
     * إضافة كتاب جديد (للمديرين فقط)
     */
    public function addBook($data, $files = []) {
        $user = Auth::requireAdmin();
        
        // التحقق من صحة البيانات
        $validator = new Validator($data);
        $validator->required('title', 'عنوان الكتاب مطلوب')
                 ->required('author', 'اسم المؤلف مطلوب')
                 ->required('category_id', 'التصنيف مطلوب')
                 ->integer('category_id', 'التصنيف غير صحيح')
                 ->required('description', 'وصف الكتاب مطلوب');
        
        if (isset($data['publish_year'])) {
            $validator->integer('publish_year', 'سنة النشر غير صحيحة')
                     ->min('publish_year', 1000, 'سنة النشر غير صحيحة')
                     ->max('publish_year', date('Y'), 'سنة النشر لا يمكن أن تكون في المستقبل');
        }
        
        if ($validator->hasErrors()) {
            Response::validationError($validator->getErrors());
        }
        
        $cleanData = $validator->getData();
        
        // التحقق من وجود التصنيف
        $category = $this->db->fetch(
            "SELECT id FROM categories WHERE id = ? AND is_active = 1",
            [$cleanData['category_id']]
        );
        
        if (!$category) {
            Response::error('التصنيف غير موجود', 400);
        }
        
        try {
            $this->db->beginTransaction();
            
            // إدراج الكتاب
            $bookId = $this->db->insert(
                "INSERT INTO books (title, author, isbn, description, category_id, 
                                   publish_year, publisher, language, added_by, created_at) 
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())",
                [
                    $cleanData['title'],
                    $cleanData['author'],
                    $cleanData['isbn'] ?? null,
                    $cleanData['description'],
                    $cleanData['category_id'],
                    $cleanData['publish_year'] ?? null,
                    $cleanData['publisher'] ?? null,
                    $cleanData['language'] ?? 'ar',
                    $user['id']
                ]
            );
            
            $this->db->commit();
            
            // تسجيل النشاط
            $this->logActivity($user['id'], 'book_added', 'books', $bookId);
            
            // الحصول على بيانات الكتاب الجديد
            $newBook = $this->getBookById($bookId);
            
            Response::success([
                'book' => $this->formatBookData($newBook)
            ], 'تم إضافة الكتاب بنجاح', 201);
            
        } catch (Exception $e) {
            $this->db->rollback();
            Response::serverError('خطأ في إضافة الكتاب');
        }
    }
    
    /**
     * تحديث كتاب (للمديرين فقط)
     */
    public function updateBook($id, $data) {
        $user = Auth::requireAdmin();
        
        // التحقق من وجود الكتاب
        $book = $this->getBookById($id);
        if (!$book) {
            Response::notFound('الكتاب غير موجود');
        }
        
        // التحقق من صحة البيانات
        $validator = new Validator($data);
        if (isset($data['category_id'])) {
            $validator->integer('category_id', 'التصنيف غير صحيح');
        }
        if (isset($data['publish_year'])) {
            $validator->integer('publish_year', 'سنة النشر غير صحيحة')
                     ->min('publish_year', 1000, 'سنة النشر غير صحيحة')
                     ->max('publish_year', date('Y'), 'سنة النشر لا يمكن أن تكون في المستقبل');
        }
        
        if ($validator->hasErrors()) {
            Response::validationError($validator->getErrors());
        }
        
        $cleanData = $validator->getData();
        
        try {
            // بناء استعلام التحديث
            $updateFields = [];
            $updateValues = [];
            
            $allowedFields = ['title', 'author', 'isbn', 'description', 'category_id', 
                             'publish_year', 'publisher', 'language', 'is_featured', 'is_new'];
            
            foreach ($allowedFields as $field) {
                if (isset($cleanData[$field])) {
                    $updateFields[] = "$field = ?";
                    $updateValues[] = $cleanData[$field];
                }
            }
            
            if (empty($updateFields)) {
                Response::error('لا توجد بيانات للتحديث', 400);
            }
            
            $updateValues[] = $id;
            
            $this->db->update(
                "UPDATE books SET " . implode(', ', $updateFields) . ", updated_at = NOW() WHERE id = ?",
                $updateValues
            );
            
            // تسجيل النشاط
            $this->logActivity($user['id'], 'book_updated', 'books', $id);
            
            // الحصول على البيانات المحدثة
            $updatedBook = $this->getBookById($id);
            
            Response::success([
                'book' => $this->formatBookData($updatedBook)
            ], 'تم تحديث الكتاب بنجاح');
            
        } catch (Exception $e) {
            Response::serverError('خطأ في تحديث الكتاب');
        }
    }
    
    /**
     * حذف كتاب (للمديرين فقط)
     */
    public function deleteBook($id) {
        $user = Auth::requireAdmin();
        
        // التحقق من وجود الكتاب
        $book = $this->getBookById($id);
        if (!$book) {
            Response::notFound('الكتاب غير موجود');
        }
        
        try {
            // حذف ناعم
            $this->db->update(
                "UPDATE books SET status = 'deleted', updated_at = NOW() WHERE id = ?",
                [$id]
            );
            
            // تسجيل النشاط
            $this->logActivity($user['id'], 'book_deleted', 'books', $id);
            
            Response::success(null, 'تم حذف الكتاب بنجاح');
            
        } catch (Exception $e) {
            Response::serverError('خطأ في حذف الكتاب');
        }
    }
    
    /**
     * تقييم كتاب
     */
    public function rateBook($bookId, $data) {
        $user = Auth::requireAuth();
        
        // التحقق من وجود الكتاب
        $book = $this->getBookById($bookId);
        if (!$book) {
            Response::notFound('الكتاب غير موجود');
        }
        
        // التحقق من صحة البيانات
        $validator = new Validator($data);
        $validator->required('rating', 'التقييم مطلوب')
                 ->integer('rating', 'التقييم يجب أن يكون رقم')
                 ->min('rating', 1, 'التقييم يجب أن يكون من 1 إلى 5')
                 ->max('rating', 5, 'التقييم يجب أن يكون من 1 إلى 5');
        
        if ($validator->hasErrors()) {
            Response::validationError($validator->getErrors());
        }
        
        $cleanData = $validator->getData();
        
        try {
            // التحقق من وجود تقييم سابق
            $existingRating = $this->db->fetch(
                "SELECT id FROM book_ratings WHERE book_id = ? AND user_id = ?",
                [$bookId, $user['id']]
            );
            
            if ($existingRating) {
                // تحديث التقييم الموجود
                $this->db->update(
                    "UPDATE book_ratings SET rating = ?, review = ?, updated_at = NOW() 
                     WHERE book_id = ? AND user_id = ?",
                    [$cleanData['rating'], $cleanData['review'] ?? null, $bookId, $user['id']]
                );
            } else {
                // إضافة تقييم جديد
                $this->db->insert(
                    "INSERT INTO book_ratings (book_id, user_id, rating, review) 
                     VALUES (?, ?, ?, ?)",
                    [$bookId, $user['id'], $cleanData['rating'], $cleanData['review'] ?? null]
                );
            }
            
            // تحديث متوسط التقييم للكتاب
            $this->updateBookRating($bookId);
            
            // تسجيل النشاط
            $this->logActivity($user['id'], 'book_rated', 'books', $bookId);
            
            Response::success(null, 'تم إضافة التقييم بنجاح');
            
        } catch (Exception $e) {
            Response::serverError('خطأ في إضافة التقييم');
        }
    }
    
    /**
     * تحميل كتاب
     */
    public function downloadBook($id) {
        $user = Auth::requireAuth();
        
        // التحقق من وجود الكتاب
        $book = $this->getBookById($id);
        if (!$book || $book['status'] !== 'active') {
            Response::notFound('الكتاب غير موجود');
        }
        
        try {
            // تسجيل التحميل
            $this->db->insert(
                "INSERT INTO downloads (user_id, book_id, ip_address, user_agent) 
                 VALUES (?, ?, ?, ?)",
                [$user['id'], $id, getClientIP(), getUserAgent()]
            );
            
            // زيادة عداد التحميلات
            $this->db->update(
                "UPDATE books SET downloads_count = downloads_count + 1 WHERE id = ?",
                [$id]
            );
            
            // تسجيل النشاط
            $this->logActivity($user['id'], 'book_downloaded', 'books', $id);
            
            Response::success([
                'download_url' => SITE_URL . '/public/uploads/' . $book['file_path'],
                'filename' => $book['title'] . '.pdf'
            ], 'رابط التحميل جاهز');
            
        } catch (Exception $e) {
            Response::serverError('خطأ في تحميل الكتاب');
        }
    }
    
    /**
     * وظائف مساعدة
     */
    
    private function getBookById($id) {
        return $this->db->fetch(
            "SELECT b.*, c.name as category_name, c.slug as category_slug
             FROM books b 
             INNER JOIN categories c ON b.category_id = c.id
             WHERE b.id = ?",
            [$id]
        );
    }
    
    private function formatBookData($book) {
        $book['cover_url'] = $book['cover_image'] ? 
            SITE_URL . '/public/covers/' . $book['cover_image'] : null;
        $book['file_size_formatted'] = formatFileSize($book['file_size']);
        return $book;
    }
    
    private function getOrderByClause($sort) {
        switch ($sort) {
            case 'popular':
                return 'b.downloads_count DESC, b.views_count DESC';
            case 'rating':
                return 'b.rating DESC, b.downloads_count DESC';
            case 'alphabetical':
                return 'b.title ASC';
            case 'oldest':
                return 'b.created_at ASC';
            default: // newest
                return 'b.created_at DESC';
        }
    }
    
    private function incrementViews($bookId) {
        $this->db->update(
            "UPDATE books SET views_count = views_count + 1 WHERE id = ?",
            [$bookId]
        );
        
        // تسجيل المشاهدة
        $user = Auth::verifyToken();
        $this->db->insert(
            "INSERT INTO book_views (book_id, user_id, ip_address, user_agent) 
             VALUES (?, ?, ?, ?)",
            [$bookId, $user['id'] ?? null, getClientIP(), getUserAgent()]
        );
    }
    
    private function getBookRatings($bookId) {
        return $this->db->fetchAll(
            "SELECT br.*, u.name as user_name 
             FROM book_ratings br 
             INNER JOIN users u ON br.user_id = u.id 
             WHERE br.book_id = ? 
             ORDER BY br.created_at DESC 
             LIMIT 10",
            [$bookId]
        );
    }
    
    private function getRelatedBooks($bookId, $categoryId) {
        return $this->db->fetchAll(
            "SELECT b.*, c.name as category_name 
             FROM books b 
             INNER JOIN categories c ON b.category_id = c.id
             WHERE b.category_id = ? AND b.id != ? AND b.status = 'active'
             ORDER BY b.rating DESC, b.downloads_count DESC 
             LIMIT 4",
            [$categoryId, $bookId]
        );
    }
    
    private function updateBookRating($bookId) {
        $avgRating = $this->db->fetch(
            "SELECT ROUND(AVG(rating), 2) as avg_rating FROM book_ratings WHERE book_id = ?",
            [$bookId]
        )['avg_rating'];
        
        $this->db->update(
            "UPDATE books SET rating = ? WHERE id = ?",
            [$avgRating, $bookId]
        );
    }
    
    private function logActivity($userId, $action, $entityType, $entityId, $details = null) {
        $this->db->insert(
            "INSERT INTO activity_logs (user_id, action, entity_type, entity_id, details, ip_address, user_agent) 
             VALUES (?, ?, ?, ?, ?, ?, ?)",
            [
                $userId,
                $action,
                $entityType,
                $entityId,
                $details ? json_encode($details) : null,
                getClientIP(),
                getUserAgent()
            ]
        );
    }
}
?>
