<?php
/**
 * فئة رفع الملفات - مكتبتي الحرة
 * File Upload Class - My Free Library
 */

require_once '../database/config.php';
require_once 'Response.php';

class FileUpload {
    private $uploadPath;
    private $coversPath;
    private $allowedFileTypes;
    private $allowedImageTypes;
    private $maxFileSize;
    
    public function __construct() {
        $this->uploadPath = UPLOAD_PATH;
        $this->coversPath = COVERS_PATH;
        $this->allowedFileTypes = ALLOWED_FILE_TYPES;
        $this->allowedImageTypes = ALLOWED_IMAGE_TYPES;
        $this->maxFileSize = MAX_FILE_SIZE;
        
        // إنشاء المجلدات إذا لم تكن موجودة
        $this->createDirectories();
    }
    
    /**
     * رفع ملف كتاب (PDF)
     */
    public function uploadBookFile($file) {
        return $this->uploadFile($file, $this->uploadPath, $this->allowedFileTypes, 'book');
    }
    
    /**
     * رفع صورة غلاف
     */
    public function uploadCoverImage($file) {
        return $this->uploadFile($file, $this->coversPath, $this->allowedImageTypes, 'cover');
    }
    
    /**
     * رفع صورة شخصية
     */
    public function uploadAvatar($file) {
        $avatarPath = $this->coversPath . 'avatars/';
        if (!is_dir($avatarPath)) {
            mkdir($avatarPath, 0755, true);
        }
        
        return $this->uploadFile($file, $avatarPath, $this->allowedImageTypes, 'avatar');
    }
    
    /**
     * رفع ملف عام
     */
    private function uploadFile($file, $targetPath, $allowedTypes, $type = 'file') {
        try {
            // التحقق من وجود الملف
            if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
                throw new Exception($this->getUploadErrorMessage($file['error'] ?? UPLOAD_ERR_NO_FILE));
            }
            
            // التحقق من حجم الملف
            if ($file['size'] > $this->maxFileSize) {
                throw new Exception('حجم الملف كبير جداً. الحد الأقصى: ' . formatFileSize($this->maxFileSize));
            }
            
            // التحقق من نوع الملف
            $originalName = $file['name'];
            $extension = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
            
            if (!in_array($extension, $allowedTypes)) {
                throw new Exception('نوع الملف غير مدعوم. الأنواع المدعومة: ' . implode(', ', $allowedTypes));
            }
            
            // التحقق من MIME type
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mimeType = finfo_file($finfo, $file['tmp_name']);
            finfo_close($finfo);
            
            if (!$this->isValidMimeType($mimeType, $extension)) {
                throw new Exception('نوع الملف غير صحيح');
            }
            
            // إنشاء اسم ملف فريد
            $filename = $this->generateUniqueFilename($originalName, $type);
            $targetFile = $targetPath . $filename;
            
            // نقل الملف
            if (!move_uploaded_file($file['tmp_name'], $targetFile)) {
                throw new Exception('فشل في رفع الملف');
            }
            
            // تحسين الصورة إذا كانت صورة
            if (in_array($extension, $this->allowedImageTypes)) {
                $this->optimizeImage($targetFile, $type);
            }
            
            // إنشاء صورة مصغرة للأغلفة
            if ($type === 'cover') {
                $this->createThumbnail($targetFile, $filename);
            }
            
            return [
                'filename' => $filename,
                'original_name' => $originalName,
                'size' => $file['size'],
                'mime_type' => $mimeType,
                'extension' => $extension,
                'path' => $targetFile,
                'url' => $this->getFileUrl($filename, $type)
            ];
            
        } catch (Exception $e) {
            throw new Exception('خطأ في رفع الملف: ' . $e->getMessage());
        }
    }
    
    /**
     * حذف ملف
     */
    public function deleteFile($filename, $type = 'book') {
        try {
            $filePath = $this->getFilePath($filename, $type);
            
            if (file_exists($filePath)) {
                unlink($filePath);
                
                // حذف الصورة المصغرة إذا كانت موجودة
                if ($type === 'cover') {
                    $thumbnailPath = $this->getFilePath('thumb_' . $filename, $type);
                    if (file_exists($thumbnailPath)) {
                        unlink($thumbnailPath);
                    }
                }
                
                return true;
            }
            
            return false;
            
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * الحصول على معلومات الملف
     */
    public function getFileInfo($filename, $type = 'book') {
        $filePath = $this->getFilePath($filename, $type);
        
        if (!file_exists($filePath)) {
            return null;
        }
        
        return [
            'filename' => $filename,
            'size' => filesize($filePath),
            'size_formatted' => formatFileSize(filesize($filePath)),
            'modified' => filemtime($filePath),
            'url' => $this->getFileUrl($filename, $type),
            'exists' => true
        ];
    }
    
    /**
     * التحقق من وجود الملف
     */
    public function fileExists($filename, $type = 'book') {
        $filePath = $this->getFilePath($filename, $type);
        return file_exists($filePath);
    }
    
    /**
     * إنشاء المجلدات المطلوبة
     */
    private function createDirectories() {
        $directories = [
            $this->uploadPath,
            $this->coversPath,
            $this->coversPath . 'avatars/',
            $this->coversPath . 'thumbnails/'
        ];
        
        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
        
        // إنشاء ملف .htaccess لحماية مجلد الرفع
        $htaccessContent = "# حماية ملفات PHP من التنفيذ\n";
        $htaccessContent .= "<Files *.php>\n";
        $htaccessContent .= "    Order Deny,Allow\n";
        $htaccessContent .= "    Deny from all\n";
        $htaccessContent .= "</Files>\n\n";
        $htaccessContent .= "# السماح فقط بأنواع ملفات محددة\n";
        $htaccessContent .= "<FilesMatch \"\\.(pdf|jpg|jpeg|png|webp)$\">\n";
        $htaccessContent .= "    Order Allow,Deny\n";
        $htaccessContent .= "    Allow from all\n";
        $htaccessContent .= "</FilesMatch>\n";
        
        file_put_contents($this->uploadPath . '.htaccess', $htaccessContent);
        file_put_contents($this->coversPath . '.htaccess', $htaccessContent);
    }
    
    /**
     * إنشاء اسم ملف فريد
     */
    private function generateUniqueFilename($originalName, $type) {
        $extension = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
        $basename = pathinfo($originalName, PATHINFO_FILENAME);
        
        // تنظيف اسم الملف
        $basename = preg_replace('/[^a-zA-Z0-9\-_\u0600-\u06FF]/', '', $basename);
        $basename = substr($basename, 0, 50); // تحديد الطول
        
        // إضافة معرف فريد
        $uniqueId = uniqid() . '_' . time();
        
        return $type . '_' . $basename . '_' . $uniqueId . '.' . $extension;
    }
    
    /**
     * التحقق من صحة MIME type
     */
    private function isValidMimeType($mimeType, $extension) {
        $validMimeTypes = [
            'pdf' => ['application/pdf'],
            'jpg' => ['image/jpeg'],
            'jpeg' => ['image/jpeg'],
            'png' => ['image/png'],
            'webp' => ['image/webp']
        ];
        
        return isset($validMimeTypes[$extension]) && 
               in_array($mimeType, $validMimeTypes[$extension]);
    }
    
    /**
     * تحسين الصورة
     */
    private function optimizeImage($imagePath, $type) {
        $imageInfo = getimagesize($imagePath);
        if (!$imageInfo) return;
        
        $mimeType = $imageInfo['mime'];
        $maxWidth = ($type === 'cover') ? 400 : 200;
        $maxHeight = ($type === 'cover') ? 600 : 200;
        $quality = 85;
        
        // إنشاء الصورة من الملف
        switch ($mimeType) {
            case 'image/jpeg':
                $image = imagecreatefromjpeg($imagePath);
                break;
            case 'image/png':
                $image = imagecreatefrompng($imagePath);
                break;
            case 'image/webp':
                $image = imagecreatefromwebp($imagePath);
                break;
            default:
                return;
        }
        
        if (!$image) return;
        
        // الحصول على الأبعاد الحالية
        $currentWidth = imagesx($image);
        $currentHeight = imagesy($image);
        
        // حساب الأبعاد الجديدة
        $ratio = min($maxWidth / $currentWidth, $maxHeight / $currentHeight);
        
        if ($ratio < 1) {
            $newWidth = (int)($currentWidth * $ratio);
            $newHeight = (int)($currentHeight * $ratio);
            
            // إنشاء صورة جديدة
            $newImage = imagecreatetruecolor($newWidth, $newHeight);
            
            // الحفاظ على الشفافية للـ PNG
            if ($mimeType === 'image/png') {
                imagealphablending($newImage, false);
                imagesavealpha($newImage, true);
            }
            
            // تغيير حجم الصورة
            imagecopyresampled($newImage, $image, 0, 0, 0, 0, 
                             $newWidth, $newHeight, $currentWidth, $currentHeight);
            
            // حفظ الصورة المحسنة
            switch ($mimeType) {
                case 'image/jpeg':
                    imagejpeg($newImage, $imagePath, $quality);
                    break;
                case 'image/png':
                    imagepng($newImage, $imagePath, 9);
                    break;
                case 'image/webp':
                    imagewebp($newImage, $imagePath, $quality);
                    break;
            }
            
            imagedestroy($newImage);
        }
        
        imagedestroy($image);
    }
    
    /**
     * إنشاء صورة مصغرة
     */
    private function createThumbnail($imagePath, $filename) {
        $thumbnailPath = $this->coversPath . 'thumbnails/thumb_' . $filename;
        
        $imageInfo = getimagesize($imagePath);
        if (!$imageInfo) return;
        
        $mimeType = $imageInfo['mime'];
        $thumbWidth = 150;
        $thumbHeight = 200;
        
        // إنشاء الصورة من الملف
        switch ($mimeType) {
            case 'image/jpeg':
                $image = imagecreatefromjpeg($imagePath);
                break;
            case 'image/png':
                $image = imagecreatefrompng($imagePath);
                break;
            case 'image/webp':
                $image = imagecreatefromwebp($imagePath);
                break;
            default:
                return;
        }
        
        if (!$image) return;
        
        $currentWidth = imagesx($image);
        $currentHeight = imagesy($image);
        
        // إنشاء صورة مصغرة
        $thumbnail = imagecreatetruecolor($thumbWidth, $thumbHeight);
        
        // الحفاظ على الشفافية
        if ($mimeType === 'image/png') {
            imagealphablending($thumbnail, false);
            imagesavealpha($thumbnail, true);
        }
        
        imagecopyresampled($thumbnail, $image, 0, 0, 0, 0, 
                         $thumbWidth, $thumbHeight, $currentWidth, $currentHeight);
        
        // حفظ الصورة المصغرة
        switch ($mimeType) {
            case 'image/jpeg':
                imagejpeg($thumbnail, $thumbnailPath, 80);
                break;
            case 'image/png':
                imagepng($thumbnail, $thumbnailPath, 9);
                break;
            case 'image/webp':
                imagewebp($thumbnail, $thumbnailPath, 80);
                break;
        }
        
        imagedestroy($image);
        imagedestroy($thumbnail);
    }
    
    /**
     * الحصول على مسار الملف
     */
    private function getFilePath($filename, $type) {
        switch ($type) {
            case 'cover':
                return $this->coversPath . $filename;
            case 'avatar':
                return $this->coversPath . 'avatars/' . $filename;
            case 'book':
            default:
                return $this->uploadPath . $filename;
        }
    }
    
    /**
     * الحصول على رابط الملف
     */
    private function getFileUrl($filename, $type) {
        $baseUrl = SITE_URL . '/public/';
        
        switch ($type) {
            case 'cover':
                return $baseUrl . 'covers/' . $filename;
            case 'avatar':
                return $baseUrl . 'covers/avatars/' . $filename;
            case 'book':
            default:
                return $baseUrl . 'uploads/' . $filename;
        }
    }
    
    /**
     * الحصول على رسالة خطأ الرفع
     */
    private function getUploadErrorMessage($errorCode) {
        switch ($errorCode) {
            case UPLOAD_ERR_INI_SIZE:
            case UPLOAD_ERR_FORM_SIZE:
                return 'حجم الملف كبير جداً';
            case UPLOAD_ERR_PARTIAL:
                return 'تم رفع الملف جزئياً فقط';
            case UPLOAD_ERR_NO_FILE:
                return 'لم يتم اختيار ملف';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'مجلد مؤقت مفقود';
            case UPLOAD_ERR_CANT_WRITE:
                return 'فشل في كتابة الملف';
            case UPLOAD_ERR_EXTENSION:
                return 'رفع الملف متوقف بواسطة إضافة';
            default:
                return 'خطأ غير معروف في رفع الملف';
        }
    }
}
?>
