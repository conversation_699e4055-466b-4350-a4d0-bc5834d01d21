<?php
/**
 * فئة الاستجابة - مكتبتي الحرة
 * Response Class - My Free Library
 */

class Response {
    
    /**
     * إرسال استجابة JSON
     */
    public static function json($data, $statusCode = 200, $message = null) {
        http_response_code($statusCode);
        header('Content-Type: application/json; charset=utf-8');
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
        
        $response = [
            'success' => $statusCode >= 200 && $statusCode < 300,
            'status_code' => $statusCode,
            'timestamp' => date('c'),
            'data' => $data
        ];
        
        if ($message) {
            $response['message'] = $message;
        }
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit;
    }
    
    /**
     * استجابة نجاح
     */
    public static function success($data = null, $message = 'تم بنجاح', $statusCode = 200) {
        self::json($data, $statusCode, $message);
    }
    
    /**
     * استجابة خطأ
     */
    public static function error($message = 'حدث خطأ', $statusCode = 400, $data = null) {
        self::json($data, $statusCode, $message);
    }
    
    /**
     * استجابة غير مصرح
     */
    public static function unauthorized($message = 'غير مصرح بالوصول') {
        self::error($message, 401);
    }
    
    /**
     * استجابة ممنوع
     */
    public static function forbidden($message = 'ممنوع الوصول') {
        self::error($message, 403);
    }
    
    /**
     * استجابة غير موجود
     */
    public static function notFound($message = 'غير موجود') {
        self::error($message, 404);
    }
    
    /**
     * استجابة خطأ خادم
     */
    public static function serverError($message = 'خطأ في الخادم') {
        self::error($message, 500);
    }
    
    /**
     * استجابة بيانات غير صحيحة
     */
    public static function validationError($errors, $message = 'بيانات غير صحيحة') {
        self::json(['errors' => $errors], 422, $message);
    }
    
    /**
     * استجابة مع ترقيم الصفحات
     */
    public static function paginated($data, $pagination, $message = null) {
        $response = [
            'items' => $data,
            'pagination' => $pagination
        ];
        
        self::success($response, $message);
    }
}

/**
 * فئة التحقق من صحة البيانات
 * Validation Class
 */
class Validator {
    private $errors = [];
    private $data = [];
    
    public function __construct($data) {
        $this->data = $data;
    }
    
    /**
     * التحقق من وجود الحقل
     */
    public function required($field, $message = null) {
        if (!isset($this->data[$field]) || empty(trim($this->data[$field]))) {
            $this->errors[$field] = $message ?: "حقل $field مطلوب";
        }
        return $this;
    }
    
    /**
     * التحقق من البريد الإلكتروني
     */
    public function email($field, $message = null) {
        if (isset($this->data[$field]) && !filter_var($this->data[$field], FILTER_VALIDATE_EMAIL)) {
            $this->errors[$field] = $message ?: "حقل $field يجب أن يكون بريد إلكتروني صحيح";
        }
        return $this;
    }
    
    /**
     * التحقق من الحد الأدنى للطول
     */
    public function minLength($field, $length, $message = null) {
        if (isset($this->data[$field]) && strlen($this->data[$field]) < $length) {
            $this->errors[$field] = $message ?: "حقل $field يجب أن يكون $length أحرف على الأقل";
        }
        return $this;
    }
    
    /**
     * التحقق من الحد الأقصى للطول
     */
    public function maxLength($field, $length, $message = null) {
        if (isset($this->data[$field]) && strlen($this->data[$field]) > $length) {
            $this->errors[$field] = $message ?: "حقل $field يجب أن يكون $length أحرف كحد أقصى";
        }
        return $this;
    }
    
    /**
     * التحقق من تطابق الحقول
     */
    public function matches($field, $matchField, $message = null) {
        if (isset($this->data[$field]) && isset($this->data[$matchField]) && 
            $this->data[$field] !== $this->data[$matchField]) {
            $this->errors[$field] = $message ?: "حقل $field يجب أن يطابق $matchField";
        }
        return $this;
    }
    
    /**
     * التحقق من القيم المسموحة
     */
    public function in($field, $values, $message = null) {
        if (isset($this->data[$field]) && !in_array($this->data[$field], $values)) {
            $this->errors[$field] = $message ?: "حقل $field يحتوي على قيمة غير مسموحة";
        }
        return $this;
    }
    
    /**
     * التحقق من الرقم
     */
    public function numeric($field, $message = null) {
        if (isset($this->data[$field]) && !is_numeric($this->data[$field])) {
            $this->errors[$field] = $message ?: "حقل $field يجب أن يكون رقم";
        }
        return $this;
    }
    
    /**
     * التحقق من الرقم الصحيح
     */
    public function integer($field, $message = null) {
        if (isset($this->data[$field]) && !filter_var($this->data[$field], FILTER_VALIDATE_INT)) {
            $this->errors[$field] = $message ?: "حقل $field يجب أن يكون رقم صحيح";
        }
        return $this;
    }
    
    /**
     * التحقق من القيمة الدنيا
     */
    public function min($field, $min, $message = null) {
        if (isset($this->data[$field]) && $this->data[$field] < $min) {
            $this->errors[$field] = $message ?: "حقل $field يجب أن يكون $min أو أكثر";
        }
        return $this;
    }
    
    /**
     * التحقق من القيمة العليا
     */
    public function max($field, $max, $message = null) {
        if (isset($this->data[$field]) && $this->data[$field] > $max) {
            $this->errors[$field] = $message ?: "حقل $field يجب أن يكون $max أو أقل";
        }
        return $this;
    }
    
    /**
     * التحقق من وجود الأخطاء
     */
    public function hasErrors() {
        return !empty($this->errors);
    }
    
    /**
     * الحصول على الأخطاء
     */
    public function getErrors() {
        return $this->errors;
    }
    
    /**
     * الحصول على البيانات المنظفة
     */
    public function getData() {
        return array_map('sanitize', $this->data);
    }
}

/**
 * فئة المصادقة
 * Authentication Class
 */
class Auth {
    
    /**
     * التحقق من الرمز المميز
     */
    public static function verifyToken() {
        $headers = getallheaders();
        $token = null;
        
        // البحث عن الرمز في الهيدر
        if (isset($headers['Authorization'])) {
            $authHeader = $headers['Authorization'];
            if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                $token = $matches[1];
            }
        }
        
        // البحث عن الرمز في الكوكيز
        if (!$token && isset($_COOKIE['auth_token'])) {
            $token = $_COOKIE['auth_token'];
        }
        
        if (!$token) {
            return null;
        }
        
        try {
            // فك تشفير الرمز (هنا نستخدم طريقة بسيطة، في الإنتاج استخدم JWT)
            $decoded = base64_decode($token);
            $data = json_decode($decoded, true);
            
            if (!$data || !isset($data['user_id']) || !isset($data['expires'])) {
                return null;
            }
            
            // التحقق من انتهاء الصلاحية
            if (time() > $data['expires']) {
                return null;
            }
            
            // الحصول على بيانات المستخدم
            $db = getDB();
            $user = $db->fetch(
                "SELECT * FROM users WHERE id = ? AND status = 'active'",
                [$data['user_id']]
            );
            
            return $user;
            
        } catch (Exception $e) {
            return null;
        }
    }
    
    /**
     * إنشاء رمز مميز
     */
    public static function createToken($userId, $rememberMe = false) {
        $expires = time() + ($rememberMe ? REMEMBER_ME_LIFETIME : SESSION_LIFETIME);
        
        $data = [
            'user_id' => $userId,
            'expires' => $expires,
            'created' => time()
        ];
        
        return base64_encode(json_encode($data));
    }
    
    /**
     * التحقق من صلاحيات المدير
     */
    public static function requireAdmin() {
        $user = self::verifyToken();
        
        if (!$user) {
            Response::unauthorized('يجب تسجيل الدخول أولاً');
        }
        
        if ($user['role'] !== 'admin') {
            Response::forbidden('ليس لديك صلاحية للوصول');
        }
        
        return $user;
    }
    
    /**
     * التحقق من تسجيل الدخول
     */
    public static function requireAuth() {
        $user = self::verifyToken();
        
        if (!$user) {
            Response::unauthorized('يجب تسجيل الدخول أولاً');
        }
        
        return $user;
    }
}

/**
 * فئة الترقيم
 * Pagination Class
 */
class Paginator {
    private $page;
    private $limit;
    private $total;
    
    public function __construct($page = 1, $limit = 10, $total = 0) {
        $this->page = max(1, (int)$page);
        $this->limit = max(1, min(100, (int)$limit));
        $this->total = max(0, (int)$total);
    }
    
    /**
     * الحصول على الإزاحة
     */
    public function getOffset() {
        return ($this->page - 1) * $this->limit;
    }
    
    /**
     * الحصول على الحد
     */
    public function getLimit() {
        return $this->limit;
    }
    
    /**
     * الحصول على معلومات الترقيم
     */
    public function getPaginationInfo() {
        $totalPages = ceil($this->total / $this->limit);
        
        return [
            'current_page' => $this->page,
            'per_page' => $this->limit,
            'total_items' => $this->total,
            'total_pages' => $totalPages,
            'has_previous' => $this->page > 1,
            'has_next' => $this->page < $totalPages,
            'previous_page' => $this->page > 1 ? $this->page - 1 : null,
            'next_page' => $this->page < $totalPages ? $this->page + 1 : null
        ];
    }
}
?>
