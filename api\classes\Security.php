<?php
/**
 * فئة الأمان - مكتبتي الحرة
 * Security Class - My Free Library
 */

require_once '../database/config.php';

class Security {
    
    /**
     * تشفير البيانات
     */
    public static function encrypt($data) {
        $key = ENCRYPTION_KEY;
        $iv = random_bytes(16);
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);
        return base64_encode($iv . $encrypted);
    }
    
    /**
     * فك تشفير البيانات
     */
    public static function decrypt($encryptedData) {
        $key = ENCRYPTION_KEY;
        $data = base64_decode($encryptedData);
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        return openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
    }
    
    /**
     * تنظيف البيانات من XSS
     */
    public static function sanitizeInput($input) {
        if (is_array($input)) {
            return array_map([self::class, 'sanitizeInput'], $input);
        }
        
        // إزالة العلامات الخطيرة
        $input = strip_tags($input);
        
        // تحويل الأحرف الخاصة
        $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
        
        // إزالة المسافات الزائدة
        $input = trim($input);
        
        return $input;
    }
    
    /**
     * التحقق من قوة كلمة المرور
     */
    public static function validatePasswordStrength($password) {
        $errors = [];
        
        if (strlen($password) < 8) {
            $errors[] = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل';
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل';
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على رقم واحد على الأقل';
        }
        
        if (!preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل';
        }
        
        return $errors;
    }
    
    /**
     * منع هجمات Brute Force
     */
    public static function checkBruteForce($identifier, $maxAttempts = 5, $timeWindow = 900) {
        $db = getDB();
        
        // حذف المحاولات القديمة
        $db->delete(
            "DELETE FROM login_attempts WHERE attempted_at < DATE_SUB(NOW(), INTERVAL ? SECOND)",
            [$timeWindow]
        );
        
        // عد المحاولات الحالية
        $attempts = $db->fetch(
            "SELECT COUNT(*) as count FROM login_attempts 
             WHERE identifier = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)",
            [$identifier, $timeWindow]
        )['count'];
        
        return $attempts < $maxAttempts;
    }
    
    /**
     * تسجيل محاولة تسجيل دخول فاشلة
     */
    public static function recordFailedLogin($identifier) {
        $db = getDB();
        
        $db->insert(
            "INSERT INTO login_attempts (identifier, ip_address, user_agent, attempted_at) 
             VALUES (?, ?, ?, NOW())",
            [$identifier, getClientIP(), getUserAgent()]
        );
    }
    
    /**
     * التحقق من صحة CSRF Token
     */
    public static function validateCSRFToken($token) {
        if (!isset($_SESSION['csrf_token'])) {
            return false;
        }
        
        return hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * إنشاء CSRF Token
     */
    public static function generateCSRFToken() {
        $token = bin2hex(random_bytes(32));
        $_SESSION['csrf_token'] = $token;
        return $token;
    }
    
    /**
     * التحقق من Rate Limiting
     */
    public static function checkRateLimit($identifier, $maxRequests = 100, $timeWindow = 3600) {
        $db = getDB();
        
        // حذف الطلبات القديمة
        $db->delete(
            "DELETE FROM rate_limits WHERE created_at < DATE_SUB(NOW(), INTERVAL ? SECOND)",
            [$timeWindow]
        );
        
        // عد الطلبات الحالية
        $requests = $db->fetch(
            "SELECT COUNT(*) as count FROM rate_limits 
             WHERE identifier = ? AND created_at > DATE_SUB(NOW(), INTERVAL ? SECOND)",
            [$identifier, $timeWindow]
        )['count'];
        
        if ($requests >= $maxRequests) {
            return false;
        }
        
        // تسجيل الطلب الحالي
        $db->insert(
            "INSERT INTO rate_limits (identifier, ip_address, user_agent) VALUES (?, ?, ?)",
            [$identifier, getClientIP(), getUserAgent()]
        );
        
        return true;
    }
    
    /**
     * التحقق من صحة الملف المرفوع
     */
    public static function validateUploadedFile($file, $allowedTypes, $maxSize) {
        $errors = [];
        
        // التحقق من وجود خطأ في الرفع
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $errors[] = 'خطأ في رفع الملف';
            return $errors;
        }
        
        // التحقق من حجم الملف
        if ($file['size'] > $maxSize) {
            $errors[] = 'حجم الملف كبير جداً';
        }
        
        // التحقق من نوع الملف
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, $allowedTypes)) {
            $errors[] = 'نوع الملف غير مدعوم';
        }
        
        // التحقق من MIME type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        $allowedMimeTypes = [
            'pdf' => ['application/pdf'],
            'jpg' => ['image/jpeg'],
            'jpeg' => ['image/jpeg'],
            'png' => ['image/png'],
            'webp' => ['image/webp']
        ];
        
        if (isset($allowedMimeTypes[$extension]) && 
            !in_array($mimeType, $allowedMimeTypes[$extension])) {
            $errors[] = 'نوع الملف غير صحيح';
        }
        
        // فحص محتوى الملف للتأكد من عدم وجود كود ضار
        if (self::containsMaliciousContent($file['tmp_name'])) {
            $errors[] = 'الملف يحتوي على محتوى ضار';
        }
        
        return $errors;
    }
    
    /**
     * فحص المحتوى الضار في الملفات
     */
    private static function containsMaliciousContent($filePath) {
        $content = file_get_contents($filePath, false, null, 0, 1024); // قراءة أول 1KB
        
        // البحث عن أنماط ضارة
        $maliciousPatterns = [
            '/<\?php/i',
            '/<script/i',
            '/javascript:/i',
            '/vbscript:/i',
            '/onload=/i',
            '/onerror=/i'
        ];
        
        foreach ($maliciousPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * تسجيل حدث أمني
     */
    public static function logSecurityEvent($event, $details = null, $severity = 'medium') {
        $db = getDB();
        
        $db->insert(
            "INSERT INTO security_logs (event_type, details, severity, ip_address, user_agent, created_at) 
             VALUES (?, ?, ?, ?, ?, NOW())",
            [
                $event,
                $details ? json_encode($details) : null,
                $severity,
                getClientIP(),
                getUserAgent()
            ]
        );
    }
    
    /**
     * التحقق من الصلاحيات
     */
    public static function hasPermission($user, $permission) {
        // نظام صلاحيات بسيط
        $permissions = [
            'admin' => ['*'], // المدير له جميع الصلاحيات
            'user' => ['read', 'download', 'rate', 'comment']
        ];
        
        $userPermissions = $permissions[$user['role']] ?? [];
        
        return in_array('*', $userPermissions) || in_array($permission, $userPermissions);
    }
    
    /**
     * تنظيف قاعدة البيانات من البيانات القديمة
     */
    public static function cleanupOldData() {
        $db = getDB();
        
        try {
            $db->beginTransaction();
            
            // حذف محاولات تسجيل الدخول القديمة (أكثر من 24 ساعة)
            $db->delete(
                "DELETE FROM login_attempts WHERE attempted_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)"
            );
            
            // حذف جلسات منتهية الصلاحية
            $db->delete(
                "DELETE FROM user_sessions WHERE expires_at < NOW()"
            );
            
            // حذف رموز إعادة تعيين كلمة المرور المنتهية
            $db->update(
                "UPDATE users SET reset_token = NULL, reset_token_expires = NULL 
                 WHERE reset_token_expires < NOW()"
            );
            
            // حذف سجلات الأمان القديمة (أكثر من 30 يوم)
            $db->delete(
                "DELETE FROM security_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)"
            );
            
            // حذف سجلات Rate Limiting القديمة
            $db->delete(
                "DELETE FROM rate_limits WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 HOUR)"
            );
            
            $db->commit();
            
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
    }
    
    /**
     * إنشاء تقرير أمني
     */
    public static function generateSecurityReport($days = 7) {
        $db = getDB();
        
        $report = [];
        
        // إحصائيات محاولات تسجيل الدخول الفاشلة
        $report['failed_logins'] = $db->fetch(
            "SELECT COUNT(*) as count FROM login_attempts 
             WHERE attempted_at > DATE_SUB(NOW(), INTERVAL ? DAY)",
            [$days]
        )['count'];
        
        // أكثر عناوين IP نشاطاً
        $report['top_ips'] = $db->fetchAll(
            "SELECT ip_address, COUNT(*) as requests 
             FROM rate_limits 
             WHERE created_at > DATE_SUB(NOW(), INTERVAL ? DAY)
             GROUP BY ip_address 
             ORDER BY requests DESC 
             LIMIT 10",
            [$days]
        );
        
        // الأحداث الأمنية
        $report['security_events'] = $db->fetchAll(
            "SELECT event_type, severity, COUNT(*) as count 
             FROM security_logs 
             WHERE created_at > DATE_SUB(NOW(), INTERVAL ? DAY)
             GROUP BY event_type, severity 
             ORDER BY count DESC",
            [$days]
        );
        
        return $report;
    }
}

/**
 * Middleware للأمان
 */
class SecurityMiddleware {
    
    /**
     * تطبيق إجراءات الأمان الأساسية
     */
    public static function apply() {
        // منع XSS
        self::preventXSS();
        
        // منع Clickjacking
        self::preventClickjacking();
        
        // تطبيق HTTPS
        self::enforceHTTPS();
        
        // التحقق من Rate Limiting
        self::checkRateLimit();
    }
    
    private static function preventXSS() {
        header('X-Content-Type-Options: nosniff');
        header('X-XSS-Protection: 1; mode=block');
    }
    
    private static function preventClickjacking() {
        header('X-Frame-Options: DENY');
    }
    
    private static function enforceHTTPS() {
        if (!DEBUG_MODE && !isset($_SERVER['HTTPS'])) {
            header('Location: https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'], true, 301);
            exit;
        }
    }
    
    private static function checkRateLimit() {
        $identifier = getClientIP();
        
        if (!Security::checkRateLimit($identifier, 1000, 3600)) { // 1000 طلب في الساعة
            http_response_code(429);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'تم تجاوز الحد المسموح من الطلبات'
            ]);
            exit;
        }
    }
}
?>
