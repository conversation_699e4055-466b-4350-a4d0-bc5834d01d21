<?php
/**
 * فئة المستخدم - مكتبتي الحرة
 * User Class - My Free Library
 */

require_once '../database/config.php';
require_once 'Response.php';

class User {
    private $db;
    
    public function __construct() {
        $this->db = getDB();
    }
    
    /**
     * تسجيل مستخدم جديد
     */
    public function register($data) {
        // التحقق من صحة البيانات
        $validator = new Validator($data);
        $validator->required('name', 'الاسم مطلوب')
                 ->required('email', 'البريد الإلكتروني مطلوب')
                 ->email('email', 'البريد الإلكتروني غير صحيح')
                 ->required('password', 'كلمة المرور مطلوبة')
                 ->minLength('password', 6, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل')
                 ->required('password_confirm', 'تأكيد كلمة المرور مطلوب')
                 ->matches('password_confirm', 'password', 'كلمة المرور وتأكيدها غير متطابقين');
        
        if ($validator->hasErrors()) {
            Response::validationError($validator->getErrors());
        }
        
        $cleanData = $validator->getData();
        
        // التحقق من عدم وجود البريد الإلكتروني مسبقاً
        $existingUser = $this->db->fetch(
            "SELECT id FROM users WHERE email = ?",
            [$cleanData['email']]
        );
        
        if ($existingUser) {
            Response::error('البريد الإلكتروني مستخدم مسبقاً', 409);
        }
        
        try {
            // إنشاء المستخدم
            $userId = $this->db->insert(
                "INSERT INTO users (name, email, password_hash, verification_token, created_at) 
                 VALUES (?, ?, ?, ?, NOW())",
                [
                    $cleanData['name'],
                    $cleanData['email'],
                    hashPassword($cleanData['password']),
                    generateToken()
                ]
            );
            
            // تسجيل النشاط
            $this->logActivity($userId, 'user_registered', 'users', $userId);
            
            // الحصول على بيانات المستخدم الجديد
            $user = $this->getUserById($userId);
            
            Response::success([
                'user' => $this->formatUserData($user),
                'message' => 'تم إنشاء الحساب بنجاح'
            ], 'تم التسجيل بنجاح', 201);
            
        } catch (Exception $e) {
            Response::serverError('خطأ في إنشاء الحساب');
        }
    }
    
    /**
     * تسجيل الدخول
     */
    public function login($data) {
        // التحقق من صحة البيانات
        $validator = new Validator($data);
        $validator->required('email', 'البريد الإلكتروني مطلوب')
                 ->email('email', 'البريد الإلكتروني غير صحيح')
                 ->required('password', 'كلمة المرور مطلوبة');
        
        if ($validator->hasErrors()) {
            Response::validationError($validator->getErrors());
        }
        
        $cleanData = $validator->getData();
        
        // البحث عن المستخدم
        $user = $this->db->fetch(
            "SELECT * FROM users WHERE email = ? AND status = 'active'",
            [$cleanData['email']]
        );
        
        if (!$user || !verifyPassword($cleanData['password'], $user['password_hash'])) {
            Response::error('البريد الإلكتروني أو كلمة المرور غير صحيحة', 401);
        }
        
        try {
            // تحديث آخر تسجيل دخول
            $this->db->update(
                "UPDATE users SET last_login = NOW() WHERE id = ?",
                [$user['id']]
            );
            
            // إنشاء رمز المصادقة
            $rememberMe = isset($data['remember_me']) && $data['remember_me'];
            $token = Auth::createToken($user['id'], $rememberMe);
            
            // حفظ الجلسة
            $this->createSession($user['id'], $token);
            
            // تسجيل النشاط
            $this->logActivity($user['id'], 'user_login', 'users', $user['id']);
            
            Response::success([
                'user' => $this->formatUserData($user),
                'token' => $token,
                'expires_in' => $rememberMe ? REMEMBER_ME_LIFETIME : SESSION_LIFETIME
            ], 'تم تسجيل الدخول بنجاح');
            
        } catch (Exception $e) {
            Response::serverError('خطأ في تسجيل الدخول');
        }
    }
    
    /**
     * تسجيل الخروج
     */
    public function logout() {
        $user = Auth::requireAuth();
        
        try {
            // حذف الجلسة
            $this->deleteUserSessions($user['id']);
            
            // تسجيل النشاط
            $this->logActivity($user['id'], 'user_logout', 'users', $user['id']);
            
            Response::success(null, 'تم تسجيل الخروج بنجاح');
            
        } catch (Exception $e) {
            Response::serverError('خطأ في تسجيل الخروج');
        }
    }
    
    /**
     * الحصول على الملف الشخصي
     */
    public function getProfile() {
        $user = Auth::requireAuth();
        
        // الحصول على إحصائيات المستخدم
        $stats = $this->getUserStats($user['id']);
        
        Response::success([
            'user' => $this->formatUserData($user),
            'stats' => $stats
        ]);
    }
    
    /**
     * تحديث الملف الشخصي
     */
    public function updateProfile($data) {
        $user = Auth::requireAuth();
        
        // التحقق من صحة البيانات
        $validator = new Validator($data);
        $validator->required('name', 'الاسم مطلوب');
        
        // التحقق من البريد الإلكتروني إذا تم تغييره
        if (isset($data['email']) && $data['email'] !== $user['email']) {
            $validator->email('email', 'البريد الإلكتروني غير صحيح');
            
            $existingUser = $this->db->fetch(
                "SELECT id FROM users WHERE email = ? AND id != ?",
                [$data['email'], $user['id']]
            );
            
            if ($existingUser) {
                Response::error('البريد الإلكتروني مستخدم مسبقاً', 409);
            }
        }
        
        if ($validator->hasErrors()) {
            Response::validationError($validator->getErrors());
        }
        
        $cleanData = $validator->getData();
        
        try {
            // تحديث البيانات
            $updateFields = ['name = ?'];
            $updateValues = [$cleanData['name']];
            
            if (isset($cleanData['email'])) {
                $updateFields[] = 'email = ?';
                $updateValues[] = $cleanData['email'];
            }
            
            $updateValues[] = $user['id'];
            
            $this->db->update(
                "UPDATE users SET " . implode(', ', $updateFields) . ", updated_at = NOW() WHERE id = ?",
                $updateValues
            );
            
            // تسجيل النشاط
            $this->logActivity($user['id'], 'profile_updated', 'users', $user['id']);
            
            // الحصول على البيانات المحدثة
            $updatedUser = $this->getUserById($user['id']);
            
            Response::success([
                'user' => $this->formatUserData($updatedUser)
            ], 'تم تحديث الملف الشخصي بنجاح');
            
        } catch (Exception $e) {
            Response::serverError('خطأ في تحديث الملف الشخصي');
        }
    }
    
    /**
     * تغيير كلمة المرور
     */
    public function changePassword($data) {
        $user = Auth::requireAuth();
        
        // التحقق من صحة البيانات
        $validator = new Validator($data);
        $validator->required('current_password', 'كلمة المرور الحالية مطلوبة')
                 ->required('new_password', 'كلمة المرور الجديدة مطلوبة')
                 ->minLength('new_password', 6, 'كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل')
                 ->required('new_password_confirm', 'تأكيد كلمة المرور الجديدة مطلوب')
                 ->matches('new_password_confirm', 'new_password', 'كلمة المرور الجديدة وتأكيدها غير متطابقين');
        
        if ($validator->hasErrors()) {
            Response::validationError($validator->getErrors());
        }
        
        $cleanData = $validator->getData();
        
        // التحقق من كلمة المرور الحالية
        if (!verifyPassword($cleanData['current_password'], $user['password_hash'])) {
            Response::error('كلمة المرور الحالية غير صحيحة', 401);
        }
        
        try {
            // تحديث كلمة المرور
            $this->db->update(
                "UPDATE users SET password_hash = ?, updated_at = NOW() WHERE id = ?",
                [hashPassword($cleanData['new_password']), $user['id']]
            );
            
            // حذف جميع الجلسات الأخرى
            $this->deleteUserSessions($user['id']);
            
            // تسجيل النشاط
            $this->logActivity($user['id'], 'password_changed', 'users', $user['id']);
            
            Response::success(null, 'تم تغيير كلمة المرور بنجاح');
            
        } catch (Exception $e) {
            Response::serverError('خطأ في تغيير كلمة المرور');
        }
    }
    
    /**
     * الحصول على المستخدم بالمعرف
     */
    private function getUserById($id) {
        return $this->db->fetch(
            "SELECT * FROM users WHERE id = ?",
            [$id]
        );
    }
    
    /**
     * تنسيق بيانات المستخدم
     */
    private function formatUserData($user) {
        unset($user['password_hash'], $user['verification_token'], $user['reset_token']);
        return $user;
    }
    
    /**
     * الحصول على إحصائيات المستخدم
     */
    private function getUserStats($userId) {
        $stats = [];
        
        // عدد الكتب المفضلة
        $stats['favorites_count'] = $this->db->fetch(
            "SELECT COUNT(*) as count FROM favorites WHERE user_id = ?",
            [$userId]
        )['count'];
        
        // عدد التحميلات
        $stats['downloads_count'] = $this->db->fetch(
            "SELECT COUNT(*) as count FROM downloads WHERE user_id = ?",
            [$userId]
        )['count'];
        
        // عدد العلامات المرجعية
        $stats['bookmarks_count'] = $this->db->fetch(
            "SELECT COUNT(*) as count FROM bookmarks WHERE user_id = ?",
            [$userId]
        )['count'];
        
        // عدد التقييمات
        $stats['ratings_count'] = $this->db->fetch(
            "SELECT COUNT(*) as count FROM book_ratings WHERE user_id = ?",
            [$userId]
        )['count'];
        
        return $stats;
    }
    
    /**
     * إنشاء جلسة
     */
    private function createSession($userId, $token) {
        $sessionId = hash('sha256', $token);
        $expiresAt = date('Y-m-d H:i:s', time() + SESSION_LIFETIME);
        
        $this->db->insert(
            "INSERT INTO user_sessions (id, user_id, ip_address, user_agent, expires_at) 
             VALUES (?, ?, ?, ?, ?)",
            [$sessionId, $userId, getClientIP(), getUserAgent(), $expiresAt]
        );
    }
    
    /**
     * حذف جلسات المستخدم
     */
    private function deleteUserSessions($userId) {
        $this->db->delete(
            "DELETE FROM user_sessions WHERE user_id = ?",
            [$userId]
        );
    }
    
    /**
     * تسجيل النشاط
     */
    private function logActivity($userId, $action, $entityType, $entityId, $details = null) {
        $this->db->insert(
            "INSERT INTO activity_logs (user_id, action, entity_type, entity_id, details, ip_address, user_agent) 
             VALUES (?, ?, ?, ?, ?, ?, ?)",
            [
                $userId,
                $action,
                $entityType,
                $entityId,
                $details ? json_encode($details) : null,
                getClientIP(),
                getUserAgent()
            ]
        );
    }
}
?>
