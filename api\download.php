<?php
/**
 * نقطة نهاية التحميل المحمي - مكتبتي الحرة
 * Protected Download Endpoint - My Free Library
 */

// تضمين الملفات المطلوبة
require_once '../database/config.php';
require_once 'classes/Response.php';

// التحقق من وجود معامل الملف
if (!isset($_GET['file'])) {
    http_response_code(404);
    die('الملف غير موجود');
}

$filename = $_GET['file'];

try {
    // التحقق من المصادقة
    $user = Auth::requireAuth();
    
    // التحقق من صحة اسم الملف
    if (!preg_match('/^[a-zA-Z0-9_\-\.]+$/', $filename) || strpos($filename, '..') !== false) {
        http_response_code(400);
        die('اسم ملف غير صحيح');
    }
    
    // البحث عن الكتاب في قاعدة البيانات
    $db = getDB();
    $book = $db->fetch(
        "SELECT id, title, author, file_path, file_size FROM books 
         WHERE file_path = ? AND status = 'active'",
        [$filename]
    );
    
    if (!$book) {
        http_response_code(404);
        die('الكتاب غير موجود');
    }
    
    // مسار الملف الفعلي
    $filePath = UPLOAD_PATH . $filename;
    
    // التحقق من وجود الملف
    if (!file_exists($filePath)) {
        http_response_code(404);
        die('الملف غير موجود على الخادم');
    }
    
    // تسجيل التحميل
    $db->insert(
        "INSERT INTO downloads (user_id, book_id, ip_address, user_agent) 
         VALUES (?, ?, ?, ?)",
        [$user['id'], $book['id'], getClientIP(), getUserAgent()]
    );
    
    // زيادة عداد التحميلات
    $db->update(
        "UPDATE books SET downloads_count = downloads_count + 1 WHERE id = ?",
        [$book['id']]
    );
    
    // تسجيل النشاط
    $db->insert(
        "INSERT INTO activity_logs (user_id, action, entity_type, entity_id, details, ip_address, user_agent) 
         VALUES (?, ?, ?, ?, ?, ?, ?)",
        [
            $user['id'],
            'book_downloaded',
            'books',
            $book['id'],
            json_encode(['filename' => $filename, 'title' => $book['title']]),
            getClientIP(),
            getUserAgent()
        ]
    );
    
    // إعداد headers للتحميل
    $fileSize = filesize($filePath);
    $fileName = $book['title'] . '.pdf';
    
    // تنظيف اسم الملف للتحميل
    $fileName = preg_replace('/[^a-zA-Z0-9\-_\.\u0600-\u06FF\s]/', '', $fileName);
    
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="' . $fileName . '"');
    header('Content-Length: ' . $fileSize);
    header('Cache-Control: private, must-revalidate');
    header('Pragma: private');
    header('Expires: 0');
    
    // دعم التحميل المتقطع (Resume)
    $range = null;
    if (isset($_SERVER['HTTP_RANGE'])) {
        $range = $_SERVER['HTTP_RANGE'];
        
        if (preg_match('/bytes=(\d+)-(\d*)/i', $range, $matches)) {
            $start = (int)$matches[1];
            $end = !empty($matches[2]) ? (int)$matches[2] : $fileSize - 1;
            
            if ($start < $fileSize && $end < $fileSize && $start <= $end) {
                header('HTTP/1.1 206 Partial Content');
                header('Accept-Ranges: bytes');
                header('Content-Range: bytes ' . $start . '-' . $end . '/' . $fileSize);
                header('Content-Length: ' . ($end - $start + 1));
                
                // قراءة وإرسال الجزء المطلوب
                $file = fopen($filePath, 'rb');
                fseek($file, $start);
                
                $chunkSize = 8192; // 8KB chunks
                $remaining = $end - $start + 1;
                
                while ($remaining > 0 && !feof($file)) {
                    $readSize = min($chunkSize, $remaining);
                    echo fread($file, $readSize);
                    $remaining -= $readSize;
                    
                    if (connection_aborted()) {
                        break;
                    }
                }
                
                fclose($file);
                exit;
            }
        }
    }
    
    // إرسال الملف كاملاً
    $file = fopen($filePath, 'rb');
    
    if ($file) {
        while (!feof($file)) {
            echo fread($file, 8192);
            
            if (connection_aborted()) {
                break;
            }
        }
        fclose($file);
    }
    
    exit;
    
} catch (Exception $e) {
    if (DEBUG_MODE) {
        http_response_code(500);
        die('خطأ في التحميل: ' . $e->getMessage());
    } else {
        http_response_code(500);
        die('خطأ في التحميل');
    }
}

/**
 * نقطة نهاية معاينة الملفات (للصور)
 */
if (isset($_GET['preview'])) {
    $filename = $_GET['preview'];
    $type = $_GET['type'] ?? 'cover';
    
    // التحقق من صحة اسم الملف
    if (!preg_match('/^[a-zA-Z0-9_\-\.]+$/', $filename) || strpos($filename, '..') !== false) {
        http_response_code(400);
        die('اسم ملف غير صحيح');
    }
    
    // تحديد مسار الملف حسب النوع
    switch ($type) {
        case 'cover':
            $filePath = COVERS_PATH . $filename;
            break;
        case 'avatar':
            $filePath = COVERS_PATH . 'avatars/' . $filename;
            break;
        case 'thumbnail':
            $filePath = COVERS_PATH . 'thumbnails/' . $filename;
            break;
        default:
            http_response_code(400);
            die('نوع ملف غير مدعوم');
    }
    
    // التحقق من وجود الملف
    if (!file_exists($filePath)) {
        http_response_code(404);
        die('الملف غير موجود');
    }
    
    // تحديد نوع المحتوى
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    $mimeTypes = [
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'webp' => 'image/webp',
        'gif' => 'image/gif'
    ];
    
    $mimeType = $mimeTypes[$extension] ?? 'application/octet-stream';
    
    // إعداد headers
    header('Content-Type: ' . $mimeType);
    header('Content-Length: ' . filesize($filePath));
    header('Cache-Control: public, max-age=31536000'); // سنة واحدة
    header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 31536000) . ' GMT');
    
    // إرسال الملف
    readfile($filePath);
    exit;
}

/**
 * إحصائيات التحميل
 */
if (isset($_GET['stats'])) {
    $user = Auth::requireAdmin();
    
    try {
        $db = getDB();
        
        // إحصائيات عامة
        $stats = [];
        
        // إجمالي التحميلات
        $stats['total_downloads'] = $db->fetch(
            "SELECT COUNT(*) as count FROM downloads"
        )['count'];
        
        // التحميلات اليوم
        $stats['today_downloads'] = $db->fetch(
            "SELECT COUNT(*) as count FROM downloads WHERE DATE(downloaded_at) = CURDATE()"
        )['count'];
        
        // التحميلات هذا الأسبوع
        $stats['week_downloads'] = $db->fetch(
            "SELECT COUNT(*) as count FROM downloads WHERE downloaded_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)"
        )['count'];
        
        // التحميلات هذا الشهر
        $stats['month_downloads'] = $db->fetch(
            "SELECT COUNT(*) as count FROM downloads WHERE downloaded_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)"
        )['count'];
        
        // أكثر الكتب تحميلاً
        $stats['top_books'] = $db->fetchAll(
            "SELECT b.id, b.title, b.author, COUNT(d.id) as download_count
             FROM books b
             LEFT JOIN downloads d ON b.id = d.book_id
             WHERE b.status = 'active'
             GROUP BY b.id
             ORDER BY download_count DESC
             LIMIT 10"
        );
        
        // إحصائيات التحميل الشهرية
        $stats['monthly_stats'] = $db->fetchAll(
            "SELECT 
                DATE_FORMAT(downloaded_at, '%Y-%m') as month,
                COUNT(*) as downloads
             FROM downloads 
             WHERE downloaded_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
             GROUP BY DATE_FORMAT(downloaded_at, '%Y-%m')
             ORDER BY month ASC"
        );
        
        Response::success($stats);
        
    } catch (Exception $e) {
        Response::serverError('خطأ في جلب الإحصائيات');
    }
}
?>
