<?php
/**
 * نقطة دخول API - مكتبتي الحرة
 * API Entry Point - My Free Library
 */

// إعداد الترميز والأخطاء
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// التعامل مع طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// تضمين الملفات المطلوبة
require_once '../database/config.php';
require_once 'classes/Response.php';
require_once 'classes/User.php';
require_once 'classes/Book.php';

// إعداد معالجة الأخطاء
set_error_handler(function($severity, $message, $file, $line) {
    if (DEBUG_MODE) {
        Response::serverError("خطأ: $message في $file:$line");
    } else {
        Response::serverError('حدث خطأ في الخادم');
    }
});

set_exception_handler(function($exception) {
    if (DEBUG_MODE) {
        Response::serverError('استثناء: ' . $exception->getMessage());
    } else {
        Response::serverError('حدث خطأ في الخادم');
    }
});

// الحصول على المسار والطريقة
$requestUri = $_SERVER['REQUEST_URI'];
$requestMethod = $_SERVER['REQUEST_METHOD'];

// إزالة معاملات الاستعلام من المسار
$path = parse_url($requestUri, PHP_URL_PATH);

// إزالة /api من بداية المسار
$path = preg_replace('#^/api#', '', $path);

// تقسيم المسار
$pathParts = array_filter(explode('/', $path));
$pathParts = array_values($pathParts); // إعادة فهرسة المصفوفة

// الحصول على البيانات المرسلة
$inputData = [];
if (in_array($requestMethod, ['POST', 'PUT', 'PATCH'])) {
    $rawInput = file_get_contents('php://input');
    $inputData = json_decode($rawInput, true) ?? [];
    
    // دمج البيانات مع $_POST إذا كانت موجودة
    $inputData = array_merge($_POST, $inputData);
}

// دمج معاملات GET
$inputData = array_merge($_GET, $inputData);

// توجيه الطلبات
try {
    // التحقق من وجود نقطة النهاية
    if (empty($pathParts)) {
        Response::success([
            'message' => 'مرحباً بك في API مكتبتي الحرة',
            'version' => '1.0.0',
            'endpoints' => [
                'auth' => [
                    'POST /auth/register' => 'تسجيل مستخدم جديد',
                    'POST /auth/login' => 'تسجيل الدخول',
                    'POST /auth/logout' => 'تسجيل الخروج',
                    'GET /auth/profile' => 'الحصول على الملف الشخصي',
                    'PUT /auth/profile' => 'تحديث الملف الشخصي',
                    'PUT /auth/password' => 'تغيير كلمة المرور'
                ],
                'books' => [
                    'GET /books' => 'الحصول على قائمة الكتب',
                    'GET /books/{id}' => 'الحصول على كتاب واحد',
                    'POST /books' => 'إضافة كتاب جديد (مدير)',
                    'PUT /books/{id}' => 'تحديث كتاب (مدير)',
                    'DELETE /books/{id}' => 'حذف كتاب (مدير)',
                    'POST /books/{id}/rate' => 'تقييم كتاب',
                    'POST /books/{id}/download' => 'تحميل كتاب'
                ],
                'categories' => [
                    'GET /categories' => 'الحصول على التصنيفات'
                ]
            ]
        ]);
    }
    
    $endpoint = $pathParts[0];
    
    switch ($endpoint) {
        case 'auth':
            handleAuthRoutes($pathParts, $requestMethod, $inputData);
            break;
            
        case 'books':
            handleBookRoutes($pathParts, $requestMethod, $inputData);
            break;
            
        case 'categories':
            handleCategoryRoutes($pathParts, $requestMethod, $inputData);
            break;
            
        default:
            Response::notFound('نقطة النهاية غير موجودة');
    }
    
} catch (Exception $e) {
    if (DEBUG_MODE) {
        Response::serverError('خطأ: ' . $e->getMessage());
    } else {
        Response::serverError('حدث خطأ في الخادم');
    }
}

/**
 * معالجة طلبات المصادقة
 */
function handleAuthRoutes($pathParts, $method, $data) {
    $user = new User();
    
    if (count($pathParts) < 2) {
        Response::notFound('نقطة النهاية غير موجودة');
    }
    
    $action = $pathParts[1];
    
    switch ($action) {
        case 'register':
            if ($method !== 'POST') {
                Response::error('طريقة غير مدعومة', 405);
            }
            $user->register($data);
            break;
            
        case 'login':
            if ($method !== 'POST') {
                Response::error('طريقة غير مدعومة', 405);
            }
            $user->login($data);
            break;
            
        case 'logout':
            if ($method !== 'POST') {
                Response::error('طريقة غير مدعومة', 405);
            }
            $user->logout();
            break;
            
        case 'profile':
            if ($method === 'GET') {
                $user->getProfile();
            } elseif ($method === 'PUT') {
                $user->updateProfile($data);
            } else {
                Response::error('طريقة غير مدعومة', 405);
            }
            break;
            
        case 'password':
            if ($method !== 'PUT') {
                Response::error('طريقة غير مدعومة', 405);
            }
            $user->changePassword($data);
            break;
            
        default:
            Response::notFound('نقطة النهاية غير موجودة');
    }
}

/**
 * معالجة طلبات الكتب
 */
function handleBookRoutes($pathParts, $method, $data) {
    $book = new Book();
    
    if (count($pathParts) === 1) {
        // /books
        if ($method === 'GET') {
            $book->getBooks($data);
        } elseif ($method === 'POST') {
            $book->addBook($data, $_FILES);
        } else {
            Response::error('طريقة غير مدعومة', 405);
        }
    } elseif (count($pathParts) >= 2) {
        $bookId = (int)$pathParts[1];
        
        if (count($pathParts) === 2) {
            // /books/{id}
            if ($method === 'GET') {
                $book->getBook($bookId);
            } elseif ($method === 'PUT') {
                $book->updateBook($bookId, $data);
            } elseif ($method === 'DELETE') {
                $book->deleteBook($bookId);
            } else {
                Response::error('طريقة غير مدعومة', 405);
            }
        } elseif (count($pathParts) === 3) {
            $action = $pathParts[2];
            
            switch ($action) {
                case 'rate':
                    if ($method !== 'POST') {
                        Response::error('طريقة غير مدعومة', 405);
                    }
                    $book->rateBook($bookId, $data);
                    break;
                    
                case 'download':
                    if ($method !== 'POST') {
                        Response::error('طريقة غير مدعومة', 405);
                    }
                    $book->downloadBook($bookId);
                    break;
                    
                default:
                    Response::notFound('نقطة النهاية غير موجودة');
            }
        } else {
            Response::notFound('نقطة النهاية غير موجودة');
        }
    } else {
        Response::notFound('نقطة النهاية غير موجودة');
    }
}

/**
 * معالجة طلبات التصنيفات
 */
function handleCategoryRoutes($pathParts, $method, $data) {
    if ($method !== 'GET') {
        Response::error('طريقة غير مدعومة', 405);
    }
    
    $db = getDB();
    
    try {
        $categories = $db->fetchAll(
            "SELECT id, name, name_en, slug, description, icon, color, 
                    (SELECT COUNT(*) FROM books WHERE category_id = categories.id AND status = 'active') as books_count
             FROM categories 
             WHERE is_active = 1 
             ORDER BY sort_order ASC, name ASC"
        );
        
        Response::success($categories);
        
    } catch (Exception $e) {
        Response::serverError('خطأ في جلب التصنيفات');
    }
}
?>
