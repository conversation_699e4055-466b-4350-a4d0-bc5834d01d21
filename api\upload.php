<?php
/**
 * نقطة نهاية رفع الملفات - مكتبتي الحرة
 * File Upload Endpoint - My Free Library
 */

// إعداد الترميز والأخطاء
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// التعامل مع طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// تضمين الملفات المطلوبة
require_once '../database/config.php';
require_once 'classes/Response.php';
require_once 'classes/FileUpload.php';

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    Response::error('طريقة غير مدعومة', 405);
}

try {
    // التحقق من المصادقة للمديرين فقط
    $user = Auth::requireAdmin();
    
    // إنشاء مثيل رفع الملفات
    $fileUpload = new FileUpload();
    
    // الحصول على نوع الرفع
    $uploadType = $_POST['type'] ?? 'book';
    
    $uploadedFiles = [];
    $errors = [];
    
    switch ($uploadType) {
        case 'book':
            // رفع ملف كتاب
            if (!isset($_FILES['book_file'])) {
                Response::error('ملف الكتاب مطلوب', 400);
            }
            
            try {
                $result = $fileUpload->uploadBookFile($_FILES['book_file']);
                $uploadedFiles['book_file'] = $result;
            } catch (Exception $e) {
                $errors['book_file'] = $e->getMessage();
            }
            
            // رفع غلاف الكتاب (اختياري)
            if (isset($_FILES['cover_image']) && $_FILES['cover_image']['error'] !== UPLOAD_ERR_NO_FILE) {
                try {
                    $result = $fileUpload->uploadCoverImage($_FILES['cover_image']);
                    $uploadedFiles['cover_image'] = $result;
                } catch (Exception $e) {
                    $errors['cover_image'] = $e->getMessage();
                }
            }
            break;
            
        case 'cover':
            // رفع غلاف فقط
            if (!isset($_FILES['cover_image'])) {
                Response::error('صورة الغلاف مطلوبة', 400);
            }
            
            try {
                $result = $fileUpload->uploadCoverImage($_FILES['cover_image']);
                $uploadedFiles['cover_image'] = $result;
            } catch (Exception $e) {
                $errors['cover_image'] = $e->getMessage();
            }
            break;
            
        case 'avatar':
            // رفع صورة شخصية
            if (!isset($_FILES['avatar'])) {
                Response::error('الصورة الشخصية مطلوبة', 400);
            }
            
            try {
                $result = $fileUpload->uploadAvatar($_FILES['avatar']);
                $uploadedFiles['avatar'] = $result;
            } catch (Exception $e) {
                $errors['avatar'] = $e->getMessage();
            }
            break;
            
        case 'multiple':
            // رفع متعدد
            foreach ($_FILES as $fieldName => $file) {
                if ($file['error'] === UPLOAD_ERR_NO_FILE) {
                    continue;
                }
                
                try {
                    if (strpos($fieldName, 'book') !== false) {
                        $result = $fileUpload->uploadBookFile($file);
                    } elseif (strpos($fieldName, 'cover') !== false) {
                        $result = $fileUpload->uploadCoverImage($file);
                    } elseif (strpos($fieldName, 'avatar') !== false) {
                        $result = $fileUpload->uploadAvatar($file);
                    } else {
                        continue;
                    }
                    
                    $uploadedFiles[$fieldName] = $result;
                } catch (Exception $e) {
                    $errors[$fieldName] = $e->getMessage();
                }
            }
            break;
            
        default:
            Response::error('نوع الرفع غير مدعوم', 400);
    }
    
    // التحقق من النتائج
    if (empty($uploadedFiles) && !empty($errors)) {
        Response::validationError($errors, 'فشل في رفع الملفات');
    }
    
    // تسجيل النشاط
    $db = getDB();
    $db->insert(
        "INSERT INTO activity_logs (user_id, action, entity_type, entity_id, details, ip_address, user_agent) 
         VALUES (?, ?, ?, ?, ?, ?, ?)",
        [
            $user['id'],
            'files_uploaded',
            'files',
            null,
            json_encode([
                'upload_type' => $uploadType,
                'files_count' => count($uploadedFiles),
                'files' => array_keys($uploadedFiles)
            ]),
            getClientIP(),
            getUserAgent()
        ]
    );
    
    $response = [
        'uploaded_files' => $uploadedFiles
    ];
    
    if (!empty($errors)) {
        $response['errors'] = $errors;
    }
    
    Response::success($response, 'تم رفع الملفات بنجاح', 201);
    
} catch (Exception $e) {
    if (DEBUG_MODE) {
        Response::serverError('خطأ في رفع الملفات: ' . $e->getMessage());
    } else {
        Response::serverError('خطأ في رفع الملفات');
    }
}

/**
 * نقطة نهاية إدارة الملفات
 */
if (isset($_GET['action'])) {
    $action = $_GET['action'];
    
    switch ($action) {
        case 'delete':
            handleFileDelete();
            break;
            
        case 'info':
            handleFileInfo();
            break;
            
        case 'list':
            handleFileList();
            break;
            
        default:
            Response::error('إجراء غير مدعوم', 400);
    }
}

/**
 * حذف ملف
 */
function handleFileDelete() {
    $user = Auth::requireAdmin();
    
    $filename = $_GET['filename'] ?? '';
    $type = $_GET['type'] ?? 'book';
    
    if (empty($filename)) {
        Response::error('اسم الملف مطلوب', 400);
    }
    
    try {
        $fileUpload = new FileUpload();
        $deleted = $fileUpload->deleteFile($filename, $type);
        
        if ($deleted) {
            // تسجيل النشاط
            $db = getDB();
            $db->insert(
                "INSERT INTO activity_logs (user_id, action, entity_type, entity_id, details, ip_address, user_agent) 
                 VALUES (?, ?, ?, ?, ?, ?, ?)",
                [
                    $user['id'],
                    'file_deleted',
                    'files',
                    null,
                    json_encode(['filename' => $filename, 'type' => $type]),
                    getClientIP(),
                    getUserAgent()
                ]
            );
            
            Response::success(null, 'تم حذف الملف بنجاح');
        } else {
            Response::notFound('الملف غير موجود');
        }
        
    } catch (Exception $e) {
        Response::serverError('خطأ في حذف الملف');
    }
}

/**
 * معلومات الملف
 */
function handleFileInfo() {
    Auth::requireAuth(); // يمكن للمستخدمين العاديين رؤية معلومات الملفات
    
    $filename = $_GET['filename'] ?? '';
    $type = $_GET['type'] ?? 'book';
    
    if (empty($filename)) {
        Response::error('اسم الملف مطلوب', 400);
    }
    
    try {
        $fileUpload = new FileUpload();
        $fileInfo = $fileUpload->getFileInfo($filename, $type);
        
        if ($fileInfo) {
            Response::success($fileInfo);
        } else {
            Response::notFound('الملف غير موجود');
        }
        
    } catch (Exception $e) {
        Response::serverError('خطأ في جلب معلومات الملف');
    }
}

/**
 * قائمة الملفات
 */
function handleFileList() {
    $user = Auth::requireAdmin();
    
    $type = $_GET['type'] ?? 'book';
    $page = (int)($_GET['page'] ?? 1);
    $limit = min(50, (int)($_GET['limit'] ?? 20));
    
    try {
        $db = getDB();
        
        // تحديد الجدول والحقول حسب النوع
        switch ($type) {
            case 'book':
                $query = "SELECT file_path as filename, title, author, file_size, created_at 
                         FROM books WHERE status = 'active' AND file_path IS NOT NULL";
                break;
            case 'cover':
                $query = "SELECT cover_image as filename, title, author, created_at 
                         FROM books WHERE status = 'active' AND cover_image IS NOT NULL";
                break;
            default:
                Response::error('نوع الملف غير مدعوم', 400);
        }
        
        // حساب العدد الكلي
        $totalQuery = str_replace('SELECT file_path as filename, title, author, file_size, created_at', 'SELECT COUNT(*) as total', $query);
        $total = $db->fetch($totalQuery)['total'];
        
        // إنشاء الترقيم
        $paginator = new Paginator($page, $limit, $total);
        
        // الاستعلام الرئيسي
        $query .= " ORDER BY created_at DESC LIMIT ? OFFSET ?";
        $files = $db->fetchAll($query, [$paginator->getLimit(), $paginator->getOffset()]);
        
        Response::paginated($files, $paginator->getPaginationInfo());
        
    } catch (Exception $e) {
        Response::serverError('خطأ في جلب قائمة الملفات');
    }
}
?>
