/* لوحة التحكم - تصميم مخصص */

/* الشريط الجانبي */
.sidebar {
    position: fixed;
    top: 56px;
    bottom: 0;
    right: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    overflow-y: auto;
}

[data-theme="dark"] .sidebar {
    background-color: #2d2d2d !important;
    box-shadow: inset -1px 0 0 rgba(255, 255, 255, .1);
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
    padding: 12px 20px;
    border-radius: 8px;
    margin: 2px 10px;
    transition: all 0.3s ease;
}

[data-theme="dark"] .sidebar .nav-link {
    color: #ffffff;
}

.sidebar .nav-link:hover {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
    transform: translateX(-3px);
}

.sidebar .nav-link.active {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.15);
    border-right: 3px solid #007bff;
}

[data-theme="dark"] .sidebar .nav-link:hover,
[data-theme="dark"] .sidebar .nav-link.active {
    background-color: rgba(0, 123, 255, 0.2);
}

/* المحتوى الرئيسي */
main {
    margin-top: 56px;
    padding-top: 20px;
}

.content-section {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* بطاقات الإحصائيات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.25);
}

[data-theme="dark"] .card {
    background-color: #2d2d2d;
    color: white;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(0, 0, 0, 0.3);
}

/* بطاقات الإحصائيات الملونة */
.border-left-primary {
    border-right: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-right: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-right: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-right: 0.25rem solid #f6c23e !important;
}

/* النصوص */
.text-xs {
    font-size: 0.7rem;
}

.font-weight-bold {
    font-weight: 700 !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

[data-theme="dark"] .text-gray-800 {
    color: #ffffff !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

[data-theme="dark"] .text-gray-300 {
    color: #6c757d !important;
}

/* الجداول */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    font-weight: 600;
    color: #5a5c69;
    padding: 15px;
}

[data-theme="dark"] .table thead th {
    background-color: #1a1a1a;
    border-bottom-color: #404040;
    color: #ffffff;
}

.table tbody td {
    padding: 15px;
    vertical-align: middle;
    border-bottom: 1px solid #e3e6f0;
}

[data-theme="dark"] .table tbody td {
    border-bottom-color: #404040;
    color: #ffffff;
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

[data-theme="dark"] .table tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* الأزرار */
.btn {
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn-sm {
    border-radius: 15px;
}

/* النماذج */
.form-control, .form-select {
    border-radius: 10px;
    border: 1px solid #d1d3e2;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
    background-color: #2d2d2d;
    border-color: #404040;
    color: white;
}

[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus {
    background-color: #2d2d2d;
    border-color: #4e73df;
    color: white;
}

/* الشارات */
.badge {
    border-radius: 10px;
    padding: 8px 12px;
    font-weight: 600;
}

/* المودال */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 1rem 3rem rgba(0,0,0,.175);
}

[data-theme="dark"] .modal-content {
    background-color: #2d2d2d;
    color: white;
}

.modal-header {
    border-bottom: 1px solid #e3e6f0;
    border-radius: 15px 15px 0 0;
}

[data-theme="dark"] .modal-header {
    border-bottom-color: #404040;
}

.modal-footer {
    border-top: 1px solid #e3e6f0;
    border-radius: 0 0 15px 15px;
}

[data-theme="dark"] .modal-footer {
    border-top-color: #404040;
}

/* الرسوم البيانية */
.chart-container {
    position: relative;
    height: 300px;
    margin: 20px 0;
}

/* تأثيرات الحركة */
.fade-in {
    animation: fadeIn 0.6s ease-in;
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* تحسينات للجوال */
@media (max-width: 768px) {
    .sidebar {
        position: static;
        height: auto;
        padding: 0;
    }
    
    main {
        margin-top: 0;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-toolbar {
        flex-direction: column;
        gap: 10px;
    }
    
    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 15px;
    }
}

/* تحسينات إضافية */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: 15px;
}

[data-theme="dark"] .loading-overlay {
    background-color: rgba(0, 0, 0, 0.8);
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* تصميم البحث */
.search-highlight {
    background-color: rgba(255, 255, 0, 0.3);
    padding: 2px 4px;
    border-radius: 3px;
}

/* تحسين الأيقونات */
.nav-link i {
    width: 20px;
    text-align: center;
}

/* تأثيرات التمرير */
.scroll-to-top {
    position: fixed;
    bottom: 20px;
    left: 20px;
    width: 50px;
    height: 50px;
    background-color: #4e73df;
    color: white;
    border: none;
    border-radius: 50%;
    display: none;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 1000;
}

.scroll-to-top:hover {
    background-color: #2e59d9;
    transform: translateY(-3px);
}

.scroll-to-top.show {
    display: flex;
}

/* تحسين عرض الصور */
.book-cover-thumb {
    width: 40px;
    height: 60px;
    object-fit: cover;
    border-radius: 5px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* تحسين الإشعارات */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* تحسين القوائم المنسدلة */
.dropdown-menu {
    border-radius: 10px;
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

[data-theme="dark"] .dropdown-menu {
    background-color: #2d2d2d;
    color: white;
}

.dropdown-item {
    border-radius: 5px;
    margin: 2px 5px;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background-color: rgba(0, 123, 255, 0.1);
    transform: translateX(-3px);
}

[data-theme="dark"] .dropdown-item {
    color: white;
}

[data-theme="dark"] .dropdown-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}
