/* ملف CSS الاحترافي المتقدم */

/* ===============================
   متغيرات الألوان الاحترافية
   =============================== */
:root {
    /* الألوان الأساسية */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;
    
    /* الألوان الثانوية */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* ألوان الحالة */
    --success-500: #10b981;
    --warning-500: #f59e0b;
    --danger-500: #ef4444;
    --info-500: #06b6d4;
    
    /* التدرجات الاحترافية */
    --gradient-primary: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-800) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--gray-600) 0%, var(--gray-800) 100%);
    --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    
    /* الظلال الاحترافية */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-colored: 0 10px 40px rgba(59, 130, 246, 0.15);
    --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);
    
    /* المسافات */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    
    /* الحدود */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-full: 9999px;
    
    /* الانتقالات */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: 400ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* ===============================
   التحسينات الأساسية
   =============================== */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--gray-900);
    background: var(--gray-50);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===============================
   شريط التنقل الاحترافي
   =============================== */
.navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--gray-200);
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
    padding: var(--space-4) 0;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98) !important;
    box-shadow: var(--shadow-xl);
    padding: var(--space-2) 0;
}

.navbar-brand {
    font-weight: 800;
    font-size: 1.5rem;
    color: var(--primary-600);
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.navbar-brand:hover {
    color: var(--primary-700);
    transform: scale(1.05);
}

.brand-icon {
    width: 32px;
    height: 32px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.125rem;
    box-shadow: var(--shadow-md);
}

.nav-link {
    position: relative;
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--gray-600) !important;
    padding: var(--space-2) var(--space-4) !important;
    border-radius: var(--radius-lg);
    transition: var(--transition-normal);
    margin: 0 var(--space-1);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    transition: var(--transition-normal);
    transform: translateX(-50%);
}

.nav-link:hover::before,
.nav-link.active::before {
    width: 80%;
}

.nav-link:hover {
    color: var(--primary-600) !important;
    background: rgba(59, 130, 246, 0.05);
    transform: translateY(-1px);
}

.nav-link.active {
    color: var(--primary-600) !important;
    background: rgba(59, 130, 246, 0.1);
}

.nav-link i {
    font-size: 0.875rem;
    transition: var(--transition-normal);
}

.nav-link:hover i {
    transform: scale(1.1);
}

/* ===============================
   القوائم المنسدلة الاحترافية
   =============================== */
.dropdown-menu {
    border: none;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-2xl);
    padding: var(--space-2);
    margin-top: var(--space-2);
    background: white;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.dropdown-item {
    border-radius: var(--radius-md);
    padding: var(--space-3) var(--space-4);
    transition: var(--transition-fast);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.dropdown-item:hover {
    background: var(--gray-100);
    color: var(--primary-600);
    transform: translateX(4px);
}

.dropdown-header {
    font-weight: 700;
    color: var(--gray-700);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: var(--space-2) var(--space-4);
}

/* ===============================
   الأزرار الاحترافية
   =============================== */
.btn {
    font-weight: 600;
    border-radius: var(--radius-lg);
    padding: var(--space-3) var(--space-6);
    transition: var(--transition-bounce);
    border: none;
    position: relative;
    overflow: hidden;
    text-transform: none;
    letter-spacing: 0.025em;
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transition: var(--transition-normal);
    transform: translate(-50%, -50%);
    z-index: 0;
}

.btn:hover::before {
    width: 300px;
    height: 300px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn span, .btn i {
    position: relative;
    z-index: 1;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    background: var(--gradient-primary);
    filter: brightness(1.1);
    color: white;
    box-shadow: var(--shadow-colored);
}

.btn-outline-primary {
    border: 2px solid var(--primary-600);
    color: var(--primary-600);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--gradient-primary);
    border-color: transparent;
    color: white;
}

.btn-success {
    background: var(--gradient-success);
    color: white;
}

.btn-outline-secondary {
    border: 2px solid var(--gray-300);
    color: var(--gray-600);
    background: transparent;
}

.btn-outline-secondary:hover {
    background: var(--gray-600);
    border-color: transparent;
    color: white;
}

/* ===============================
   النماذج الاحترافية
   =============================== */
.form-control, .form-select {
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--space-3) var(--space-4);
    font-size: 1rem;
    transition: var(--transition-normal);
    background: white;
    box-shadow: var(--shadow-xs);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    transform: translateY(-1px);
}

.form-control:hover, .form-select:hover {
    border-color: var(--primary-400);
    box-shadow: var(--shadow-sm);
}

.input-group {
    box-shadow: var(--shadow-sm);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.input-group .form-control {
    border: none;
    box-shadow: none;
}

.input-group .btn {
    border: none;
    border-radius: 0;
}

/* ===============================
   البطاقات الاحترافية
   =============================== */
.card {
    border: none;
    border-radius: var(--radius-2xl);
    background: white;
    box-shadow: var(--shadow-md);
    transition: var(--transition-bounce);
    overflow: hidden;
    position: relative;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: var(--transition-normal);
    transform-origin: left;
}

.card:hover::before {
    transform: scaleX(1);
}

.card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-2xl);
    border-color: rgba(59, 130, 246, 0.3);
}

.card-body {
    padding: var(--space-6);
}

.card-title {
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-3);
}

.card-text {
    color: var(--gray-600);
    line-height: 1.6;
}

/* ===============================
   الشارات الاحترافية
   =============================== */
.badge {
    font-weight: 600;
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    box-shadow: var(--shadow-sm);
}

.badge.bg-success {
    background: var(--gradient-success) !important;
}

.badge.bg-warning {
    background: var(--gradient-warning) !important;
}

.badge.bg-primary {
    background: var(--gradient-primary) !important;
}

/* ===============================
   تحسينات التجاوب
   =============================== */
@media (max-width: 768px) {
    .navbar {
        padding: var(--space-2) 0;
    }
    
    .navbar-brand {
        font-size: 1.25rem;
    }
    
    .brand-icon {
        width: 28px;
        height: 28px;
        font-size: 1rem;
    }
    
    .nav-link {
        padding: var(--space-3) var(--space-4) !important;
        margin: var(--space-1) 0;
    }
    
    .btn {
        padding: var(--space-2) var(--space-4);
        font-size: 0.875rem;
    }
    
    .card {
        margin-bottom: var(--space-4);
    }
    
    .card:hover {
        transform: translateY(-4px) scale(1.01);
    }
}
