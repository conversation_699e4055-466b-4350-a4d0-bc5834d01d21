/* قارئ الكتب - تصميم مخصص */

/* إعدادات أساسية للقارئ */
.reader-body {
    margin: 0;
    padding: 0;
    overflow: hidden;
    height: 100vh;
    background-color: #f8f9fa;
}

[data-theme="dark"] .reader-body {
    background-color: #1a1a1a;
}

/* رأس القارئ */
.reader-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
}

.reader-header .form-control {
    background-color: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: white;
}

.reader-header .form-control:focus {
    background-color: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.3);
    color: white;
    box-shadow: none;
}

/* محتوى القارئ */
.reader-content {
    margin-top: 60px;
    height: calc(100vh - 60px);
    position: relative;
}

/* الشريط الجانبي */
.reader-sidebar {
    border-left: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

[data-theme="dark"] .reader-sidebar {
    border-left-color: #404040;
}

.sidebar-content {
    overflow-y: auto;
}

[data-theme="dark"] .sidebar-content {
    background-color: #2d2d2d !important;
    color: white;
}

/* المنطقة الرئيسية للقارئ */
.reader-main {
    transition: all 0.3s ease;
}

.reader-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

[data-theme="dark"] .reader-container {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

/* حاوي الكتاب المقلب */
.flipbook-container {
    position: relative;
    max-width: 800px;
    max-height: 600px;
    margin: 0 auto;
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
    border-radius: 10px;
    overflow: hidden;
}

/* صفحات الكتاب */
.page {
    background-color: white;
    border: 1px solid #ddd;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: relative;
}

[data-theme="dark"] .page {
    background-color: #2d2d2d;
    border-color: #404040;
    color: white;
}

.page-content {
    padding: 20px;
    height: 100%;
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    font-size: 16px;
}

/* تأثيرات تقليب الصفحات */
.flipbook-container .turn-page {
    background-color: white;
    background-image: linear-gradient(90deg, rgba(0,0,0,0.05) 0%, transparent 5%);
}

[data-theme="dark"] .flipbook-container .turn-page {
    background-color: #2d2d2d;
    background-image: linear-gradient(90deg, rgba(255,255,255,0.05) 0%, transparent 5%);
}

/* عارض PDF البديل */
.pdf-viewer {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: auto;
}

#pdfCanvas {
    max-width: 100%;
    max-height: 100%;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    border-radius: 5px;
}

/* شريط التقدم */
.reading-progress {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 999;
}

.progress-bar {
    transition: width 0.3s ease;
}

/* أزرار التحكم */
.reader-controls .btn {
    border-radius: 20px;
    transition: all 0.3s ease;
}

.reader-controls .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.zoom-controls {
    background-color: rgba(255,255,255,0.1);
    border-radius: 20px;
    padding: 5px 10px;
}

/* قائمة المحتويات */
.contents-list ul {
    max-height: 300px;
    overflow-y: auto;
}

.contents-list a {
    display: block;
    padding: 8px 12px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.contents-list a:hover {
    background-color: rgba(0,123,255,0.1);
    transform: translateX(-5px);
}

[data-theme="dark"] .contents-list a:hover {
    background-color: rgba(255,255,255,0.1);
}

/* العلامات المرجعية */
.bookmark-item {
    padding: 10px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: all 0.3s ease;
}

.bookmark-item:hover {
    background-color: #f8f9fa;
    transform: translateX(-3px);
}

[data-theme="dark"] .bookmark-item {
    border-bottom-color: #404040;
}

[data-theme="dark"] .bookmark-item:hover {
    background-color: rgba(255,255,255,0.05);
}

.bookmark-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

[data-theme="dark"] .bookmark-title {
    color: white;
}

.bookmark-page {
    font-size: 0.8rem;
    color: #666;
}

[data-theme="dark"] .bookmark-page {
    color: #adb5bd;
}

/* الملاحظات */
.note-item {
    padding: 10px;
    border: 1px solid #eee;
    border-radius: 5px;
    margin-bottom: 10px;
    background-color: #fffbf0;
}

[data-theme="dark"] .note-item {
    border-color: #404040;
    background-color: #2d2d2d;
}

.note-content {
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 5px;
}

.note-meta {
    font-size: 0.7rem;
    color: #666;
}

[data-theme="dark"] .note-meta {
    color: #adb5bd;
}

/* تأثيرات الحركة */
.page-turn-effect {
    animation: pageTurn 0.6s ease-in-out;
}

@keyframes pageTurn {
    0% { transform: rotateY(0deg); }
    50% { transform: rotateY(-90deg); }
    100% { transform: rotateY(0deg); }
}

/* وضع ملء الشاشة */
.fullscreen-mode {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    background-color: #000;
}

.fullscreen-mode .reader-header {
    background-color: rgba(0,0,0,0.8);
}

.fullscreen-mode .flipbook-container {
    max-width: 90vw;
    max-height: 80vh;
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .reader-header .col-md-6 {
        order: 3;
        margin-top: 10px;
    }
    
    .reader-controls {
        flex-wrap: wrap;
        gap: 5px;
    }
    
    .zoom-controls {
        font-size: 0.8rem;
    }
    
    .flipbook-container {
        max-width: 95vw;
        max-height: 70vh;
    }
    
    .page-content {
        padding: 15px;
        font-size: 14px;
    }
    
    .reader-sidebar {
        position: fixed;
        top: 60px;
        left: -100%;
        width: 280px;
        height: calc(100vh - 60px);
        z-index: 1001;
        transition: left 0.3s ease;
    }
    
    .reader-sidebar.show {
        left: 0;
    }
}

@media (max-width: 576px) {
    .reader-header .row {
        flex-direction: column;
        gap: 10px;
    }
    
    .reader-options {
        justify-content: center !important;
    }
    
    .flipbook-container {
        max-width: 100vw;
        max-height: 60vh;
    }
}

/* تحسينات إضافية */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255,255,255,0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

[data-theme="dark"] .loading-overlay {
    background-color: rgba(0,0,0,0.9);
}

.page-number {
    position: absolute;
    bottom: 10px;
    right: 20px;
    font-size: 0.8rem;
    color: #666;
}

[data-theme="dark"] .page-number {
    color: #adb5bd;
}

/* تأثيرات الإضاءة */
.page-glow {
    box-shadow: 0 0 30px rgba(74, 144, 226, 0.3);
}

/* تحسين الخطوط */
.page-content h1, .page-content h2, .page-content h3 {
    color: #2c3e50;
    margin-bottom: 15px;
}

[data-theme="dark"] .page-content h1,
[data-theme="dark"] .page-content h2,
[data-theme="dark"] .page-content h3 {
    color: #ecf0f1;
}

.page-content p {
    text-align: justify;
    margin-bottom: 12px;
}

/* تأثير التمييز */
.highlight {
    background-color: rgba(255, 255, 0, 0.3);
    padding: 2px 4px;
    border-radius: 3px;
}

[data-theme="dark"] .highlight {
    background-color: rgba(255, 255, 0, 0.2);
}
