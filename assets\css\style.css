/* مكتبتي الحرة - ملف التصميم الرئيسي */

/* الخطوط والإعدادات الأساسية */
* {
    font-family: 'Cairo', sans-serif;
    box-sizing: border-box;
}

body {
    direction: rtl;
    text-align: right;
    overflow-x: hidden;
    position: relative;
    /* إزالة جميع الانتقالات لضمان الاستقرار */
    transition: none !important;
    animation: none !important;
}

/* منع الحركة غير المرغوب فيها */
*, *::before, *::after {
    animation: none !important;
    transition: none !important;
}

/* استثناءات للانتقالات المطلوبة فقط */
.btn, .card:hover, .nav-link:hover, .dropdown-menu {
    transition: all 0.2s ease !important;
}

/* ضمان عدم التمرير الأفقي */
html, body {
    max-width: 100%;
    overflow-x: hidden;
}

.container, .container-fluid {
    max-width: 100%;
    overflow-x: hidden;
}

.row {
    margin-left: 0;
    margin-right: 0;
}

/* متغيرات الألوان المحسنة */
:root {
    /* الألوان الأساسية */
    --primary-color: #667eea;
    --primary-dark: #5a67d8;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    --success-color: #48bb78;
    --danger-color: #f56565;
    --warning-color: #ed8936;
    --info-color: #4299e1;

    /* التدرجات الجميلة */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --elegant-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);

    /* الخلفيات */
    --bg-light: #ffffff;
    --bg-dark: #1a202c;
    --bg-gradient: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-border: rgba(255, 255, 255, 0.18);

    /* النصوص */
    --text-primary: #2d3748;
    --text-secondary: #4a5568;
    --text-muted: #718096;
    --text-light: #ffffff;
    --text-dark: #1a202c;

    /* الظلال الجميلة */
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.05);
    --shadow-md: 0 4px 12px rgba(0,0,0,0.08);
    --shadow-lg: 0 8px 32px rgba(0,0,0,0.12);
    --shadow-xl: 0 20px 60px rgba(0,0,0,0.15);
    --shadow-elegant: 0 10px 40px rgba(103, 126, 234, 0.15);
    --shadow-hover: 0 20px 60px rgba(103, 126, 234, 0.25);
    --shadow-glow: 0 0 20px rgba(103, 126, 234, 0.3);

    /* الحدود */
    --border-radius-sm: 8px;
    --border-radius-md: 12px;
    --border-radius-lg: 20px;
    --border-radius-xl: 24px;
    --border-radius-full: 50px;

    /* المسافات */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* الانتقالات */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    --transition-bounce: 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* الوضع الليلي */
[data-theme="dark"] {
    --bg-light: #1a1a1a;
    --bg-dark: #000000;
    --text-light: #ffffff;
    --text-dark: #333333;
    --card-bg-light: #2d2d2d;
    --card-bg-dark: #1a1a1a;
    --border-light: #404040;
    --border-dark: #dee2e6;
}

[data-theme="dark"] body {
    background-color: var(--bg-light);
    color: var(--text-light);
}

[data-theme="dark"] .navbar-light {
    background-color: var(--card-bg-light) !important;
    border-bottom: 1px solid var(--border-light);
}

[data-theme="dark"] .navbar-light .navbar-brand,
[data-theme="dark"] .navbar-light .nav-link {
    color: var(--text-light) !important;
}

[data-theme="dark"] .bg-light {
    background-color: var(--card-bg-light) !important;
}

[data-theme="dark"] .card {
    background-color: var(--card-bg-light);
    border-color: var(--border-light);
    color: var(--text-light);
}

[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
    background-color: var(--card-bg-light);
    border-color: var(--border-light);
    color: var(--text-light);
}

[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus {
    background-color: var(--card-bg-light);
    border-color: var(--primary-color);
    color: var(--text-light);
    box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
}

/* تدرج الألوان للقسم الرئيسي */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #667eea 100%);
}

/* تصميم القسم الرئيسي */
.hero-section {
    min-height: 500px;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,255,0.1)"><polygon points="1000,100 1000,0 0,100"/></svg>');
    background-size: cover;
}

.hero-image {
    max-width: 400px;
    filter: drop-shadow(0 10px 20px rgba(0,0,0,0.2));
    /* إزالة الانيميشن المستمر لتجنب عدم الاستقرار */
}

/* إزالة keyframes غير المستخدم */

/* تصميم مربع البحث */
.search-box {
    border-radius: 15px;
    border: 1px solid var(--border-light);
    transition: box-shadow 0.2s ease;
}

.search-box:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* تصميم البطاقات الرائع */
.card {
    border: none;
    border-radius: var(--border-radius-lg);
    background: var(--bg-light);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-bounce);
    overflow: hidden;
    position: relative;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
    transform-origin: left;
}

.card:hover::before {
    transform: scaleX(1);
}

.card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-hover);
    border-color: rgba(103, 126, 234, 0.3);
}

/* تحسينات بطاقات الكتب الرائعة */
.book-card {
    position: relative;
    transition: all var(--transition-bounce);
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.book-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-gradient);
    opacity: 0;
    transition: opacity var(--transition-normal);
    z-index: 1;
}

.book-card:hover::after {
    opacity: 0.05;
}

.book-card:hover {
    transform: translateY(-12px) rotateX(5deg);
    box-shadow: var(--shadow-xl);
}

.book-card .position-relative {
    overflow: hidden;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.book-card img {
    transition: all var(--transition-slow);
    object-fit: cover;
    width: 100%;
    height: 250px;
}

.book-card:hover img {
    transform: scale(1.1) rotate(2deg);
    filter: brightness(1.1) saturate(1.2);
}

.book-card .card-body {
    padding: var(--spacing-xl);
    position: relative;
    z-index: 2;
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
}

.book-card .card-title {
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    transition: color var(--transition-normal);
}

.book-card:hover .card-title {
    color: var(--primary-color);
}

.book-card .card-text {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 1.6;
}

.book-card .rating {
    font-size: 0.9rem;
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.book-card .rating .fa-star {
    color: #fbbf24;
    filter: drop-shadow(0 1px 2px rgba(251, 191, 36, 0.3));
    transition: all var(--transition-fast);
}

.book-card .rating .fa-star:hover {
    transform: scale(1.2);
    color: #f59e0b;
}

.book-card .btn {
    font-size: 0.85rem;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-full);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all var(--transition-bounce);
    position: relative;
    overflow: hidden;
    border: none;
}

.book-card .btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transition: all var(--transition-normal);
    transform: translate(-50%, -50%);
}

.book-card .btn:hover::before {
    width: 200px;
    height: 200px;
}

.book-card .btn-primary {
    background: var(--primary-gradient);
    box-shadow: var(--shadow-md);
}

.book-card .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-elegant);
}

.book-card .btn-outline-success {
    border: 2px solid var(--success-color);
    color: var(--success-color);
    background: transparent;
}

.book-card .btn-outline-success:hover {
    background: var(--success-gradient);
    border-color: transparent;
    color: white;
    transform: translateY(-2px);
}

.book-card .btn-outline-danger {
    border: 2px solid var(--danger-color);
    color: var(--danger-color);
    background: transparent;
}

.book-card .btn-outline-danger:hover {
    background: var(--danger-color);
    border-color: transparent;
    color: white;
    transform: translateY(-2px);
}

.book-card .btn-sm {
    font-size: 0.75rem;
    padding: var(--spacing-xs) var(--spacing-sm);
}

/* شارات الكتب */
.book-card .badge {
    font-size: 0.75rem;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-full);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.book-card .badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left var(--transition-normal);
}

.book-card .badge:hover::before {
    left: 100%;
}

.badge.bg-success {
    background: var(--success-gradient) !important;
}

.badge.bg-warning {
    background: var(--warning-gradient) !important;
}

.badge.bg-primary {
    background: var(--primary-gradient) !important;
}

.badge.bg-info {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%) !important;
}

.badge.bg-secondary {
    background: var(--dark-gradient) !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%) !important;
}

/* تحسينات الأزرار العامة */
.btn {
    border-radius: var(--border-radius-full);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all var(--transition-bounce);
    position: relative;
    overflow: hidden;
    border: none;
    box-shadow: var(--shadow-md);
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transition: all var(--transition-normal);
    transform: translate(-50%, -50%);
    z-index: 0;
}

.btn:hover::before {
    width: 300px;
    height: 300px;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

.btn span, .btn i {
    position: relative;
    z-index: 1;
}

/* أزرار ملونة */
.btn-primary {
    background: var(--primary-gradient);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-gradient);
    filter: brightness(1.1);
    color: white;
}

.btn-success {
    background: var(--success-gradient);
    color: white;
}

.btn-success:hover {
    background: var(--success-gradient);
    filter: brightness(1.1);
    color: white;
}

.btn-warning {
    background: var(--warning-gradient);
    color: white;
}

.btn-warning:hover {
    background: var(--warning-gradient);
    filter: brightness(1.1);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    color: white;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    filter: brightness(1.1);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    color: white;
}

.btn-info:hover {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    filter: brightness(1.1);
    color: white;
}

/* أزرار شفافة */
.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-gradient);
    border-color: transparent;
    color: white;
}

.btn-outline-success {
    border: 2px solid var(--success-color);
    color: var(--success-color);
    background: transparent;
}

.btn-outline-success:hover {
    background: var(--success-gradient);
    border-color: transparent;
    color: white;
}

.btn-outline-danger {
    border: 2px solid var(--danger-color);
    color: var(--danger-color);
    background: transparent;
}

.btn-outline-danger:hover {
    background: var(--danger-color);
    border-color: transparent;
    color: white;
}

/* تحسين شريط التنقل */
.navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal);
    padding: var(--spacing-md) 0;
}

.navbar-brand {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    font-size: 1.5rem;
    text-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

.navbar-brand:hover {
    transform: scale(1.05);
    filter: brightness(1.2);
}

.nav-link {
    position: relative;
    transition: all var(--transition-normal);
    font-weight: 500;
    color: var(--text-primary) !important;
    padding: var(--spacing-sm) var(--spacing-md) !important;
    border-radius: var(--border-radius-md);
    margin: 0 var(--spacing-xs);
}

.nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 3px;
    background: var(--primary-gradient);
    transition: all var(--transition-normal);
    transform: translateX(-50%);
    border-radius: var(--border-radius-sm);
}

.nav-link:hover::before,
.nav-link.active::before {
    width: 80%;
}

.nav-link:hover {
    color: var(--primary-color) !important;
    transform: translateY(-2px);
    background: rgba(103, 126, 234, 0.1);
}

.nav-link i {
    margin-left: var(--spacing-xs);
    transition: all var(--transition-normal);
}

.nav-link:hover i {
    transform: scale(1.2);
}

/* تحسين النماذج */
.form-control, .form-select {
    border: 2px solid #e2e8f0;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    font-size: 1rem;
    transition: all var(--transition-normal);
    background: white;
    box-shadow: var(--shadow-sm);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(103, 126, 234, 0.1);
    transform: translateY(-2px);
}

.form-control:hover, .form-select:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

/* تحسين مربع البحث */
.search-box {
    background: white;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid rgba(255,255,255,0.2);
    transition: all var(--transition-bounce);
    overflow: hidden;
    position: relative;
}

.search-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.search-box:hover::before {
    transform: scaleX(1);
}

.search-box:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-hover);
}

/* تحسين التصنيفات */
.category-card {
    transition: all var(--transition-bounce);
    text-decoration: none;
}

.category-card .card {
    cursor: pointer;
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    border-radius: var(--border-radius-lg);
    transition: all var(--transition-bounce);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    position: relative;
}

.category-card .card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-gradient);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.category-card:hover .card::before {
    opacity: 0.1;
}

.category-card:hover .card {
    transform: translateY(-8px) scale(1.05);
    box-shadow: var(--shadow-hover);
    color: var(--primary-color);
}

.category-card:hover .card i {
    transform: scale(1.2) rotate(10deg);
    filter: brightness(1.2);
}

.category-card .card i {
    transition: all var(--transition-bounce);
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
}

/* تأثيرات متقدمة للخلفية */
.hero-section {
    background: var(--primary-gradient);
    position: relative;
    overflow: hidden;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.1) rotate(2deg); }
}

/* تأثيرات الجسيمات المتحركة */
.particle {
    position: absolute;
    pointer-events: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    animation: particleFloat 15s linear infinite;
}

@keyframes particleFloat {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100vh) rotate(360deg);
        opacity: 0;
    }
}

/* تأثيرات الوهج والإضاءة */
.glow-effect {
    position: relative;
}

.glow-effect::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: var(--primary-gradient);
    border-radius: inherit;
    z-index: -1;
    filter: blur(10px);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.glow-effect:hover::before {
    opacity: 0.7;
    animation: glowPulse 2s ease-in-out infinite alternate;
}

@keyframes glowPulse {
    0% {
        opacity: 0.5;
        transform: scale(0.98);
        filter: blur(8px);
    }
    100% {
        opacity: 0.8;
        transform: scale(1.02);
        filter: blur(12px);
    }
}

/* تأثيرات النص المتدرج */
.gradient-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.gradient-text::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    background: var(--secondary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.gradient-text:hover::after {
    opacity: 1;
}

/* تأثيرات الحدود المتحركة */
.animated-border {
    position: relative;
    border-radius: var(--border-radius-lg);
    background: white;
    padding: 2px;
}

.animated-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: inherit;
    background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c, #667eea);
    background-size: 300% 300%;
    z-index: -1;
    animation: borderGradient 4s ease infinite;
}

@keyframes borderGradient {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* تأثيرات الظلال المتحركة */
.floating-shadow {
    position: relative;
    transition: all var(--transition-bounce);
}

.floating-shadow::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    width: 80%;
    height: 20px;
    background: radial-gradient(ellipse, rgba(0,0,0,0.2) 0%, transparent 70%);
    transform: translateX(-50%);
    transition: all var(--transition-bounce);
    z-index: -1;
}

.floating-shadow:hover::after {
    transform: translateX(-50%) translateY(10px) scale(1.2);
    opacity: 0.5;
}

/* تأثيرات الموجات */
.wave-effect {
    position: relative;
    overflow: hidden;
}

.wave-effect::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left var(--transition-slow);
}

.wave-effect:hover::before {
    left: 100%;
}

/* تأثيرات التكبير والتصغير */
.zoom-effect {
    transition: all var(--transition-bounce);
    overflow: hidden;
}

.zoom-effect img {
    transition: all var(--transition-slow);
}

.zoom-effect:hover img {
    transform: scale(1.15) rotate(2deg);
    filter: brightness(1.1) saturate(1.3);
}

/* تأثيرات الانعكاس */
.reflection-effect {
    position: relative;
}

.reflection-effect::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(to bottom, rgba(255,255,255,0.1) 0%, transparent 100%);
    transform: scaleY(-1);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.reflection-effect:hover::after {
    opacity: 1;
}

/* تأثيرات الدوران */
.rotate-effect {
    transition: all var(--transition-bounce);
}

.rotate-effect:hover {
    transform: rotate(5deg) scale(1.05);
}

/* تأثيرات التموج */
.ripple-effect {
    position: relative;
    overflow: hidden;
}

.ripple-effect::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    transform: translate(-50%, -50%);
    transition: all var(--transition-normal);
}

.ripple-effect:hover::before {
    width: 300px;
    height: 300px;
}

/* تأثيرات الانزلاق */
.slide-effect {
    position: relative;
    overflow: hidden;
}

.slide-effect .slide-content {
    transition: all var(--transition-bounce);
}

.slide-effect:hover .slide-content {
    transform: translateX(10px);
}

/* تأثيرات الشفافية المتدرجة */
.fade-effect {
    position: relative;
}

.fade-effect::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-gradient);
    opacity: 0;
    transition: opacity var(--transition-normal);
    border-radius: inherit;
}

.fade-effect:hover::before {
    opacity: 0.1;
}

/* تأثيرات النبض */
.pulse-effect {
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(103, 126, 234, 0.4);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 0 20px rgba(103, 126, 234, 0);
    }
}

/* تأثيرات الاهتزاز */
.shake-effect:hover {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* تأثيرات الارتداد */
.bounce-effect:hover {
    animation: bounce 0.6s ease;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* تأثيرات التدوير ثلاثي الأبعاد */
.flip-effect {
    perspective: 1000px;
    transition: all var(--transition-bounce);
}

.flip-effect:hover {
    transform: rotateY(10deg) rotateX(5deg);
}

/* تأثيرات الانحناء */
.skew-effect {
    transition: all var(--transition-bounce);
}

.skew-effect:hover {
    transform: skew(-2deg, 1deg) scale(1.02);
}

/* تحسينات الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .hero-section {
        min-height: 80vh;
    }

    .book-card:hover {
        transform: translateY(-8px) scale(1.01);
    }

    .floating-shadow:hover::after {
        transform: translateX(-50%) translateY(5px) scale(1.1);
    }

    .zoom-effect:hover img {
        transform: scale(1.08) rotate(1deg);
    }

    .flip-effect:hover {
        transform: rotateY(5deg) rotateX(2deg);
    }
}

.category-card .card {
    cursor: pointer;
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
}

[data-theme="dark"] .category-card .card {
    background: linear-gradient(145deg, var(--card-bg-light), #333333);
}

.category-card:hover .card {
    background: linear-gradient(145deg, var(--primary-color), #667eea);
    color: white;
}

.category-card:hover .card i {
    color: white !important;
}

/* تصميم عناوين الأقسام */
.section-title {
    font-weight: 700;
    color: var(--dark-color);
    position: relative;
    padding-bottom: 10px;
}

[data-theme="dark"] .section-title {
    color: var(--text-light);
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

/* تصميم الأزرار */
.btn {
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 600;
    transition: background-color 0.2s ease, box-shadow 0.2s ease;
    border: none;
}

.btn:hover {
    box-shadow: 0 3px 10px rgba(0,0,0,0.15);
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), #667eea);
}

.btn-primary:hover {
    background: linear-gradient(45deg, #357abd, #5a6fd8);
}

/* تصميم شريط التنقل */
.navbar {
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

/* تصميم التذييل */
footer {
    background: linear-gradient(135deg, var(--dark-color) 0%, #34495e 100%);
}

/* تأثيرات الحركة المحسنة للاستقرار */
.fade-in {
    opacity: 1 !important;
    transform: none !important;
}

/* إزالة keyframes المسببة لعدم الاستقرار */

/* تصميم متجاوب */
@media (max-width: 768px) {
    .hero-section {
        min-height: 400px;
        text-align: center;
    }
    
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .search-box {
        margin: 0 15px;
    }
    
    .category-card .card-body {
        padding: 1rem;
    }
    
    .category-card .card-title {
        font-size: 0.9rem;
    }
}

/* تصميم كارت الكتاب */
.book-card {
    transition: box-shadow 0.2s ease;
    border-radius: 15px;
    overflow: hidden;
}

.book-card:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.12);
}

.book-cover {
    height: 250px;
    object-fit: cover;
    border-radius: 10px;
}

.book-rating {
    color: var(--warning-color);
}

/* تصميم شارة الجديد */
.badge-new {
    position: absolute;
    top: 10px;
    right: 10px;
    background: linear-gradient(45deg, var(--danger-color), #ff6b6b);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.7rem;
    font-weight: 600;
}

/* تصميم مؤشر التحميل */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* تحسينات الاستقرار والأداء */
* {
    /* تقليل الحركة للمستخدمين الذين يفضلون ذلك */
    @media (prefers-reduced-motion: reduce) {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* منع الحركة غير المرغوب فيها */
html {
    scroll-behavior: smooth;
    overflow-x: hidden;
}

body {
    overflow-x: hidden;
    position: relative;
}

/* تحسين استقرار الصور */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* منع تحرك العناصر أثناء التحميل */
.container, .container-fluid {
    min-height: 1px;
}

/* ضمان الاستقرار الكامل */
.hero-section, .section {
    position: relative;
    will-change: auto;
}

/* منع الحركة غير المرغوب فيها في العناصر */
.card, .btn, .search-box {
    will-change: auto;
    backface-visibility: hidden;
    transform: translateZ(0);
}

/* تحسين الأداء */
.book-cover, .hero-image {
    transform: translateZ(0);
    will-change: auto;
}

/* منع التمرير الأفقي */
.row {
    margin-left: 0;
    margin-right: 0;
}

.col, [class*="col-"] {
    padding-left: 15px;
    padding-right: 15px;
}

/* تحسينات إضافية للوضع الليلي */
[data-theme="dark"] .hero-section {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

[data-theme="dark"] .search-box {
    background-color: var(--card-bg-light);
    border-color: var(--border-light);
}

[data-theme="dark"] .text-muted {
    color: #adb5bd !important;
}

/* تأثير الضوء على الأزرار */
.btn-glow {
    box-shadow: 0 0 20px rgba(74, 144, 226, 0.3);
}

.btn-glow:hover {
    box-shadow: 0 0 30px rgba(74, 144, 226, 0.5);
}

/* تصميم شريط التقدم */
.progress-bar-custom {
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 10px;
}

/* تحسين النصوص */
.text-gradient {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}
