/* مكتبتي الحرة - ملف التصميم الرئيسي */

/* الخطوط والإعدادات الأساسية */
* {
    font-family: 'Cairo', sans-serif;
}

body {
    direction: rtl;
    text-align: right;
    /* إزالة الانتقال من body لتجنب عدم الاستقرار */
}

/* متغيرات الألوان */
:root {
    --primary-color: #4a90e2;
    --secondary-color: #f39c12;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f1c40f;
    --info-color: #3498db;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --bg-light: #ffffff;
    --bg-dark: #1a1a1a;
    --text-light: #333333;
    --text-dark: #ffffff;
    --card-bg-light: #ffffff;
    --card-bg-dark: #2d2d2d;
    --border-light: #dee2e6;
    --border-dark: #404040;
}

/* الوضع الليلي */
[data-theme="dark"] {
    --bg-light: #1a1a1a;
    --bg-dark: #000000;
    --text-light: #ffffff;
    --text-dark: #333333;
    --card-bg-light: #2d2d2d;
    --card-bg-dark: #1a1a1a;
    --border-light: #404040;
    --border-dark: #dee2e6;
}

[data-theme="dark"] body {
    background-color: var(--bg-light);
    color: var(--text-light);
}

[data-theme="dark"] .navbar-light {
    background-color: var(--card-bg-light) !important;
    border-bottom: 1px solid var(--border-light);
}

[data-theme="dark"] .navbar-light .navbar-brand,
[data-theme="dark"] .navbar-light .nav-link {
    color: var(--text-light) !important;
}

[data-theme="dark"] .bg-light {
    background-color: var(--card-bg-light) !important;
}

[data-theme="dark"] .card {
    background-color: var(--card-bg-light);
    border-color: var(--border-light);
    color: var(--text-light);
}

[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
    background-color: var(--card-bg-light);
    border-color: var(--border-light);
    color: var(--text-light);
}

[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus {
    background-color: var(--card-bg-light);
    border-color: var(--primary-color);
    color: var(--text-light);
    box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
}

/* تدرج الألوان للقسم الرئيسي */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #667eea 100%);
}

/* تصميم القسم الرئيسي */
.hero-section {
    min-height: 500px;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,255,0.1)"><polygon points="1000,100 1000,0 0,100"/></svg>');
    background-size: cover;
}

.hero-image {
    max-width: 400px;
    filter: drop-shadow(0 10px 20px rgba(0,0,0,0.2));
    /* إزالة الانيميشن المستمر لتجنب عدم الاستقرار */
}

/* إزالة keyframes غير المستخدم */

/* تصميم مربع البحث */
.search-box {
    border-radius: 15px;
    border: 1px solid var(--border-light);
    transition: box-shadow 0.2s ease;
}

.search-box:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* تصميم البطاقات */
.card {
    border: none;
    border-radius: 15px;
    transition: box-shadow 0.2s ease;
    overflow: hidden;
}

.card:hover {
    box-shadow: 0 8px 20px rgba(0,0,0,0.1);
}

.category-card .card {
    cursor: pointer;
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
}

[data-theme="dark"] .category-card .card {
    background: linear-gradient(145deg, var(--card-bg-light), #333333);
}

.category-card:hover .card {
    background: linear-gradient(145deg, var(--primary-color), #667eea);
    color: white;
}

.category-card:hover .card i {
    color: white !important;
}

/* تصميم عناوين الأقسام */
.section-title {
    font-weight: 700;
    color: var(--dark-color);
    position: relative;
    padding-bottom: 10px;
}

[data-theme="dark"] .section-title {
    color: var(--text-light);
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

/* تصميم الأزرار */
.btn {
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 600;
    transition: background-color 0.2s ease, box-shadow 0.2s ease;
    border: none;
}

.btn:hover {
    box-shadow: 0 3px 10px rgba(0,0,0,0.15);
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), #667eea);
}

.btn-primary:hover {
    background: linear-gradient(45deg, #357abd, #5a6fd8);
}

/* تصميم شريط التنقل */
.navbar {
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

/* تصميم التذييل */
footer {
    background: linear-gradient(135deg, var(--dark-color) 0%, #34495e 100%);
}

/* تأثيرات الحركة المحسنة للاستقرار */
.fade-in {
    opacity: 1 !important;
    transform: none !important;
}

/* إزالة keyframes المسببة لعدم الاستقرار */

/* تصميم متجاوب */
@media (max-width: 768px) {
    .hero-section {
        min-height: 400px;
        text-align: center;
    }
    
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .search-box {
        margin: 0 15px;
    }
    
    .category-card .card-body {
        padding: 1rem;
    }
    
    .category-card .card-title {
        font-size: 0.9rem;
    }
}

/* تصميم كارت الكتاب */
.book-card {
    transition: box-shadow 0.2s ease;
    border-radius: 15px;
    overflow: hidden;
}

.book-card:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.12);
}

.book-cover {
    height: 250px;
    object-fit: cover;
    border-radius: 10px;
}

.book-rating {
    color: var(--warning-color);
}

/* تصميم شارة الجديد */
.badge-new {
    position: absolute;
    top: 10px;
    right: 10px;
    background: linear-gradient(45deg, var(--danger-color), #ff6b6b);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.7rem;
    font-weight: 600;
}

/* تصميم مؤشر التحميل */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* تحسينات الاستقرار والأداء */
* {
    /* تقليل الحركة للمستخدمين الذين يفضلون ذلك */
    @media (prefers-reduced-motion: reduce) {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* منع الحركة غير المرغوب فيها */
html {
    scroll-behavior: smooth;
    overflow-x: hidden;
}

body {
    overflow-x: hidden;
    position: relative;
}

/* تحسين استقرار الصور */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* منع تحرك العناصر أثناء التحميل */
.container, .container-fluid {
    min-height: 1px;
}

/* ضمان الاستقرار الكامل */
.hero-section, .section {
    position: relative;
    will-change: auto;
}

/* منع الحركة غير المرغوب فيها في العناصر */
.card, .btn, .search-box {
    will-change: auto;
    backface-visibility: hidden;
    transform: translateZ(0);
}

/* تحسين الأداء */
.book-cover, .hero-image {
    transform: translateZ(0);
    will-change: auto;
}

/* منع التمرير الأفقي */
.row {
    margin-left: 0;
    margin-right: 0;
}

.col, [class*="col-"] {
    padding-left: 15px;
    padding-right: 15px;
}

/* تحسينات إضافية للوضع الليلي */
[data-theme="dark"] .hero-section {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

[data-theme="dark"] .search-box {
    background-color: var(--card-bg-light);
    border-color: var(--border-light);
}

[data-theme="dark"] .text-muted {
    color: #adb5bd !important;
}

/* تأثير الضوء على الأزرار */
.btn-glow {
    box-shadow: 0 0 20px rgba(74, 144, 226, 0.3);
}

.btn-glow:hover {
    box-shadow: 0 0 30px rgba(74, 144, 226, 0.5);
}

/* تصميم شريط التقدم */
.progress-bar-custom {
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 10px;
}

/* تحسين النصوص */
.text-gradient {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}
