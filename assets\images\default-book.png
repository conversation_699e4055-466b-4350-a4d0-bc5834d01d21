<!-- This is a placeholder for the default book cover image -->
<!-- In a real implementation, this would be an actual PNG image file -->
<!-- For now, we'll create a simple SVG placeholder -->

<svg width="200" height="300" xmlns="http://www.w3.org/2000/svg">
  <rect width="200" height="300" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
  <text x="100" y="140" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#6c757d">
    غلاف الكتاب
  </text>
  <text x="100" y="160" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#adb5bd">
    غير متوفر
  </text>
  <rect x="50" y="80" width="100" height="80" fill="none" stroke="#dee2e6" stroke-width="1"/>
  <path d="M70 100 L90 120 L130 90" stroke="#dee2e6" stroke-width="2" fill="none"/>
  <circle cx="110" cy="105" r="8" fill="#dee2e6"/>
</svg>
