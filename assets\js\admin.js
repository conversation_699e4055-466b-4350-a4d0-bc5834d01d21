// لوحة التحكم - JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // التحقق من صلاحيات المدير
    checkAdminAccess();
    
    // تهيئة التنقل بين الأقسام
    initSectionNavigation();
    
    // تهيئة الرسوم البيانية
    initCharts();
    
    // تهيئة الجداول
    initTables();
    
    // تهيئة النماذج
    initForms();
    
    // تحديث الإحصائيات
    updateStatistics();
});

// التحقق من صلاحيات المدير
function checkAdminAccess() {
    if (!isLoggedIn()) {
        window.location.href = 'login.html?redirect=admin.html';
        return;
    }
    
    if (!isAdmin()) {
        showToast('ليس لديك صلاحية للوصول إلى لوحة التحكم', 'error');
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 2000);
        return;
    }
}

// تهيئة التنقل بين الأقسام
function initSectionNavigation() {
    const navLinks = document.querySelectorAll('.sidebar .nav-link');
    const sections = document.querySelectorAll('.content-section');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetSection = this.dataset.section;
            
            // إزالة الفئة النشطة من جميع الروابط
            navLinks.forEach(l => l.classList.remove('active'));
            
            // إضافة الفئة النشطة للرابط المحدد
            this.classList.add('active');
            
            // إخفاء جميع الأقسام
            sections.forEach(section => {
                section.classList.add('d-none');
            });
            
            // إظهار القسم المحدد
            const targetElement = document.getElementById(targetSection);
            if (targetElement) {
                targetElement.classList.remove('d-none');
                targetElement.classList.add('fade-in');
            }
        });
    });
}

// تهيئة الرسوم البيانية
function initCharts() {
    // رسم بياني للتحميلات الشهرية
    const downloadsCtx = document.getElementById('downloadsChart');
    if (downloadsCtx) {
        new Chart(downloadsCtx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'التحميلات',
                    data: [1200, 1900, 3000, 5000, 2000, 3000],
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
    
    // رسم بياني دائري للتصنيفات
    const categoriesCtx = document.getElementById('categoriesChart');
    if (categoriesCtx) {
        new Chart(categoriesCtx, {
            type: 'doughnut',
            data: {
                labels: ['الأدب', 'العلوم', 'التاريخ', 'الفلسفة', 'الدين'],
                datasets: [{
                    data: [30, 25, 20, 15, 10],
                    backgroundColor: [
                        '#4e73df',
                        '#1cc88a',
                        '#36b9cc',
                        '#f6c23e',
                        '#e74a3b'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
    
    // رسم بياني لإحصائيات التصنيفات
    const categoryStatsCtx = document.getElementById('categoryStatsChart');
    if (categoryStatsCtx) {
        new Chart(categoryStatsCtx, {
            type: 'bar',
            data: {
                labels: ['الأدب', 'العلوم', 'التاريخ', 'الفلسفة'],
                datasets: [{
                    label: 'عدد الكتب',
                    data: [250, 180, 320, 95],
                    backgroundColor: [
                        'rgba(78, 115, 223, 0.8)',
                        'rgba(28, 200, 138, 0.8)',
                        'rgba(54, 185, 204, 0.8)',
                        'rgba(246, 194, 62, 0.8)'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
}

// تهيئة الجداول
function initTables() {
    // إضافة وظائف البحث والترتيب للجداول
    const tables = document.querySelectorAll('table');
    tables.forEach(table => {
        addTableFeatures(table);
    });
}

function addTableFeatures(table) {
    // إضافة وظيفة الترتيب للعناوين
    const headers = table.querySelectorAll('thead th');
    headers.forEach((header, index) => {
        if (index < headers.length - 1) { // تجاهل عمود الإجراءات
            header.style.cursor = 'pointer';
            header.addEventListener('click', () => sortTable(table, index));
        }
    });
}

function sortTable(table, columnIndex) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    const isAscending = table.dataset.sortOrder !== 'asc';
    table.dataset.sortOrder = isAscending ? 'asc' : 'desc';
    
    rows.sort((a, b) => {
        const aText = a.cells[columnIndex].textContent.trim();
        const bText = b.cells[columnIndex].textContent.trim();
        
        // محاولة التحويل إلى رقم
        const aNum = parseFloat(aText);
        const bNum = parseFloat(bText);
        
        if (!isNaN(aNum) && !isNaN(bNum)) {
            return isAscending ? aNum - bNum : bNum - aNum;
        }
        
        return isAscending ? aText.localeCompare(bText) : bText.localeCompare(aText);
    });
    
    // إعادة ترتيب الصفوف
    rows.forEach(row => tbody.appendChild(row));
    
    // تحديث مؤشر الترتيب
    updateSortIndicator(table, columnIndex, isAscending);
}

function updateSortIndicator(table, columnIndex, isAscending) {
    // إزالة جميع مؤشرات الترتيب
    table.querySelectorAll('thead th i').forEach(icon => icon.remove());
    
    // إضافة مؤشر الترتيب الجديد
    const header = table.querySelectorAll('thead th')[columnIndex];
    const icon = document.createElement('i');
    icon.className = `fas fa-sort-${isAscending ? 'up' : 'down'} ms-2`;
    header.appendChild(icon);
}

// تهيئة النماذج
function initForms() {
    // نموذج إضافة كتاب
    const addBookForm = document.getElementById('addBookForm');
    if (addBookForm) {
        addBookForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveBook();
        });
    }
    
    // معاينة الصور
    const imageInputs = document.querySelectorAll('input[type="file"][accept*="image"]');
    imageInputs.forEach(input => {
        input.addEventListener('change', function(e) {
            previewImage(e.target);
        });
    });
}

function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            // إنشاء معاينة للصورة
            let preview = input.parentNode.querySelector('.image-preview');
            if (!preview) {
                preview = document.createElement('div');
                preview.className = 'image-preview mt-2';
                input.parentNode.appendChild(preview);
            }
            
            preview.innerHTML = `
                <img src="${e.target.result}" alt="معاينة" 
                     style="max-width: 150px; max-height: 200px; object-fit: cover; border-radius: 8px;">
            `;
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// حفظ كتاب جديد
function saveBook() {
    const form = document.getElementById('addBookForm');
    const formData = new FormData(form);
    
    // التحقق من صحة البيانات
    if (!validateBookForm()) {
        return;
    }
    
    // محاكاة حفظ الكتاب
    showLoading('جاري حفظ الكتاب...');
    
    setTimeout(() => {
        hideLoading();
        showToast('تم حفظ الكتاب بنجاح', 'success');
        
        // إغلاق المودال
        const modal = bootstrap.Modal.getInstance(document.getElementById('addBookModal'));
        modal.hide();
        
        // إعادة تعيين النموذج
        form.reset();
        
        // تحديث جدول الكتب
        refreshBooksTable();
    }, 2000);
}

function validateBookForm() {
    const title = document.getElementById('bookTitle').value.trim();
    const author = document.getElementById('bookAuthor').value.trim();
    const category = document.getElementById('bookCategory').value;
    const file = document.getElementById('bookFile').files[0];
    
    if (!title || !author || !category || !file) {
        showToast('يرجى ملء جميع الحقول المطلوبة', 'error');
        return false;
    }
    
    if (file && file.type !== 'application/pdf') {
        showToast('يجب أن يكون ملف الكتاب بصيغة PDF', 'error');
        return false;
    }
    
    return true;
}

// تحديث الإحصائيات
function updateStatistics() {
    // محاكاة تحديث الإحصائيات من الخادم
    const stats = {
        totalBooks: 1247,
        totalUsers: 3456,
        todayDownloads: 892,
        todayViews: 2341
    };
    
    // تحديث البطاقات
    animateCounter('.card:nth-child(1) .h5', stats.totalBooks);
    animateCounter('.card:nth-child(2) .h5', stats.totalUsers);
    animateCounter('.card:nth-child(3) .h5', stats.todayDownloads);
    animateCounter('.card:nth-child(4) .h5', stats.todayViews);
}

function animateCounter(selector, targetValue) {
    const element = document.querySelector(selector);
    if (!element) return;
    
    const startValue = 0;
    const duration = 2000;
    const startTime = performance.now();
    
    function updateCounter(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const currentValue = Math.floor(startValue + (targetValue - startValue) * progress);
        element.textContent = currentValue.toLocaleString();
        
        if (progress < 1) {
            requestAnimationFrame(updateCounter);
        }
    }
    
    requestAnimationFrame(updateCounter);
}

// تحديث جدول الكتب
function refreshBooksTable() {
    const tableBody = document.querySelector('#booksTable tbody');
    if (!tableBody) return;
    
    // محاكاة إضافة كتاب جديد
    const newRow = document.createElement('tr');
    newRow.innerHTML = `
        <td>
            <img src="assets/images/books/new-book.jpg" alt="غلاف" 
                 style="width: 40px; height: 60px; object-fit: cover;">
        </td>
        <td>كتاب جديد</td>
        <td>مؤلف جديد</td>
        <td><span class="badge bg-primary">أدب</span></td>
        <td>0</td>
        <td>${new Date().toLocaleDateString('ar-SA')}</td>
        <td>
            <button class="btn btn-sm btn-outline-primary" title="تعديل">
                <i class="fas fa-edit"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger" title="حذف">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;
    
    // إضافة الصف الجديد في المقدمة
    tableBody.insertBefore(newRow, tableBody.firstChild);
    
    // إضافة تأثير التمييز
    newRow.classList.add('table-success');
    setTimeout(() => {
        newRow.classList.remove('table-success');
    }, 3000);
}

// وظائف مساعدة
function showLoading(message = 'جاري التحميل...') {
    const loadingHTML = `
        <div class="loading-overlay">
            <div class="text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p>${message}</p>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', loadingHTML);
}

function hideLoading() {
    const loadingOverlay = document.querySelector('.loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.remove();
    }
}

// إضافة زر العودة للأعلى
function addScrollToTopButton() {
    const button = document.createElement('button');
    button.className = 'scroll-to-top';
    button.innerHTML = '<i class="fas fa-chevron-up"></i>';
    button.addEventListener('click', () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });
    
    document.body.appendChild(button);
    
    // إظهار/إخفاء الزر حسب موضع التمرير
    window.addEventListener('scroll', () => {
        if (window.pageYOffset > 300) {
            button.classList.add('show');
        } else {
            button.classList.remove('show');
        }
    });
}

// تهيئة زر العودة للأعلى
document.addEventListener('DOMContentLoaded', addScrollToTopButton);

// وظائف إدارة المستخدمين
function suspendUser(userId) {
    if (confirm('هل أنت متأكد من تعليق هذا المستخدم؟')) {
        showToast('تم تعليق المستخدم بنجاح', 'warning');
        // هنا يتم إرسال طلب إلى الخادم
    }
}

function deleteUser(userId) {
    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        showToast('تم حذف المستخدم بنجاح', 'success');
        // هنا يتم إرسال طلب إلى الخادم
    }
}

// وظائف إدارة الكتب
function editBook(bookId) {
    // فتح مودال التعديل مع بيانات الكتاب
    showToast('سيتم فتح نموذج التعديل قريباً', 'info');
}

function deleteBook(bookId) {
    if (confirm('هل أنت متأكد من حذف هذا الكتاب؟')) {
        showToast('تم حذف الكتاب بنجاح', 'success');
        // هنا يتم إرسال طلب إلى الخادم
    }
}
