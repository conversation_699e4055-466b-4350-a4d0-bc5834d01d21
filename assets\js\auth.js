// نظام المصادقة - JavaScript

// بيانات المستخدمين التجريبية
const demoUsers = {
    '<EMAIL>': {
        password: 'password123',
        name: 'أحم<PERSON> محمد',
        role: 'user',
        avatar: 'assets/images/avatars/user1.jpg'
    },
    '<EMAIL>': {
        password: 'admin123',
        name: 'مدير النظام',
        role: 'admin',
        avatar: 'assets/images/avatars/admin.jpg'
    }
};

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة نموذج تسجيل الدخول
    initLoginForm();
    
    // تهيئة إظهار/إخفاء كلمة المرور
    initPasswordToggle();
    
    // التحقق من حالة تسجيل الدخول
    checkAuthStatus();
});

function initLoginForm() {
    const loginForm = document.getElementById('loginForm');
    if (!loginForm) return;
    
    loginForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const email = document.getElementById('email').value.trim();
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('rememberMe').checked;
        
        // التحقق من صحة البيانات
        if (!validateLoginForm(email, password)) {
            return;
        }
        
        // محاولة تسجيل الدخول
        attemptLogin(email, password, rememberMe);
    });
}

function validateLoginForm(email, password) {
    let isValid = true;
    
    // التحقق من البريد الإلكتروني
    const emailInput = document.getElementById('email');
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (!email || !emailRegex.test(email)) {
        emailInput.classList.add('is-invalid');
        isValid = false;
    } else {
        emailInput.classList.remove('is-invalid');
        emailInput.classList.add('is-valid');
    }
    
    // التحقق من كلمة المرور
    const passwordInput = document.getElementById('password');
    
    if (!password || password.length < 6) {
        passwordInput.classList.add('is-invalid');
        isValid = false;
    } else {
        passwordInput.classList.remove('is-invalid');
        passwordInput.classList.add('is-valid');
    }
    
    return isValid;
}

function attemptLogin(email, password, rememberMe) {
    const loginBtn = document.getElementById('loginBtn');
    const originalText = loginBtn.innerHTML;
    
    // إظهار حالة التحميل
    loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري تسجيل الدخول...';
    loginBtn.disabled = true;
    
    // محاكاة تأخير الشبكة
    setTimeout(() => {
        // التحقق من بيانات المستخدم
        const user = demoUsers[email];
        
        if (user && user.password === password) {
            // نجح تسجيل الدخول
            const userData = {
                email: email,
                name: user.name,
                role: user.role,
                avatar: user.avatar,
                loginTime: new Date().toISOString()
            };
            
            // حفظ بيانات المستخدم
            if (rememberMe) {
                localStorage.setItem('userData', JSON.stringify(userData));
                localStorage.setItem('isLoggedIn', 'true');
            } else {
                sessionStorage.setItem('userData', JSON.stringify(userData));
                sessionStorage.setItem('isLoggedIn', 'true');
            }
            
            // إظهار رسالة نجاح
            showToast(`مرحباً ${user.name}! تم تسجيل الدخول بنجاح`, 'success');
            
            // إعادة التوجيه
            setTimeout(() => {
                const redirectUrl = new URLSearchParams(window.location.search).get('redirect') || 'index.html';
                window.location.href = redirectUrl;
            }, 1500);
            
        } else {
            // فشل تسجيل الدخول
            showToast('البريد الإلكتروني أو كلمة المرور غير صحيحة', 'error');
            
            // إعادة تعيين النموذج
            loginBtn.innerHTML = originalText;
            loginBtn.disabled = false;
            
            // إضافة تأثير اهتزاز
            loginForm.classList.add('shake');
            setTimeout(() => loginForm.classList.remove('shake'), 500);
        }
    }, 1500);
}

function initPasswordToggle() {
    const toggleBtn = document.getElementById('togglePassword');
    const passwordInput = document.getElementById('password');
    
    if (toggleBtn && passwordInput) {
        toggleBtn.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);
            
            const icon = this.querySelector('i');
            if (type === 'text') {
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    }
}

function checkAuthStatus() {
    const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true' || 
                      sessionStorage.getItem('isLoggedIn') === 'true';
    
    if (isLoggedIn) {
        // المستخدم مسجل دخوله بالفعل، إعادة توجيه للصفحة الرئيسية
        const currentPage = window.location.pathname.split('/').pop();
        if (currentPage === 'login.html' || currentPage === 'register.html') {
            window.location.href = 'index.html';
        }
    }
}

// وظائف الحسابات التجريبية
function fillDemoUser() {
    document.getElementById('email').value = '<EMAIL>';
    document.getElementById('password').value = 'password123';
    showToast('تم ملء بيانات المستخدم التجريبي', 'info');
}

function fillDemoAdmin() {
    document.getElementById('email').value = '<EMAIL>';
    document.getElementById('password').value = 'admin123';
    showToast('تم ملء بيانات المدير التجريبي', 'info');
}

// وظيفة تسجيل الخروج
function logout() {
    localStorage.removeItem('userData');
    localStorage.removeItem('isLoggedIn');
    sessionStorage.removeItem('userData');
    sessionStorage.removeItem('isLoggedIn');
    
    showToast('تم تسجيل الخروج بنجاح', 'info');
    
    setTimeout(() => {
        window.location.href = 'index.html';
    }, 1000);
}

// وظيفة الحصول على بيانات المستخدم الحالي
function getCurrentUser() {
    const userData = localStorage.getItem('userData') || sessionStorage.getItem('userData');
    return userData ? JSON.parse(userData) : null;
}

// وظيفة التحقق من صلاحيات المدير
function isAdmin() {
    const user = getCurrentUser();
    return user && user.role === 'admin';
}

// وظيفة التحقق من تسجيل الدخول
function isLoggedIn() {
    return localStorage.getItem('isLoggedIn') === 'true' || 
           sessionStorage.getItem('isLoggedIn') === 'true';
}

// تحديث واجهة المستخدم بناءً على حالة تسجيل الدخول
function updateAuthUI() {
    const isUserLoggedIn = isLoggedIn();
    const user = getCurrentUser();
    
    // تحديث أزرار التنقل
    const loginBtn = document.querySelector('a[href="login.html"]');
    const registerBtn = document.querySelector('a[href="register.html"]');
    
    if (isUserLoggedIn && user) {
        // إخفاء أزرار تسجيل الدخول والتسجيل
        if (loginBtn) loginBtn.style.display = 'none';
        if (registerBtn) registerBtn.style.display = 'none';
        
        // إضافة قائمة المستخدم
        addUserMenu(user);
    } else {
        // إظهار أزرار تسجيل الدخول والتسجيل
        if (loginBtn) loginBtn.style.display = 'inline-block';
        if (registerBtn) registerBtn.style.display = 'inline-block';
    }
}

function addUserMenu(user) {
    const navbar = document.querySelector('.navbar .d-flex');
    if (!navbar || document.getElementById('userMenu')) return;
    
    const userMenuHTML = `
        <div class="dropdown me-2" id="userMenu">
            <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-user me-1"></i>
                ${user.name}
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
                <li><h6 class="dropdown-header">${user.name}</h6></li>
                <li><small class="dropdown-item-text text-muted">${user.email}</small></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="profile.html">
                    <i class="fas fa-user me-2"></i>الملف الشخصي
                </a></li>
                <li><a class="dropdown-item" href="favorites.html">
                    <i class="fas fa-heart me-2"></i>المفضلة
                </a></li>
                ${user.role === 'admin' ? `
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="admin.html">
                    <i class="fas fa-cog me-2"></i>لوحة التحكم
                </a></li>
                ` : ''}
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item text-danger" href="#" onclick="logout()">
                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                </a></li>
            </ul>
        </div>
    `;
    
    navbar.insertAdjacentHTML('afterbegin', userMenuHTML);
}

// تشغيل تحديث واجهة المستخدم عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateAuthUI();
});

// إضافة تأثير الاهتزاز
const style = document.createElement('style');
style.textContent = `
    .shake {
        animation: shake 0.5s;
    }
    
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
        20%, 40%, 60%, 80% { transform: translateX(5px); }
    }
`;
document.head.appendChild(style);
