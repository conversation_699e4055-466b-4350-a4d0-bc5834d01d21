// ملف JavaScript خاص بصفحة تفاصيل الكتاب

class BookDetails {
    constructor() {
        this.bookId = this.getBookIdFromUrl();
        this.book = null;
        this.userRating = 0;

        this.init();
    }

    init() {
        if (this.bookId) {
            this.loadBookDetails();
        } else {
            this.showError('معرف الكتاب غير صحيح');
        }

        this.setupEventListeners();
    }

    getBookIdFromUrl() {
        const urlParams = new URLSearchParams(window.location.search);
        return parseInt(urlParams.get('id'));
    }

    async loadBookDetails() {
        try {
            this.showLoading();
            const response = await API.books.getById(this.bookId);

            if (response.success) {
                this.book = response.data;
                this.renderBookDetails();
                this.loadComments();
                this.loadRelatedBooks();
            }
        } catch (error) {
            console.error('خطأ في تحميل تفاصيل الكتاب:', error);
            this.showError('خطأ في تحميل تفاصيل الكتاب');
        } finally {
            this.hideLoading();
        }
    }

    renderBookDetails() {
        if (!this.book) return;

        // تحديث عنوان الصفحة
        document.title = `${this.book.title} - مكتبتي الحرة`;

        // تحديث معلومات الكتاب
        this.updateBookInfo();
        this.updateBookStats();
        this.updateBookActions();
    }

    updateBookInfo() {
        const elements = {
            title: document.querySelector('.book-title'),
            author: document.querySelector('.book-author'),
            description: document.querySelector('.book-description'),
            cover: document.querySelector('.book-cover'),
            category: document.querySelector('.book-category'),
            publishYear: document.querySelector('.book-publish-year'),
            pages: document.querySelector('.book-pages'),
            language: document.querySelector('.book-language'),
            publisher: document.querySelector('.book-publisher')
        };

        if (elements.title) elements.title.textContent = this.book.title;
        if (elements.author) elements.author.textContent = this.book.author;
        if (elements.description) elements.description.textContent = this.book.description;
        if (elements.cover) elements.cover.src = this.book.cover_url || 'assets/images/default-book.png';
        if (elements.category) elements.category.textContent = this.book.category_name;
        if (elements.publishYear) elements.publishYear.textContent = this.book.publish_year || 'غير محدد';
        if (elements.pages) elements.pages.textContent = this.book.pages_count || 'غير محدد';
        if (elements.language) elements.language.textContent = this.book.language === 'ar' ? 'العربية' : 'أخرى';
        if (elements.publisher) elements.publisher.textContent = this.book.publisher || 'غير محدد';
    }

    updateBookStats() {
        const elements = {
            rating: document.querySelector('.book-rating'),
            downloads: document.querySelector('.book-downloads'),
            views: document.querySelector('.book-views')
        };

        if (elements.rating) {
            elements.rating.innerHTML = this.generateStars(this.book.rating) +
                ` <span class="ms-2">(${this.book.rating})</span>`;
        }
        if (elements.downloads) elements.downloads.textContent = this.book.downloads_count;
        if (elements.views) elements.views.textContent = this.book.views_count;
    }

    updateBookActions() {
        const downloadBtn = document.getElementById('downloadBtn');
        const favoriteBtn = document.getElementById('favoriteBtn');

        if (downloadBtn) {
            downloadBtn.onclick = () => this.downloadBook();
        }

        if (favoriteBtn) {
            favoriteBtn.onclick = () => this.toggleFavorite();
        }
    }

    setupEventListeners() {
        // تهيئة نظام التقييم
        this.initRatingSystem();

        // تهيئة نموذج التقييم
        const ratingForm = document.getElementById('ratingForm');
        if (ratingForm) {
            ratingForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submitRating();
            });
        }
    }

    showLoading() {
        const loadingElement = document.getElementById('bookLoading');
        if (loadingElement) {
            loadingElement.classList.remove('d-none');
        }
    }

    hideLoading() {
        const loadingElement = document.getElementById('bookLoading');
        if (loadingElement) {
            loadingElement.classList.add('d-none');
        }
    }

    showError(message) {
        const errorElement = document.getElementById('bookError');
        if (errorElement) {
            errorElement.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    ${message}
                </div>
            `;
            errorElement.classList.remove('d-none');
        }
    }
}

// تهيئة الفئة عند تحميل الصفحة
let bookDetails;

document.addEventListener('DOMContentLoaded', function() {
    bookDetails = new BookDetails();

    // تهيئة التقييم التفاعلي (للتوافق مع الكود القديم)
    initRatingSystem();

    // تحميل الكتب المشابهة
    loadRelatedBooks();

    // التحقق من حالة تسجيل الدخول للتحميل
    checkLoginStatus();

    // تهيئة نظام المفضلة
    initFavoritesSystem();
});

// نظام التقييم التفاعلي
function initRatingSystem() {
    const ratingStars = document.querySelectorAll('.rating-input i');
    let selectedRating = 0;
    
    ratingStars.forEach((star, index) => {
        star.addEventListener('mouseenter', function() {
            highlightStars(index + 1);
        });
        
        star.addEventListener('mouseleave', function() {
            highlightStars(selectedRating);
        });
        
        star.addEventListener('click', function() {
            selectedRating = index + 1;
            highlightStars(selectedRating);
        });
    });
    
    function highlightStars(rating) {
        ratingStars.forEach((star, index) => {
            if (index < rating) {
                star.className = 'fas fa-star text-warning';
            } else {
                star.className = 'far fa-star text-muted';
            }
        });
    }
}

// تحميل الكتب المشابهة
function loadRelatedBooks() {
    const relatedBooksContainer = document.getElementById('relatedBooks');
    
    // بيانات تجريبية للكتب المشابهة
    const relatedBooks = [
        {
            id: 2,
            title: "الحب في زمن الكوليرا",
            author: "غابرييل غارسيا ماركيز",
            cover: "assets/images/books/book2.jpg",
            rating: 4.7
        },
        {
            id: 3,
            title: "خريف البطريرك",
            author: "غابرييل غارسيا ماركيز",
            cover: "assets/images/books/book3.jpg",
            rating: 4.5
        },
        {
            id: 4,
            title: "الجنرال في متاهته",
            author: "غابرييل غارسيا ماركيز",
            cover: "assets/images/books/book4.jpg",
            rating: 4.6
        },
        {
            id: 5,
            title: "ذاكرة غانياتي الحزينات",
            author: "غابرييل غارسيا ماركيز",
            cover: "assets/images/books/book5.jpg",
            rating: 4.4
        }
    ];
    
    relatedBooks.forEach(book => {
        const bookCard = createRelatedBookCard(book);
        relatedBooksContainer.appendChild(bookCard);
    });
}

function createRelatedBookCard(book) {
    const col = document.createElement('div');
    col.className = 'col-lg-3 col-md-6 mb-4';
    
    col.innerHTML = `
        <div class="card book-card h-100 shadow-sm">
            <div class="card-body p-3">
                <div class="text-center mb-3">
                    <img src="${book.cover}" alt="${book.title}" class="book-cover img-fluid" 
                         style="height: 200px; object-fit: cover;"
                         onerror="this.src='assets/images/default-book.png'">
                </div>
                <h6 class="card-title fw-bold mb-2" title="${book.title}">
                    ${book.title.length > 25 ? book.title.substring(0, 25) + '...' : book.title}
                </h6>
                <p class="card-text text-muted small mb-2">
                    <i class="fas fa-user me-1"></i>
                    ${book.author}
                </p>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div class="book-rating">
                        ${generateStars(book.rating)}
                    </div>
                    <small class="text-muted">${book.rating}</small>
                </div>
                <div class="d-grid">
                    <a href="book-details.html?id=${book.id}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye me-1"></i>
                        عرض التفاصيل
                    </a>
                </div>
            </div>
        </div>
    `;
    
    return col;
}

// التحقق من حالة تسجيل الدخول
function checkLoginStatus() {
    const downloadBtn = document.getElementById('downloadBtn');
    const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
    
    if (!isLoggedIn) {
        downloadBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showLoginModal();
        });
    }
}

function showLoginModal() {
    // إنشاء مودال تسجيل الدخول
    const modalHTML = `
        <div class="modal fade" id="loginModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">تسجيل الدخول مطلوب</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body text-center">
                        <i class="fas fa-lock fa-3x text-warning mb-3"></i>
                        <h6>يجب تسجيل الدخول لتحميل الكتب</h6>
                        <p class="text-muted">قم بإنشاء حساب مجاني أو سجل دخولك للاستمتاع بتحميل الكتب</p>
                    </div>
                    <div class="modal-footer justify-content-center">
                        <a href="login.html" class="btn btn-primary">تسجيل الدخول</a>
                        <a href="register.html" class="btn btn-outline-primary">إنشاء حساب</a>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    const modal = new bootstrap.Modal(document.getElementById('loginModal'));
    modal.show();
    
    // إزالة المودال بعد إغلاقه
    document.getElementById('loginModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// نظام المفضلة
function initFavoritesSystem() {
    const favoriteBtn = document.querySelector('button[onclick="addToFavorites()"]');
    const bookId = getBookIdFromURL();
    
    // التحقق من حالة المفضلة
    const favorites = JSON.parse(localStorage.getItem('favorites') || '[]');
    const isFavorite = favorites.includes(bookId);
    
    updateFavoriteButton(favoriteBtn, isFavorite);
}

function addToFavorites() {
    const bookId = getBookIdFromURL();
    const favorites = JSON.parse(localStorage.getItem('favorites') || '[]');
    const favoriteBtn = document.querySelector('button[onclick="addToFavorites()"]');
    
    if (favorites.includes(bookId)) {
        // إزالة من المفضلة
        const index = favorites.indexOf(bookId);
        favorites.splice(index, 1);
        updateFavoriteButton(favoriteBtn, false);
        showToast('تم إزالة الكتاب من المفضلة', 'info');
    } else {
        // إضافة للمفضلة
        favorites.push(bookId);
        updateFavoriteButton(favoriteBtn, true);
        showToast('تم إضافة الكتاب للمفضلة', 'success');
    }
    
    localStorage.setItem('favorites', JSON.stringify(favorites));
}

function updateFavoriteButton(btn, isFavorite) {
    if (isFavorite) {
        btn.innerHTML = '<i class="fas fa-heart me-2"></i>إزالة من المفضلة';
        btn.className = 'btn btn-danger';
    } else {
        btn.innerHTML = '<i class="far fa-heart me-2"></i>إضافة للمفضلة';
        btn.className = 'btn btn-outline-danger';
    }
}

// وظائف القراءة والتحميل
function readOnline() {
    const bookId = getBookIdFromURL();
    window.open(`reader.html?id=${bookId}`, '_blank');
}

function downloadBook() {
    const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
    
    if (!isLoggedIn) {
        showLoginModal();
        return;
    }
    
    // محاكاة عملية التحميل
    const downloadBtn = document.getElementById('downloadBtn');
    const originalText = downloadBtn.innerHTML;
    
    downloadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحميل...';
    downloadBtn.disabled = true;
    
    setTimeout(() => {
        // محاكاة رابط التحميل
        const link = document.createElement('a');
        link.href = 'assets/books/sample-book.pdf';
        link.download = 'مئة-عام-من-العزلة.pdf';
        link.click();
        
        downloadBtn.innerHTML = originalText;
        downloadBtn.disabled = false;
        
        showToast('تم بدء التحميل بنجاح', 'success');
        
        // تحديث إحصائيات التحميل
        updateDownloadStats();
    }, 2000);
}

// وظائف مساعدة
function getBookIdFromURL() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('id') || '1';
}

function generateStars(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    let stars = '';
    
    for (let i = 0; i < fullStars; i++) {
        stars += '<i class="fas fa-star text-warning"></i>';
    }
    
    if (hasHalfStar) {
        stars += '<i class="fas fa-star-half-alt text-warning"></i>';
    }
    
    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
        stars += '<i class="far fa-star text-warning"></i>';
    }
    
    return stars;
}

function showToast(message, type = 'info') {
    const toastHTML = `
        <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    // إنشاء حاوي التوست إذا لم يكن موجوداً
    let toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toastContainer';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }
    
    toastContainer.insertAdjacentHTML('beforeend', toastHTML);
    const toastElement = toastContainer.lastElementChild;
    const toast = new bootstrap.Toast(toastElement);
    toast.show();
    
    // إزالة التوست بعد إخفائه
    toastElement.addEventListener('hidden.bs.toast', function() {
        this.remove();
    });
}

function updateDownloadStats() {
    const statsElement = document.querySelector('.stat-card h5');
    if (statsElement) {
        const currentDownloads = parseInt(statsElement.textContent.replace(',', ''));
        statsElement.textContent = (currentDownloads + 1).toLocaleString();
    }
}

// تهيئة نظام التعليقات
document.addEventListener('DOMContentLoaded', function() {
    const commentForm = document.querySelector('.add-comment form');
    if (commentForm) {
        commentForm.addEventListener('submit', function(e) {
            e.preventDefault();
            addComment();
        });
    }
});

function addComment() {
    const commentText = document.getElementById('comment').value.trim();
    const selectedRating = document.querySelectorAll('.rating-input .fas').length;
    
    if (!commentText) {
        showToast('يرجى كتابة تعليق', 'error');
        return;
    }
    
    if (selectedRating === 0) {
        showToast('يرجى اختيار تقييم', 'error');
        return;
    }
    
    // محاكاة إضافة التعليق
    const commentHTML = `
        <div class="comment-item card mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div class="d-flex align-items-center">
                        <div class="avatar bg-info text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                            م
                        </div>
                        <div>
                            <h6 class="mb-0">مستخدم جديد</h6>
                            <small class="text-muted">الآن</small>
                        </div>
                    </div>
                    <div class="rating">
                        ${generateStars(selectedRating)}
                    </div>
                </div>
                <p class="mb-0">${commentText}</p>
            </div>
        </div>
    `;
    
    const commentsList = document.querySelector('.comments-list');
    commentsList.insertAdjacentHTML('afterbegin', commentHTML);
    
    // إعادة تعيين النموذج
    document.getElementById('comment').value = '';
    document.querySelectorAll('.rating-input i').forEach(star => {
        star.className = 'far fa-star text-muted';
    });
    
    showToast('تم إضافة التعليق بنجاح', 'success');
}
