// صفحة الكتب - JavaScript

class BooksManager {
    constructor() {
        this.books = [];
        this.filteredBooks = [];
        this.currentPage = 1;
        this.booksPerPage = 12;
        this.viewMode = 'grid'; // grid or list
        this.currentFilters = {
            search: '',
            category: '',
            sort: 'newest'
        };
        
        this.init();
    }
    
    init() {
        this.loadBooks();
        this.setupEventListeners();
        this.setupViewToggle();
    }
    
    setupEventListeners() {
        // البحث
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.getElementById('searchBtn');
        
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.currentFilters.search = e.target.value.trim();
                    this.applyFilters();
                }, 300);
            });
        }
        
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.currentFilters.search = searchInput.value.trim();
                this.applyFilters();
            });
        }
        
        // المرشحات
        const categoryFilter = document.getElementById('categoryFilter');
        const sortFilter = document.getElementById('sortFilter');
        
        if (categoryFilter) {
            categoryFilter.addEventListener('change', (e) => {
                this.currentFilters.category = e.target.value;
                this.applyFilters();
            });
        }
        
        if (sortFilter) {
            sortFilter.addEventListener('change', (e) => {
                this.currentFilters.sort = e.target.value;
                this.applyFilters();
            });
        }
    }
    
    setupViewToggle() {
        const gridViewBtn = document.getElementById('gridView');
        const listViewBtn = document.getElementById('listView');
        
        if (gridViewBtn) {
            gridViewBtn.addEventListener('click', () => {
                this.setViewMode('grid');
                gridViewBtn.classList.add('active');
                listViewBtn.classList.remove('active');
            });
        }
        
        if (listViewBtn) {
            listViewBtn.addEventListener('click', () => {
                this.setViewMode('list');
                listViewBtn.classList.add('active');
                gridViewBtn.classList.remove('active');
            });
        }
    }
    
    setViewMode(mode) {
        this.viewMode = mode;
        this.renderBooks();
    }
    
    async loadBooks() {
        try {
            // تحميل الكتب من API
            const params = {
                page: this.currentPage,
                limit: this.booksPerPage,
                search: this.currentFilters.search,
                category: this.currentFilters.category,
                sort: this.currentFilters.sort
            };

            const response = await API.books.getAll(params);

            if (response.success) {
                this.filteredBooks = response.data.items;
                this.totalBooks = response.data.pagination.total_items;
                this.hideLoading();
                this.renderBooks();
                this.renderPagination();
            }
        } catch (error) {
            console.error('خطأ في تحميل الكتب:', error);
            this.hideLoading();
            this.showError('خطأ في تحميل الكتب. يرجى المحاولة مرة أخرى.');
        }
    }
    
    generateSampleBooks() {
        const sampleBooks = [];
        const titles = [
            'مئة عام من العزلة', 'الحب في زمن الكوليرا', 'تاريخ الطبري', 'مقدمة ابن خلدون',
            'الأسود يليق بك', 'مدن الملح', 'الثلاثية', 'موسم الهجرة إلى الشمال',
            'رجال في الشمس', 'الخبز الحافي', 'الحرافيش', 'زقاق المدق',
            'بين القصرين', 'قصر الشوق', 'السكرية', 'أولاد حارتنا',
            'الطريق', 'اللص والكلاب', 'الشحاذ', 'ميرامار'
        ];
        
        const authors = [
            'غابرييل غارسيا ماركيز', 'محمد بن جرير الطبري', 'عبد الرحمن ابن خلدون',
            'أحلام مستغانمي', 'عبد الرحمن منيف', 'نجيب محفوظ', 'الطيب صالح',
            'غسان كنفاني', 'محمد شكري', 'يوسف إدريس', 'إحسان عبد القدوس'
        ];
        
        const categories = ['literature', 'history', 'philosophy', 'science', 'religion'];
        
        for (let i = 0; i < 50; i++) {
            sampleBooks.push({
                id: i + 1,
                title: titles[Math.floor(Math.random() * titles.length)],
                author: authors[Math.floor(Math.random() * authors.length)],
                category: categories[Math.floor(Math.random() * categories.length)],
                cover: `assets/images/books/book${(i % 10) + 1}.jpg`,
                rating: (Math.random() * 2 + 3).toFixed(1), // 3.0 - 5.0
                downloads: Math.floor(Math.random() * 5000) + 100,
                views: Math.floor(Math.random() * 10000) + 500,
                publishYear: Math.floor(Math.random() * 50) + 1970,
                pages: Math.floor(Math.random() * 400) + 100,
                description: 'وصف تجريبي للكتاب. هذا نص تجريبي لمحاكاة وصف الكتاب الحقيقي.',
                addedDate: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000),
                isNew: Math.random() > 0.8
            });
        }
        
        return sampleBooks;
    }
    
    async applyFilters() {
        // إعادة تعيين الصفحة الحالية
        this.currentPage = 1;

        // إظهار مؤشر التحميل
        this.showLoading();

        // تحميل البيانات الجديدة
        await this.loadBooks();
    }
    
    sortBooks() {
        switch (this.currentFilters.sort) {
            case 'newest':
                this.filteredBooks.sort((a, b) => new Date(b.addedDate) - new Date(a.addedDate));
                break;
            case 'popular':
                this.filteredBooks.sort((a, b) => b.downloads - a.downloads);
                break;
            case 'alphabetical':
                this.filteredBooks.sort((a, b) => a.title.localeCompare(b.title));
                break;
            case 'rating':
                this.filteredBooks.sort((a, b) => b.rating - a.rating);
                break;
        }
    }
    
    renderBooks() {
        const container = document.getElementById('booksContainer');
        const noResults = document.getElementById('noResults');
        
        if (this.filteredBooks.length === 0) {
            container.classList.add('d-none');
            noResults.classList.remove('d-none');
            return;
        }
        
        container.classList.remove('d-none');
        noResults.classList.add('d-none');
        
        const startIndex = (this.currentPage - 1) * this.booksPerPage;
        const endIndex = startIndex + this.booksPerPage;
        const booksToShow = this.filteredBooks.slice(startIndex, endIndex);
        
        container.innerHTML = '';
        
        booksToShow.forEach(book => {
            const bookElement = this.createBookElement(book);
            container.appendChild(bookElement);
        });
    }
    
    createBookElement(book) {
        const col = document.createElement('div');
        
        if (this.viewMode === 'grid') {
            col.className = 'col-lg-3 col-md-4 col-sm-6 mb-4';
            col.innerHTML = this.createGridBookCard(book);
        } else {
            col.className = 'col-12 mb-3';
            col.innerHTML = this.createListBookCard(book);
        }
        
        return col;
    }
    
    createGridBookCard(book) {
        return `
            <div class="card book-card h-100 shadow-sm position-relative">
                ${book.isNew ? '<span class="badge-new">جديد</span>' : ''}
                <div class="card-body p-3">
                    <div class="text-center mb-3">
                        <img src="${book.cover}" alt="${book.title}" class="book-cover img-fluid" 
                             style="height: 200px; object-fit: cover;"
                             onerror="this.src='assets/images/default-book.png'">
                    </div>
                    <h6 class="card-title fw-bold mb-2" title="${book.title}">
                        ${book.title.length > 30 ? book.title.substring(0, 30) + '...' : book.title}
                    </h6>
                    <p class="card-text text-muted small mb-2">
                        <i class="fas fa-user me-1"></i>
                        ${book.author}
                    </p>
                    <p class="card-text text-muted small mb-2">
                        <i class="fas fa-tag me-1"></i>
                        ${getCategoryName(book.category)}
                    </p>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="book-rating">
                            ${this.generateStars(book.rating)}
                            <small class="text-muted ms-1">(${book.rating})</small>
                        </div>
                        <small class="text-muted">
                            <i class="fas fa-download me-1"></i>
                            ${book.downloads}
                        </small>
                    </div>
                    <div class="d-grid gap-2">
                        <a href="book-details.html?id=${book.id}" class="btn btn-primary btn-sm">
                            <i class="fas fa-eye me-1"></i>
                            عرض التفاصيل
                        </a>
                    </div>
                </div>
            </div>
        `;
    }
    
    createListBookCard(book) {
        return `
            <div class="card book-card shadow-sm">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-2 text-center mb-3 mb-md-0">
                            <img src="${book.cover}" alt="${book.title}" class="img-fluid" 
                                 style="height: 120px; object-fit: cover;"
                                 onerror="this.src='assets/images/default-book.png'">
                        </div>
                        <div class="col-md-7">
                            <div class="d-flex align-items-start">
                                <div class="flex-grow-1">
                                    <h5 class="card-title fw-bold mb-2">${book.title}</h5>
                                    <p class="text-muted mb-1">
                                        <i class="fas fa-user me-1"></i>
                                        ${book.author}
                                    </p>
                                    <p class="text-muted mb-2">
                                        <i class="fas fa-tag me-1"></i>
                                        ${getCategoryName(book.category)}
                                        <span class="mx-2">•</span>
                                        <i class="fas fa-calendar me-1"></i>
                                        ${book.publishYear}
                                        <span class="mx-2">•</span>
                                        <i class="fas fa-file-alt me-1"></i>
                                        ${book.pages} صفحة
                                    </p>
                                    <p class="card-text text-muted small">${book.description}</p>
                                </div>
                                ${book.isNew ? '<span class="badge bg-danger ms-2">جديد</span>' : ''}
                            </div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="mb-2">
                                <div class="book-rating mb-1">
                                    ${this.generateStars(book.rating)}
                                </div>
                                <small class="text-muted">${book.rating} (${book.downloads} تحميل)</small>
                            </div>
                            <div class="d-grid gap-2">
                                <a href="book-details.html?id=${book.id}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-eye me-1"></i>
                                    عرض التفاصيل
                                </a>
                                <button class="btn btn-outline-success btn-sm" onclick="quickDownload(${book.id})">
                                    <i class="fas fa-download me-1"></i>
                                    تحميل سريع
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    generateStars(rating) {
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 !== 0;
        let stars = '';
        
        for (let i = 0; i < fullStars; i++) {
            stars += '<i class="fas fa-star text-warning"></i>';
        }
        
        if (hasHalfStar) {
            stars += '<i class="fas fa-star-half-alt text-warning"></i>';
        }
        
        const emptyStars = 5 - Math.ceil(rating);
        for (let i = 0; i < emptyStars; i++) {
            stars += '<i class="far fa-star text-warning"></i>';
        }
        
        return stars;
    }
    
    renderPagination() {
        const pagination = document.getElementById('pagination');
        const totalPages = Math.ceil(this.filteredBooks.length / this.booksPerPage);
        
        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }
        
        let paginationHTML = '';
        
        // زر السابق
        paginationHTML += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="booksManager.goToPage(${this.currentPage - 1})">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        `;
        
        // أرقام الصفحات
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);
        
        if (startPage > 1) {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="booksManager.goToPage(1)">1</a>
                </li>
            `;
            if (startPage > 2) {
                paginationHTML += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }
        
        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="booksManager.goToPage(${i})">${i}</a>
                </li>
            `;
        }
        
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHTML += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="booksManager.goToPage(${totalPages})">${totalPages}</a>
                </li>
            `;
        }
        
        // زر التالي
        paginationHTML += `
            <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="booksManager.goToPage(${this.currentPage + 1})">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        `;
        
        pagination.innerHTML = paginationHTML;
    }
    
    goToPage(page) {
        const totalPages = Math.ceil(this.filteredBooks.length / this.booksPerPage);
        
        if (page < 1 || page > totalPages) return;
        
        this.currentPage = page;
        this.renderBooks();
        this.renderPagination();
        
        // التمرير إلى أعلى الصفحة
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }
    
    hideLoading() {
        const loadingIndicator = document.getElementById('loadingIndicator');
        if (loadingIndicator) {
            loadingIndicator.classList.add('d-none');
        }
    }

    showLoading() {
        const loadingIndicator = document.getElementById('loadingIndicator');
        if (loadingIndicator) {
            loadingIndicator.classList.remove('d-none');
        }
    }

    showError(message) {
        const container = document.getElementById('booksContainer');
        const noResults = document.getElementById('noResults');

        container.classList.add('d-none');
        noResults.classList.remove('d-none');
        noResults.innerHTML = `
            <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
            <h4>حدث خطأ</h4>
            <p class="text-muted">${message}</p>
            <button class="btn btn-primary" onclick="booksManager.loadBooks()">
                <i class="fas fa-redo me-1"></i>
                إعادة المحاولة
            </button>
        `;
    }
}

// تهيئة مدير الكتب
let booksManager;

document.addEventListener('DOMContentLoaded', function() {
    booksManager = new BooksManager();
});

// وظيفة التحميل السريع
async function quickDownload(bookId) {
    if (!isLoggedIn()) {
        showToast('يجب تسجيل الدخول أولاً للتحميل', 'error');
        setTimeout(() => {
            window.location.href = 'login.html?redirect=' + encodeURIComponent(window.location.href);
        }, 1500);
        return;
    }

    try {
        showToast('جاري بدء التحميل...', 'info');

        const response = await API.books.download(bookId);

        if (response.success && response.data.download_url) {
            // إنشاء رابط تحميل مؤقت
            const link = document.createElement('a');
            link.href = response.data.download_url;
            link.download = response.data.filename || 'book.pdf';
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showToast('تم بدء التحميل بنجاح', 'success');
        }
    } catch (error) {
        console.error('خطأ في التحميل:', error);
        showToast(error.message || 'خطأ في تحميل الكتاب', 'error');
    }
}
