// إعدادات الموقع العامة

const CONFIG = {
    // معلومات الموقع
    SITE_NAME: 'مكتبتي الحرة',
    SITE_DESCRIPTION: 'المكتبة الرقمية المجانية',
    SITE_VERSION: '1.0.0',
    
    // إعدادات API
    API_BASE_URL: '/api',
    API_VERSION: 'v1',
    API_ENDPOINTS: {
        // المصادقة
        AUTH_REGISTER: '/api/auth/register',
        AUTH_LOGIN: '/api/auth/login',
        AUTH_LOGOUT: '/api/auth/logout',
        AUTH_PROFILE: '/api/auth/profile',
        AUTH_PASSWORD: '/api/auth/password',

        // الكتب
        BOOKS: '/api/books',
        BOOK_DETAILS: '/api/books/{id}',
        BOOK_RATE: '/api/books/{id}/rate',
        BOOK_DOWNLOAD: '/api/books/{id}/download',

        // التصنيفات
        CATEGORIES: '/api/categories',

        // رفع الملفات
        UPLOAD: '/api/upload.php',
        DOWNLOAD: '/api/download.php'
    },
    
    // إعدادات التحميل
    MAX_FILE_SIZE: 50 * 1024 * 1024, // 50 ميجابايت
    ALLOWED_FILE_TYPES: ['pdf'],
    ALLOWED_IMAGE_TYPES: ['jpg', 'jpeg', 'png', 'webp'],
    
    // إعدادات القارئ
    READER: {
        DEFAULT_ZOOM: 100,
        MIN_ZOOM: 50,
        MAX_ZOOM: 200,
        FLIP_SPEED: 600,
        AUTO_FLIP_INTERVAL: 5000
    },
    
    // إعدادات البحث
    SEARCH: {
        MIN_QUERY_LENGTH: 2,
        MAX_RESULTS: 50,
        DEBOUNCE_DELAY: 300
    },
    
    // إعدادات الصفحات
    PAGINATION: {
        BOOKS_PER_PAGE: 12,
        COMMENTS_PER_PAGE: 10,
        USERS_PER_PAGE: 20
    },
    
    // التصنيفات المتاحة
    CATEGORIES: [
        {
            id: 'literature',
            name: 'الأدب',
            icon: 'fas fa-feather-alt',
            color: '#4e73df'
        },
        {
            id: 'science',
            name: 'العلوم',
            icon: 'fas fa-flask',
            color: '#1cc88a'
        },
        {
            id: 'history',
            name: 'التاريخ',
            icon: 'fas fa-landmark',
            color: '#f6c23e'
        },
        {
            id: 'philosophy',
            name: 'الفلسفة',
            icon: 'fas fa-brain',
            color: '#36b9cc'
        },
        {
            id: 'religion',
            name: 'الدين',
            icon: 'fas fa-mosque',
            color: '#858796'
        },
        {
            id: 'children',
            name: 'الأطفال',
            icon: 'fas fa-child',
            color: '#e74a3b'
        },
        {
            id: 'novels',
            name: 'الروايات',
            icon: 'fas fa-book',
            color: '#5a5c69'
        },
        {
            id: 'poetry',
            name: 'الشعر',
            icon: 'fas fa-pen-fancy',
            color: '#6f42c1'
        }
    ],
    
    // رسائل النظام
    MESSAGES: {
        SUCCESS: {
            LOGIN: 'تم تسجيل الدخول بنجاح',
            LOGOUT: 'تم تسجيل الخروج بنجاح',
            BOOK_ADDED: 'تم إضافة الكتاب بنجاح',
            BOOK_UPDATED: 'تم تحديث الكتاب بنجاح',
            BOOK_DELETED: 'تم حذف الكتاب بنجاح',
            BOOKMARK_ADDED: 'تم إضافة العلامة المرجعية',
            BOOKMARK_REMOVED: 'تم إزالة العلامة المرجعية',
            FAVORITE_ADDED: 'تم إضافة الكتاب للمفضلة',
            FAVORITE_REMOVED: 'تم إزالة الكتاب من المفضلة',
            COMMENT_ADDED: 'تم إضافة التعليق بنجاح',
            DOWNLOAD_STARTED: 'تم بدء التحميل بنجاح'
        },
        ERROR: {
            LOGIN_FAILED: 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
            ACCESS_DENIED: 'ليس لديك صلاحية للوصول',
            FILE_TOO_LARGE: 'حجم الملف كبير جداً',
            INVALID_FILE_TYPE: 'نوع الملف غير مدعوم',
            NETWORK_ERROR: 'خطأ في الاتصال بالشبكة',
            UNKNOWN_ERROR: 'حدث خطأ غير متوقع',
            REQUIRED_FIELDS: 'يرجى ملء جميع الحقول المطلوبة',
            INVALID_EMAIL: 'يرجى إدخال بريد إلكتروني صحيح',
            PASSWORD_TOO_SHORT: 'كلمة المرور قصيرة جداً',
            LOGIN_REQUIRED: 'يجب تسجيل الدخول أولاً'
        },
        INFO: {
            LOADING: 'جاري التحميل...',
            SAVING: 'جاري الحفظ...',
            DELETING: 'جاري الحذف...',
            UPLOADING: 'جاري الرفع...',
            PROCESSING: 'جاري المعالجة...',
            NO_RESULTS: 'لا توجد نتائج',
            NO_BOOKS: 'لا توجد كتب',
            NO_COMMENTS: 'لا توجد تعليقات',
            NO_BOOKMARKS: 'لا توجد علامات مرجعية',
            NO_FAVORITES: 'لا توجد مفضلة'
        }
    },
    
    // إعدادات التخزين المحلي
    STORAGE_KEYS: {
        USER_DATA: 'userData',
        IS_LOGGED_IN: 'isLoggedIn',
        THEME: 'theme',
        READER_THEME: 'readerDarkMode',
        FAVORITES: 'favorites',
        BOOKMARKS: 'bookmarks',
        NOTES: 'notes',
        READING_PROGRESS: 'readingProgress',
        SEARCH_HISTORY: 'searchHistory'
    },
    
    // إعدادات الواجهة
    UI: {
        ANIMATION_DURATION: 300,
        TOAST_DURATION: 5000,
        DEBOUNCE_DELAY: 300,
        SCROLL_OFFSET: 100,
        LAZY_LOAD_OFFSET: 200
    },
    
    // إعدادات الأمان
    SECURITY: {
        SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 ساعة
        MAX_LOGIN_ATTEMPTS: 5,
        LOCKOUT_DURATION: 15 * 60 * 1000, // 15 دقيقة
        PASSWORD_MIN_LENGTH: 6
    },
    
    // إعدادات SEO
    SEO: {
        DEFAULT_TITLE: 'مكتبتي الحرة - المكتبة الرقمية المجانية',
        DEFAULT_DESCRIPTION: 'اكتشف عالماً من المعرفة المجانية. آلاف الكتب في مختلف المجالات متاحة للقراءة والتحميل مجاناً',
        DEFAULT_KEYWORDS: 'كتب مجانية, مكتبة رقمية, قراءة أونلاين, تحميل كتب, أدب, علوم, تاريخ',
        SITE_URL: 'https://my-free-library.com'
    },
    
    // إعدادات التحليلات (للاستخدام المستقبلي)
    ANALYTICS: {
        GOOGLE_ANALYTICS_ID: '',
        TRACK_DOWNLOADS: true,
        TRACK_READING_TIME: true,
        TRACK_SEARCH_QUERIES: true
    },
    
    // إعدادات الشبكات الاجتماعية
    SOCIAL: {
        FACEBOOK_URL: '#',
        TWITTER_URL: '#',
        INSTAGRAM_URL: '#',
        YOUTUBE_URL: '#',
        SHARE_ENABLED: true
    },
    
    // إعدادات البريد الإلكتروني (للاستخدام المستقبلي)
    EMAIL: {
        SMTP_HOST: '',
        SMTP_PORT: 587,
        FROM_EMAIL: '<EMAIL>',
        FROM_NAME: 'مكتبتي الحرة'
    }
};

// وظائف مساعدة للوصول للإعدادات
const getConfig = (key) => {
    const keys = key.split('.');
    let value = CONFIG;
    
    for (const k of keys) {
        if (value && typeof value === 'object' && k in value) {
            value = value[k];
        } else {
            return undefined;
        }
    }
    
    return value;
};

const getMessage = (type, key) => {
    return getConfig(`MESSAGES.${type.toUpperCase()}.${key.toUpperCase()}`) || 'رسالة غير معروفة';
};

const getCategory = (id) => {
    return CONFIG.CATEGORIES.find(cat => cat.id === id);
};

const getCategoryName = (id) => {
    const category = getCategory(id);
    return category ? category.name : 'غير محدد';
};

const getCategoryIcon = (id) => {
    const category = getCategory(id);
    return category ? category.icon : 'fas fa-book';
};

const getCategoryColor = (id) => {
    const category = getCategory(id);
    return category ? category.color : '#6c757d';
};

/**
 * فئة API للتعامل مع الطلبات
 */
class API {
    static async request(url, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        };

        // إضافة رمز المصادقة إذا كان متوفراً
        const token = this.getAuthToken();
        if (token) {
            defaultOptions.headers['Authorization'] = `Bearer ${token}`;
        }

        const finalOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers
            }
        };

        try {
            const response = await fetch(url, finalOptions);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || 'حدث خطأ في الطلب');
            }

            return data;
        } catch (error) {
            console.error('API Error:', error);
            throw error;
        }
    }

    static async get(endpoint, params = {}) {
        const url = new URL(endpoint, window.location.origin);
        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined) {
                url.searchParams.append(key, params[key]);
            }
        });

        return this.request(url.toString(), {
            method: 'GET'
        });
    }

    static async post(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    static async put(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    static async delete(endpoint) {
        return this.request(endpoint, {
            method: 'DELETE'
        });
    }

    static async upload(endpoint, formData) {
        const token = this.getAuthToken();
        const headers = {};

        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }

        return this.request(endpoint, {
            method: 'POST',
            headers,
            body: formData
        });
    }

    static getAuthToken() {
        return localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');
    }

    static setAuthToken(token, remember = false) {
        if (remember) {
            localStorage.setItem('auth_token', token);
        } else {
            sessionStorage.setItem('auth_token', token);
        }
    }

    static removeAuthToken() {
        localStorage.removeItem('auth_token');
        sessionStorage.removeItem('auth_token');
    }

    // طرق مخصصة للـ endpoints
    static auth = {
        register: (data) => API.post(CONFIG.API_ENDPOINTS.AUTH_REGISTER, data),
        login: (data) => API.post(CONFIG.API_ENDPOINTS.AUTH_LOGIN, data),
        logout: () => API.post(CONFIG.API_ENDPOINTS.AUTH_LOGOUT),
        getProfile: () => API.get(CONFIG.API_ENDPOINTS.AUTH_PROFILE),
        updateProfile: (data) => API.put(CONFIG.API_ENDPOINTS.AUTH_PROFILE, data),
        changePassword: (data) => API.put(CONFIG.API_ENDPOINTS.AUTH_PASSWORD, data)
    };

    static books = {
        getAll: (params) => API.get(CONFIG.API_ENDPOINTS.BOOKS, params),
        getById: (id) => API.get(CONFIG.API_ENDPOINTS.BOOK_DETAILS.replace('{id}', id)),
        create: (data) => API.post(CONFIG.API_ENDPOINTS.BOOKS, data),
        update: (id, data) => API.put(CONFIG.API_ENDPOINTS.BOOK_DETAILS.replace('{id}', id), data),
        delete: (id) => API.delete(CONFIG.API_ENDPOINTS.BOOK_DETAILS.replace('{id}', id)),
        rate: (id, data) => API.post(CONFIG.API_ENDPOINTS.BOOK_RATE.replace('{id}', id), data),
        download: (id) => API.post(CONFIG.API_ENDPOINTS.BOOK_DOWNLOAD.replace('{id}', id))
    };

    static categories = {
        getAll: () => API.get(CONFIG.API_ENDPOINTS.CATEGORIES)
    };

    static files = {
        upload: (formData) => API.upload(CONFIG.API_ENDPOINTS.UPLOAD, formData)
    };
}

// تصدير الإعدادات للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        CONFIG,
        API,
        getConfig,
        getMessage,
        getCategory,
        getCategoryName,
        getCategoryIcon,
        getCategoryColor
    };
}
