// إعدادات الموقع العامة

const CONFIG = {
    // معلومات الموقع
    SITE_NAME: 'مكتبتي الحرة',
    SITE_DESCRIPTION: 'المكتبة الرقمية المجانية',
    SITE_VERSION: '1.0.0',
    
    // إعدادات API (للاستخدام المستقبلي)
    API_BASE_URL: '/api',
    API_VERSION: 'v1',
    
    // إعدادات التحميل
    MAX_FILE_SIZE: 50 * 1024 * 1024, // 50 ميجابايت
    ALLOWED_FILE_TYPES: ['pdf'],
    ALLOWED_IMAGE_TYPES: ['jpg', 'jpeg', 'png', 'webp'],
    
    // إعدادات القارئ
    READER: {
        DEFAULT_ZOOM: 100,
        MIN_ZOOM: 50,
        MAX_ZOOM: 200,
        FLIP_SPEED: 600,
        AUTO_FLIP_INTERVAL: 5000
    },
    
    // إعدادات البحث
    SEARCH: {
        MIN_QUERY_LENGTH: 2,
        MAX_RESULTS: 50,
        DEBOUNCE_DELAY: 300
    },
    
    // إعدادات الصفحات
    PAGINATION: {
        BOOKS_PER_PAGE: 12,
        COMMENTS_PER_PAGE: 10,
        USERS_PER_PAGE: 20
    },
    
    // التصنيفات المتاحة
    CATEGORIES: [
        {
            id: 'literature',
            name: 'الأدب',
            icon: 'fas fa-feather-alt',
            color: '#4e73df'
        },
        {
            id: 'science',
            name: 'العلوم',
            icon: 'fas fa-flask',
            color: '#1cc88a'
        },
        {
            id: 'history',
            name: 'التاريخ',
            icon: 'fas fa-landmark',
            color: '#f6c23e'
        },
        {
            id: 'philosophy',
            name: 'الفلسفة',
            icon: 'fas fa-brain',
            color: '#36b9cc'
        },
        {
            id: 'religion',
            name: 'الدين',
            icon: 'fas fa-mosque',
            color: '#858796'
        },
        {
            id: 'children',
            name: 'الأطفال',
            icon: 'fas fa-child',
            color: '#e74a3b'
        },
        {
            id: 'novels',
            name: 'الروايات',
            icon: 'fas fa-book',
            color: '#5a5c69'
        },
        {
            id: 'poetry',
            name: 'الشعر',
            icon: 'fas fa-pen-fancy',
            color: '#6f42c1'
        }
    ],
    
    // رسائل النظام
    MESSAGES: {
        SUCCESS: {
            LOGIN: 'تم تسجيل الدخول بنجاح',
            LOGOUT: 'تم تسجيل الخروج بنجاح',
            BOOK_ADDED: 'تم إضافة الكتاب بنجاح',
            BOOK_UPDATED: 'تم تحديث الكتاب بنجاح',
            BOOK_DELETED: 'تم حذف الكتاب بنجاح',
            BOOKMARK_ADDED: 'تم إضافة العلامة المرجعية',
            BOOKMARK_REMOVED: 'تم إزالة العلامة المرجعية',
            FAVORITE_ADDED: 'تم إضافة الكتاب للمفضلة',
            FAVORITE_REMOVED: 'تم إزالة الكتاب من المفضلة',
            COMMENT_ADDED: 'تم إضافة التعليق بنجاح',
            DOWNLOAD_STARTED: 'تم بدء التحميل بنجاح'
        },
        ERROR: {
            LOGIN_FAILED: 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
            ACCESS_DENIED: 'ليس لديك صلاحية للوصول',
            FILE_TOO_LARGE: 'حجم الملف كبير جداً',
            INVALID_FILE_TYPE: 'نوع الملف غير مدعوم',
            NETWORK_ERROR: 'خطأ في الاتصال بالشبكة',
            UNKNOWN_ERROR: 'حدث خطأ غير متوقع',
            REQUIRED_FIELDS: 'يرجى ملء جميع الحقول المطلوبة',
            INVALID_EMAIL: 'يرجى إدخال بريد إلكتروني صحيح',
            PASSWORD_TOO_SHORT: 'كلمة المرور قصيرة جداً',
            LOGIN_REQUIRED: 'يجب تسجيل الدخول أولاً'
        },
        INFO: {
            LOADING: 'جاري التحميل...',
            SAVING: 'جاري الحفظ...',
            DELETING: 'جاري الحذف...',
            UPLOADING: 'جاري الرفع...',
            PROCESSING: 'جاري المعالجة...',
            NO_RESULTS: 'لا توجد نتائج',
            NO_BOOKS: 'لا توجد كتب',
            NO_COMMENTS: 'لا توجد تعليقات',
            NO_BOOKMARKS: 'لا توجد علامات مرجعية',
            NO_FAVORITES: 'لا توجد مفضلة'
        }
    },
    
    // إعدادات التخزين المحلي
    STORAGE_KEYS: {
        USER_DATA: 'userData',
        IS_LOGGED_IN: 'isLoggedIn',
        THEME: 'theme',
        READER_THEME: 'readerDarkMode',
        FAVORITES: 'favorites',
        BOOKMARKS: 'bookmarks',
        NOTES: 'notes',
        READING_PROGRESS: 'readingProgress',
        SEARCH_HISTORY: 'searchHistory'
    },
    
    // إعدادات الواجهة
    UI: {
        ANIMATION_DURATION: 300,
        TOAST_DURATION: 5000,
        DEBOUNCE_DELAY: 300,
        SCROLL_OFFSET: 100,
        LAZY_LOAD_OFFSET: 200
    },
    
    // إعدادات الأمان
    SECURITY: {
        SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 ساعة
        MAX_LOGIN_ATTEMPTS: 5,
        LOCKOUT_DURATION: 15 * 60 * 1000, // 15 دقيقة
        PASSWORD_MIN_LENGTH: 6
    },
    
    // إعدادات SEO
    SEO: {
        DEFAULT_TITLE: 'مكتبتي الحرة - المكتبة الرقمية المجانية',
        DEFAULT_DESCRIPTION: 'اكتشف عالماً من المعرفة المجانية. آلاف الكتب في مختلف المجالات متاحة للقراءة والتحميل مجاناً',
        DEFAULT_KEYWORDS: 'كتب مجانية, مكتبة رقمية, قراءة أونلاين, تحميل كتب, أدب, علوم, تاريخ',
        SITE_URL: 'https://my-free-library.com'
    },
    
    // إعدادات التحليلات (للاستخدام المستقبلي)
    ANALYTICS: {
        GOOGLE_ANALYTICS_ID: '',
        TRACK_DOWNLOADS: true,
        TRACK_READING_TIME: true,
        TRACK_SEARCH_QUERIES: true
    },
    
    // إعدادات الشبكات الاجتماعية
    SOCIAL: {
        FACEBOOK_URL: '#',
        TWITTER_URL: '#',
        INSTAGRAM_URL: '#',
        YOUTUBE_URL: '#',
        SHARE_ENABLED: true
    },
    
    // إعدادات البريد الإلكتروني (للاستخدام المستقبلي)
    EMAIL: {
        SMTP_HOST: '',
        SMTP_PORT: 587,
        FROM_EMAIL: '<EMAIL>',
        FROM_NAME: 'مكتبتي الحرة'
    }
};

// وظائف مساعدة للوصول للإعدادات
const getConfig = (key) => {
    const keys = key.split('.');
    let value = CONFIG;
    
    for (const k of keys) {
        if (value && typeof value === 'object' && k in value) {
            value = value[k];
        } else {
            return undefined;
        }
    }
    
    return value;
};

const getMessage = (type, key) => {
    return getConfig(`MESSAGES.${type.toUpperCase()}.${key.toUpperCase()}`) || 'رسالة غير معروفة';
};

const getCategory = (id) => {
    return CONFIG.CATEGORIES.find(cat => cat.id === id);
};

const getCategoryName = (id) => {
    const category = getCategory(id);
    return category ? category.name : 'غير محدد';
};

const getCategoryIcon = (id) => {
    const category = getCategory(id);
    return category ? category.icon : 'fas fa-book';
};

const getCategoryColor = (id) => {
    const category = getCategory(id);
    return category ? category.color : '#6c757d';
};

// تصدير الإعدادات للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        CONFIG,
        getConfig,
        getMessage,
        getCategory,
        getCategoryName,
        getCategoryIcon,
        getCategoryColor
    };
}
