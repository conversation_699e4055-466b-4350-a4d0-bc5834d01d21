// مكتبتي الحرة - ملف JavaScript الرئيسي

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة الوضع الليلي
    initDarkMode();
    
    // تحميل الكتب المميزة
    loadFeaturedBooks();
    
    // تهيئة البحث
    initSearch();
    
    // تهيئة التأثيرات المرئية
    initAnimations();
});

// إدارة الوضع الليلي
function initDarkMode() {
    const darkModeToggle = document.getElementById('darkModeToggle');
    const body = document.body;
    
    // التحقق من الوضع المحفوظ
    const savedTheme = localStorage.getItem('theme') || 'light';
    setTheme(savedTheme);
    
    // إضافة مستمع للنقر على زر الوضع الليلي
    if (darkModeToggle) {
        darkModeToggle.addEventListener('click', function() {
            const currentTheme = body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            setTheme(newTheme);
        });
    }
}

function setTheme(theme) {
    const body = document.body;
    const darkModeToggle = document.getElementById('darkModeToggle');
    
    body.setAttribute('data-theme', theme);
    localStorage.setItem('theme', theme);
    
    if (darkModeToggle) {
        const icon = darkModeToggle.querySelector('i');
        if (theme === 'dark') {
            icon.className = 'fas fa-sun';
            darkModeToggle.setAttribute('title', 'الوضع النهاري');
        } else {
            icon.className = 'fas fa-moon';
            darkModeToggle.setAttribute('title', 'الوضع الليلي');
        }
    }
}

// تحميل الكتب المميزة
function loadFeaturedBooks() {
    // بيانات تجريبية للكتب
    const sampleBooks = [
        {
            id: 1,
            title: "مئة عام من العزلة",
            author: "غابرييل غارسيا ماركيز",
            cover: "assets/images/books/book1.jpg",
            category: "أدب",
            rating: 4.8,
            downloads: 1250,
            isNew: true
        },
        {
            id: 2,
            title: "تاريخ الطبري",
            author: "محمد بن جرير الطبري",
            cover: "assets/images/books/book2.jpg",
            category: "تاريخ",
            rating: 4.6,
            downloads: 890,
            isNew: false
        },
        {
            id: 3,
            title: "مقدمة ابن خلدون",
            author: "عبد الرحمن ابن خلدون",
            cover: "assets/images/books/book3.jpg",
            category: "فلسفة",
            rating: 4.9,
            downloads: 2100,
            isNew: true
        },
        {
            id: 4,
            title: "الأسود يليق بك",
            author: "أحلام مستغانمي",
            cover: "assets/images/books/book4.jpg",
            category: "رواية",
            rating: 4.7,
            downloads: 1680,
            isNew: false
        }
    ];
    
    // تحميل أحدث الكتب
    loadBooksToSection('latestBooks', sampleBooks.filter(book => book.isNew));
    
    // تحميل الأكثر قراءة
    loadBooksToSection('popularBooks', sampleBooks.sort((a, b) => b.downloads - a.downloads));
    
    // تحميل اختيارات المحرر
    loadBooksToSection('editorChoice', sampleBooks.sort((a, b) => b.rating - a.rating));
}

function loadBooksToSection(sectionId, books) {
    const section = document.getElementById(sectionId);
    if (!section) return;
    
    section.innerHTML = '';
    
    books.slice(0, 4).forEach(book => {
        const bookCard = createBookCard(book);
        section.appendChild(bookCard);
    });
}

function createBookCard(book) {
    const col = document.createElement('div');
    col.className = 'col-lg-3 col-md-6 mb-4';
    
    col.innerHTML = `
        <div class="card book-card h-100 shadow-sm position-relative">
            ${book.isNew ? '<span class="badge-new">جديد</span>' : ''}
            <div class="card-body p-3">
                <div class="text-center mb-3">
                    <img src="${book.cover}" alt="${book.title}" class="book-cover img-fluid" 
                         onerror="this.src='assets/images/default-book.png'">
                </div>
                <h6 class="card-title fw-bold mb-2" title="${book.title}">
                    ${book.title.length > 30 ? book.title.substring(0, 30) + '...' : book.title}
                </h6>
                <p class="card-text text-muted small mb-2">
                    <i class="fas fa-user me-1"></i>
                    ${book.author}
                </p>
                <p class="card-text text-muted small mb-2">
                    <i class="fas fa-tag me-1"></i>
                    ${book.category}
                </p>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div class="book-rating">
                        ${generateStars(book.rating)}
                        <small class="text-muted ms-1">(${book.rating})</small>
                    </div>
                    <small class="text-muted">
                        <i class="fas fa-download me-1"></i>
                        ${book.downloads}
                    </small>
                </div>
                <div class="d-grid gap-2">
                    <a href="book-details.html?id=${book.id}" class="btn btn-primary btn-sm">
                        <i class="fas fa-eye me-1"></i>
                        عرض التفاصيل
                    </a>
                </div>
            </div>
        </div>
    `;
    
    return col;
}

function generateStars(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    let stars = '';
    
    for (let i = 0; i < fullStars; i++) {
        stars += '<i class="fas fa-star"></i>';
    }
    
    if (hasHalfStar) {
        stars += '<i class="fas fa-star-half-alt"></i>';
    }
    
    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
        stars += '<i class="far fa-star"></i>';
    }
    
    return stars;
}

// تهيئة البحث
function initSearch() {
    const searchForm = document.querySelector('.search-box form');
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const searchInput = this.querySelector('input[type="text"]');
            const categorySelect = this.querySelector('select');
            
            const searchTerm = searchInput.value.trim();
            const category = categorySelect.value;
            
            if (searchTerm) {
                // إعادة توجيه إلى صفحة النتائج
                window.location.href = `search-results.html?q=${encodeURIComponent(searchTerm)}&cat=${category}`;
            }
        });
    }
}

// تهيئة التأثيرات المرئية
function initAnimations() {
    // التحقق من تفضيل المستخدم للحركة المقللة
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

    if (prefersReducedMotion) {
        // إذا كان المستخدم يفضل تقليل الحركة، لا نضيف انيميشن
        return;
    }

    // تأثير الظهور التدريجي للعناصر (مبسط)
    const observerOptions = {
        threshold: 0.3,
        rootMargin: '0px 0px -20px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // مراقبة العناصر للتأثيرات (مقللة)
    document.querySelectorAll('.card').forEach(el => {
        el.style.opacity = '0.8';
        el.style.transition = 'opacity 0.2s ease';
        observer.observe(el);
    });
    
    // تأثير التمرير السلس
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// وظائف مساعدة
function showLoading(element) {
    element.innerHTML = '<div class="text-center p-4"><div class="loading-spinner"></div></div>';
}

function showError(element, message) {
    element.innerHTML = `
        <div class="text-center p-4">
            <i class="fas fa-exclamation-triangle text-warning fa-3x mb-3"></i>
            <p class="text-muted">${message}</p>
        </div>
    `;
}

// تحسين الأداء - تأخير تحميل الصور
function lazyLoadImages() {
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

// تهيئة تأخير تحميل الصور
document.addEventListener('DOMContentLoaded', lazyLoadImages);

// إضافة تأثيرات إضافية للأزرار
document.addEventListener('DOMContentLoaded', function() {
    // تأثير الضوء على الأزرار عند التمرير
    document.querySelectorAll('.btn-primary').forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.classList.add('btn-glow');
        });
        
        btn.addEventListener('mouseleave', function() {
            this.classList.remove('btn-glow');
        });
    });
    
    // تأثير النقر على البطاقات (مبسط)
    document.querySelectorAll('.card').forEach(card => {
        card.addEventListener('click', function(e) {
            // تأثير بسيط بدون حركة
            this.style.opacity = '0.9';
            setTimeout(() => {
                this.style.opacity = '';
            }, 100);
        });
    });
});

// وظيفة البحث المباشر
function liveSearch(query) {
    // هذه الوظيفة ستتصل بـ API البحث في المستقبل
    console.log('البحث عن:', query);
}

// إضافة مستمع للبحث المباشر
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('.search-box input[type="text"]');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                if (this.value.length > 2) {
                    liveSearch(this.value);
                }
            }, 300);
        });
    }
});

// وظيفة إظهار التوست (إذا لم تكن موجودة)
function showToast(message, type = 'info') {
    const toastHTML = `
        <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;

    let toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toastContainer';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }

    toastContainer.insertAdjacentHTML('beforeend', toastHTML);
    const toastElement = toastContainer.lastElementChild;
    const toast = new bootstrap.Toast(toastElement);
    toast.show();

    toastElement.addEventListener('hidden.bs.toast', function() {
        this.remove();
    });
}

/**
 * تهيئة PWA (Progressive Web App)
 */
function initPWA() {
    // تسجيل Service Worker
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('Service Worker registered successfully');

                // التحقق من وجود تحديثات
                registration.addEventListener('updatefound', () => {
                    const newWorker = registration.installing;
                    newWorker.addEventListener('statechange', () => {
                        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                            showUpdateAvailable();
                        }
                    });
                });
            })
            .catch(error => {
                console.log('Service Worker registration failed:', error);
            });
    }

    // إظهار رسالة تثبيت التطبيق
    let deferredPrompt;
    window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        deferredPrompt = e;
        showInstallPrompt();
    });

    // مراقبة حالة الاتصال
    window.addEventListener('online', () => {
        showToast('تم استعادة الاتصال بالإنترنت', 'success');
        syncOfflineData();
    });

    window.addEventListener('offline', () => {
        showToast('فقدان الاتصال بالإنترنت - يمكنك الاستمرار في التصفح', 'warning');
    });
}

/**
 * إظهار رسالة التحديث المتاح
 */
function showUpdateAvailable() {
    const updateBanner = document.createElement('div');
    updateBanner.className = 'alert alert-info alert-dismissible fade show position-fixed';
    updateBanner.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 300px;';
    updateBanner.innerHTML = `
        <i class="fas fa-download me-2"></i>
        <strong>تحديث متاح!</strong>
        <p class="mb-2 small">يتوفر إصدار جديد من التطبيق</p>
        <button class="btn btn-sm btn-primary me-2" onclick="updateApp()">تحديث الآن</button>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(updateBanner);

    // إزالة تلقائية بعد 10 ثوان
    setTimeout(() => {
        if (updateBanner.parentNode) {
            updateBanner.parentNode.removeChild(updateBanner);
        }
    }, 10000);
}

/**
 * تحديث التطبيق
 */
function updateApp() {
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.getRegistration().then(registration => {
            if (registration && registration.waiting) {
                registration.waiting.postMessage({ type: 'SKIP_WAITING' });
                window.location.reload();
            }
        });
    }
}

/**
 * إظهار رسالة تثبيت التطبيق
 */
function showInstallPrompt() {
    // إنشاء شريط التثبيت
    const installBanner = document.createElement('div');
    installBanner.id = 'installBanner';
    installBanner.className = 'alert alert-primary alert-dismissible fade show position-fixed';
    installBanner.style.cssText = 'bottom: 20px; left: 20px; right: 20px; z-index: 9999; margin: 0;';
    installBanner.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-mobile-alt fa-2x me-3 text-primary"></i>
            <div class="flex-grow-1">
                <h6 class="mb-1">ثبت مكتبتي الحرة</h6>
                <small>احصل على تجربة أفضل مع التطبيق</small>
            </div>
            <button class="btn btn-primary btn-sm me-2" onclick="installApp()">تثبيت</button>
            <button type="button" class="btn-close" data-bs-dismiss="alert" onclick="dismissInstallPrompt()"></button>
        </div>
    `;

    document.body.appendChild(installBanner);
}

/**
 * تثبيت التطبيق
 */
function installApp() {
    const deferredPrompt = window.deferredPrompt;
    if (deferredPrompt) {
        deferredPrompt.prompt();
        deferredPrompt.userChoice.then((choiceResult) => {
            if (choiceResult.outcome === 'accepted') {
                showToast('تم تثبيت التطبيق بنجاح!', 'success');
            }
            window.deferredPrompt = null;
            dismissInstallPrompt();
        });
    }
}

/**
 * إخفاء رسالة التثبيت
 */
function dismissInstallPrompt() {
    const installBanner = document.getElementById('installBanner');
    if (installBanner) {
        installBanner.remove();
    }

    // حفظ تفضيل المستخدم
    localStorage.setItem('installPromptDismissed', 'true');
}

/**
 * مزامنة البيانات المحفوظة محلياً
 */
function syncOfflineData() {
    const offlineData = JSON.parse(localStorage.getItem('offlineData') || '[]');

    if (offlineData.length > 0) {
        showToast('جاري مزامنة البيانات...', 'info');

        Promise.all(offlineData.map(item =>
            fetch(item.url, item.options)
                .then(response => response.json())
                .catch(error => console.error('فشل في مزامنة:', error))
        )).then(() => {
            localStorage.removeItem('offlineData');
            showToast('تم مزامنة البيانات بنجاح', 'success');
        });
    }
}

// تهيئة PWA عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة PWA
    initPWA();
});

// تصدير الوظائف للاستخدام العام
window.initPWA = initPWA;
window.updateApp = updateApp;
window.installApp = installApp;
window.dismissInstallPrompt = dismissInstallPrompt;
