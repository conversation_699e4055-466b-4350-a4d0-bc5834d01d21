/**
 * ملف تحسين الأداء - مكتبتي الحرة
 * Performance Optimization - My Free Library
 */

class PerformanceOptimizer {
    constructor() {
        this.cache = new Map();
        this.imageObserver = null;
        this.loadingQueue = [];
        this.isOnline = navigator.onLine;
        
        this.init();
    }
    
    init() {
        this.setupLazyLoading();
        this.setupImageOptimization();
        this.setupOfflineSupport();
        this.setupPreloading();
        this.setupCaching();
        this.monitorPerformance();
    }
    
    /**
     * تحميل الصور بشكل تدريجي (Lazy Loading)
     */
    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            this.imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        this.loadImage(img);
                        this.imageObserver.unobserve(img);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });
            
            this.observeImages();
        } else {
            // Fallback للمتصفحات القديمة
            this.loadAllImages();
        }
    }
    
    observeImages() {
        const lazyImages = document.querySelectorAll('img[data-src]');
        lazyImages.forEach(img => {
            this.imageObserver.observe(img);
        });
    }
    
    loadImage(img) {
        const src = img.dataset.src;
        if (!src) return;
        
        // إنشاء صورة جديدة للتحميل المسبق
        const imageLoader = new Image();
        
        imageLoader.onload = () => {
            img.src = src;
            img.classList.add('loaded');
            img.removeAttribute('data-src');
        };
        
        imageLoader.onerror = () => {
            img.src = 'assets/images/default-book.png';
            img.classList.add('error');
        };
        
        imageLoader.src = src;
    }
    
    loadAllImages() {
        const lazyImages = document.querySelectorAll('img[data-src]');
        lazyImages.forEach(img => this.loadImage(img));
    }
    
    /**
     * تحسين الصور
     */
    setupImageOptimization() {
        // تحويل الصور إلى WebP إذا كان المتصفح يدعمها
        if (this.supportsWebP()) {
            this.convertImagesToWebP();
        }
        
        // ضغط الصور قبل الرفع
        this.setupImageCompression();
    }
    
    supportsWebP() {
        const canvas = document.createElement('canvas');
        canvas.width = 1;
        canvas.height = 1;
        return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
    }
    
    convertImagesToWebP() {
        const images = document.querySelectorAll('img[src$=".jpg"], img[src$=".jpeg"], img[src$=".png"]');
        images.forEach(img => {
            const webpSrc = img.src.replace(/\.(jpg|jpeg|png)$/, '.webp');
            
            // اختبار وجود النسخة WebP
            const testImg = new Image();
            testImg.onload = () => {
                img.src = webpSrc;
            };
            testImg.src = webpSrc;
        });
    }
    
    setupImageCompression() {
        const fileInputs = document.querySelectorAll('input[type="file"][accept*="image"]');
        fileInputs.forEach(input => {
            input.addEventListener('change', (e) => {
                const files = Array.from(e.target.files);
                Promise.all(files.map(file => this.compressImage(file)))
                    .then(compressedFiles => {
                        // استبدال الملفات الأصلية بالمضغوطة
                        const dt = new DataTransfer();
                        compressedFiles.forEach(file => dt.items.add(file));
                        input.files = dt.files;
                    });
            });
        });
    }
    
    compressImage(file, quality = 0.8, maxWidth = 800) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            
            img.onload = () => {
                // حساب الأبعاد الجديدة
                const ratio = Math.min(maxWidth / img.width, maxWidth / img.height);
                canvas.width = img.width * ratio;
                canvas.height = img.height * ratio;
                
                // رسم الصورة المضغوطة
                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
                
                // تحويل إلى Blob
                canvas.toBlob(resolve, 'image/jpeg', quality);
            };
            
            img.src = URL.createObjectURL(file);
        });
    }
    
    /**
     * دعم العمل بدون اتصال
     */
    setupOfflineSupport() {
        // تسجيل Service Worker
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('Service Worker registered:', registration);
                })
                .catch(error => {
                    console.log('Service Worker registration failed:', error);
                });
        }
        
        // مراقبة حالة الاتصال
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.syncOfflineData();
            this.showConnectionStatus('متصل', 'success');
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.showConnectionStatus('غير متصل', 'warning');
        });
    }
    
    showConnectionStatus(message, type) {
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-wifi me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        
        document.body.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 5000);
    }
    
    syncOfflineData() {
        // مزامنة البيانات المحفوظة محلياً
        const offlineData = JSON.parse(localStorage.getItem('offlineData') || '[]');
        
        offlineData.forEach(async (item) => {
            try {
                await API.request(item.url, item.options);
                // إزالة العنصر من البيانات المحلية بعد المزامنة
                const index = offlineData.indexOf(item);
                offlineData.splice(index, 1);
            } catch (error) {
                console.error('فشل في مزامنة البيانات:', error);
            }
        });
        
        localStorage.setItem('offlineData', JSON.stringify(offlineData));
    }
    
    /**
     * التحميل المسبق للموارد
     */
    setupPreloading() {
        // تحميل مسبق للصفحات المهمة
        this.preloadCriticalResources();
        
        // تحميل مسبق عند التمرير فوق الروابط
        this.setupLinkPreloading();
    }
    
    preloadCriticalResources() {
        const criticalResources = [
            '/api/categories',
            '/api/books?featured=true&limit=6'
        ];
        
        criticalResources.forEach(url => {
            if (!this.cache.has(url)) {
                this.loadingQueue.push(() => this.preloadResource(url));
            }
        });
        
        // تنفيذ قائمة التحميل
        this.processLoadingQueue();
    }
    
    async preloadResource(url) {
        try {
            const response = await fetch(url);
            const data = await response.json();
            this.cache.set(url, {
                data: data,
                timestamp: Date.now(),
                expires: Date.now() + (5 * 60 * 1000) // 5 دقائق
            });
        } catch (error) {
            console.error('فشل في التحميل المسبق:', error);
        }
    }
    
    setupLinkPreloading() {
        const links = document.querySelectorAll('a[href^="/"], a[href^="./"]');
        
        links.forEach(link => {
            link.addEventListener('mouseenter', () => {
                const href = link.getAttribute('href');
                if (href && !this.cache.has(href)) {
                    this.preloadPage(href);
                }
            });
        });
    }
    
    preloadPage(url) {
        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = url;
        document.head.appendChild(link);
    }
    
    processLoadingQueue() {
        if (this.loadingQueue.length === 0) return;
        
        const batchSize = 2; // تحميل مورديْن في المرة الواحدة
        const batch = this.loadingQueue.splice(0, batchSize);
        
        Promise.all(batch.map(loader => loader()))
            .then(() => {
                // تأخير قصير قبل المجموعة التالية
                setTimeout(() => this.processLoadingQueue(), 100);
            });
    }
    
    /**
     * نظام التخزين المؤقت
     */
    setupCaching() {
        // تخزين مؤقت للـ API
        this.setupAPICache();
        
        // تنظيف التخزين المؤقت المنتهي الصلاحية
        setInterval(() => this.cleanExpiredCache(), 60000); // كل دقيقة
    }
    
    setupAPICache() {
        const originalFetch = window.fetch;
        
        window.fetch = async (url, options = {}) => {
            // تخزين مؤقت فقط لطلبات GET
            if (options.method && options.method !== 'GET') {
                return originalFetch(url, options);
            }
            
            const cacheKey = url + JSON.stringify(options);
            const cached = this.cache.get(cacheKey);
            
            if (cached && cached.expires > Date.now()) {
                return new Response(JSON.stringify(cached.data), {
                    status: 200,
                    headers: { 'Content-Type': 'application/json' }
                });
            }
            
            const response = await originalFetch(url, options);
            
            if (response.ok && response.headers.get('content-type')?.includes('application/json')) {
                const data = await response.clone().json();
                this.cache.set(cacheKey, {
                    data: data,
                    timestamp: Date.now(),
                    expires: Date.now() + (2 * 60 * 1000) // دقيقتان
                });
            }
            
            return response;
        };
    }
    
    cleanExpiredCache() {
        const now = Date.now();
        for (const [key, value] of this.cache.entries()) {
            if (value.expires < now) {
                this.cache.delete(key);
            }
        }
    }
    
    /**
     * مراقبة الأداء
     */
    monitorPerformance() {
        // مراقبة أوقات التحميل
        window.addEventListener('load', () => {
            setTimeout(() => {
                const perfData = performance.getEntriesByType('navigation')[0];
                
                const metrics = {
                    loadTime: perfData.loadEventEnd - perfData.loadEventStart,
                    domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
                    firstPaint: this.getFirstPaint(),
                    largestContentfulPaint: this.getLargestContentfulPaint()
                };
                
                this.reportPerformanceMetrics(metrics);
            }, 0);
        });
        
        // مراقبة استخدام الذاكرة
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.9) {
                    console.warn('استخدام ذاكرة عالي');
                    this.optimizeMemoryUsage();
                }
            }, 30000); // كل 30 ثانية
        }
    }
    
    getFirstPaint() {
        const paintEntries = performance.getEntriesByType('paint');
        const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
        return firstPaint ? firstPaint.startTime : 0;
    }
    
    getLargestContentfulPaint() {
        return new Promise((resolve) => {
            new PerformanceObserver((list) => {
                const entries = list.getEntries();
                const lastEntry = entries[entries.length - 1];
                resolve(lastEntry.startTime);
            }).observe({ entryTypes: ['largest-contentful-paint'] });
        });
    }
    
    reportPerformanceMetrics(metrics) {
        // إرسال المقاييس للخادم (اختياري)
        if (DEBUG_MODE) {
            console.log('Performance Metrics:', metrics);
        }
        
        // تحذير إذا كان الأداء بطيئاً
        if (metrics.loadTime > 3000) {
            console.warn('تحميل بطيء للصفحة');
        }
    }
    
    optimizeMemoryUsage() {
        // تنظيف التخزين المؤقت
        this.cache.clear();
        
        // إزالة مستمعي الأحداث غير المستخدمين
        this.cleanupEventListeners();
        
        // تشغيل garbage collection إذا كان متاحاً
        if (window.gc) {
            window.gc();
        }
    }
    
    cleanupEventListeners() {
        // إزالة مستمعي الأحداث من العناصر المحذوفة
        const elements = document.querySelectorAll('[data-cleanup]');
        elements.forEach(element => {
            element.removeEventListener('click', element._clickHandler);
            element.removeEventListener('change', element._changeHandler);
        });
    }
    
    /**
     * تحسين التمرير
     */
    optimizeScrolling() {
        let ticking = false;
        
        const updateScrollPosition = () => {
            // تحديث موضع التمرير
            const scrollTop = window.pageYOffset;
            
            // إخفاء/إظهار شريط التنقل
            const navbar = document.querySelector('.navbar');
            if (navbar) {
                if (scrollTop > 100) {
                    navbar.classList.add('navbar-scrolled');
                } else {
                    navbar.classList.remove('navbar-scrolled');
                }
            }
            
            ticking = false;
        };
        
        const requestScrollUpdate = () => {
            if (!ticking) {
                requestAnimationFrame(updateScrollPosition);
                ticking = true;
            }
        };
        
        window.addEventListener('scroll', requestScrollUpdate, { passive: true });
    }
}

// تهيئة محسن الأداء
const performanceOptimizer = new PerformanceOptimizer();

// تصدير للاستخدام العام
window.PerformanceOptimizer = PerformanceOptimizer;
