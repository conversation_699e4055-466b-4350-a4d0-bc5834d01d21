// قارئ الكتب - JavaScript

class BookReader {
    constructor() {
        this.currentPage = 1;
        this.totalPages = 417;
        this.zoomLevel = 100;
        this.viewMode = 'flipbook'; // flipbook or pdf
        this.isFullscreen = false;
        this.isDarkMode = false;
        this.bookmarks = JSON.parse(localStorage.getItem('bookmarks') || '[]');
        this.notes = JSON.parse(localStorage.getItem('notes') || '[]');
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadBook();
        this.updateUI();
        this.loadBookmarks();
        this.loadNotes();
    }
    
    setupEventListeners() {
        // أزرار التنقل
        document.getElementById('prevPage').addEventListener('click', () => this.previousPage());
        document.getElementById('nextPage').addEventListener('click', () => this.nextPage());
        document.getElementById('currentPage').addEventListener('change', (e) => this.goToPage(parseInt(e.target.value)));
        
        // أزرار التكبير
        document.getElementById('zoomIn').addEventListener('click', () => this.zoomIn());
        document.getElementById('zoomOut').addEventListener('click', () => this.zoomOut());
        
        // تبديل وضع العرض
        document.getElementById('viewModeToggle').addEventListener('click', () => this.toggleViewMode());
        
        // ملء الشاشة
        document.getElementById('fullscreenToggle').addEventListener('click', () => this.toggleFullscreen());
        
        // الوضع الليلي
        document.getElementById('readerDarkMode').addEventListener('click', () => this.toggleDarkMode());
        
        // العلامات المرجعية
        document.getElementById('addBookmark').addEventListener('click', () => this.showBookmarkModal());
        document.getElementById('saveBookmark').addEventListener('click', () => this.saveBookmark());
        document.getElementById('showBookmarks').addEventListener('click', () => this.toggleSidebar());
        
        // الملاحظات
        document.getElementById('saveNote').addEventListener('click', () => this.saveNote());
        
        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => this.handleKeyboard(e));
        
        // التقليب التلقائي
        document.getElementById('autoFlip').addEventListener('change', (e) => this.toggleAutoFlip(e.target.checked));
        
        // سرعة التقليب
        document.getElementById('flipSpeed').addEventListener('input', (e) => this.setFlipSpeed(e.target.value));
        
        // المحتويات
        document.querySelectorAll('.contents-list a').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = parseInt(e.target.dataset.page);
                this.goToPage(page);
            });
        });
    }
    
    loadBook() {
        // محاكاة تحميل الكتاب
        setTimeout(() => {
            this.initFlipbook();
            this.generatePages();
        }, 1000);
    }
    
    initFlipbook() {
        const flipbook = document.getElementById('flipbook');
        
        // إزالة محتوى التحميل
        flipbook.innerHTML = '';
        
        // إنشاء صفحات الكتاب
        for (let i = 1; i <= Math.min(this.totalPages, 20); i++) {
            const page = document.createElement('div');
            page.className = 'page';
            page.innerHTML = this.generatePageContent(i);
            flipbook.appendChild(page);
        }
        
        // تهيئة Turn.js
        if (typeof $ !== 'undefined' && $.fn.turn) {
            $(flipbook).turn({
                width: 800,
                height: 600,
                autoCenter: true,
                duration: parseInt(document.getElementById('flipSpeed').value),
                when: {
                    turned: (event, page) => {
                        this.currentPage = page;
                        this.updateUI();
                    }
                }
            });
        }
    }
    
    generatePageContent(pageNumber) {
        const sampleTexts = [
            "في مكان ما من لامانتشا، التي لا أريد أن أذكر اسمها، عاش منذ زمن ليس ببعيد نبيل من أولئك الذين يحتفظون برمح في رف، وترس قديم، وحصان هزيل، وكلب صيد سريع.",
            "كان عمره يناهز الخمسين، قوي البنية، نحيف الوجه، نهم في الصباح الباكر، وصديق للصيد. يقولون إن لقبه كان كيخادا أو كيسادا، وفي هذا خلاف بين المؤلفين الذين كتبوا عن هذا الموضوع.",
            "ولكن هذا لا يهم كثيراً لقصتنا، بشرط ألا نحيد عن الحقيقة في روايتها. يجب أن تعلم إذن أن هذا النبيل المذكور، في الأوقات التي كان فيها عاطلاً عن العمل، كان يقضي وقته في قراءة كتب الفروسية.",
            "وكان مولعاً بها إلى درجة أنه نسي تقريباً ممارسة الصيد، وحتى إدارة ممتلكاته. ووصل به الفضول والجنون إلى حد أنه باع عدة فدادين من الأراضي الزراعية لشراء كتب الفروسية ليقرأها."
        ];
        
        const randomText = sampleTexts[Math.floor(Math.random() * sampleTexts.length)];
        
        return `
            <div class="page-content">
                <div class="page-number">صفحة ${pageNumber}</div>
                <h3>الفصل ${Math.ceil(pageNumber / 20)}</h3>
                <p>${randomText}</p>
                <p>هذا نص تجريبي لمحاكاة محتوى الكتاب. في التطبيق الحقيقي، سيتم تحميل المحتوى الفعلي للكتاب من ملف PDF أو قاعدة البيانات.</p>
                <p>يمكن إضافة المزيد من النصوص والصور والتنسيقات حسب محتوى الكتاب الأصلي.</p>
            </div>
        `;
    }
    
    generatePages() {
        // تحديث شريط التقدم
        this.updateProgress();
    }
    
    previousPage() {
        if (this.currentPage > 1) {
            if (this.viewMode === 'flipbook' && typeof $ !== 'undefined' && $.fn.turn) {
                $('#flipbook').turn('previous');
            } else {
                this.currentPage--;
                this.updateUI();
            }
        }
    }
    
    nextPage() {
        if (this.currentPage < this.totalPages) {
            if (this.viewMode === 'flipbook' && typeof $ !== 'undefined' && $.fn.turn) {
                $('#flipbook').turn('next');
            } else {
                this.currentPage++;
                this.updateUI();
            }
        }
    }
    
    goToPage(page) {
        if (page >= 1 && page <= this.totalPages) {
            this.currentPage = page;
            if (this.viewMode === 'flipbook' && typeof $ !== 'undefined' && $.fn.turn) {
                $('#flipbook').turn('page', page);
            }
            this.updateUI();
        }
    }
    
    zoomIn() {
        if (this.zoomLevel < 200) {
            this.zoomLevel += 10;
            this.applyZoom();
        }
    }
    
    zoomOut() {
        if (this.zoomLevel > 50) {
            this.zoomLevel -= 10;
            this.applyZoom();
        }
    }
    
    applyZoom() {
        const container = document.querySelector('.flipbook-container');
        container.style.transform = `scale(${this.zoomLevel / 100})`;
        document.getElementById('zoomLevel').textContent = `${this.zoomLevel}%`;
    }
    
    toggleViewMode() {
        this.viewMode = this.viewMode === 'flipbook' ? 'pdf' : 'flipbook';
        const flipbook = document.getElementById('flipbook');
        const pdfViewer = document.getElementById('pdfViewer');
        const toggleBtn = document.getElementById('viewModeToggle');
        
        if (this.viewMode === 'pdf') {
            flipbook.classList.add('d-none');
            pdfViewer.classList.remove('d-none');
            toggleBtn.innerHTML = '<i class="fas fa-book"></i>';
            toggleBtn.title = 'وضع الكتاب المقلب';
        } else {
            flipbook.classList.remove('d-none');
            pdfViewer.classList.add('d-none');
            toggleBtn.innerHTML = '<i class="fas fa-file-pdf"></i>';
            toggleBtn.title = 'وضع PDF';
        }
    }
    
    toggleFullscreen() {
        const readerContent = document.querySelector('.reader-content');
        const toggleBtn = document.getElementById('fullscreenToggle');
        
        if (!this.isFullscreen) {
            readerContent.classList.add('fullscreen-mode');
            toggleBtn.innerHTML = '<i class="fas fa-compress"></i>';
            toggleBtn.title = 'إنهاء ملء الشاشة';
            this.isFullscreen = true;
        } else {
            readerContent.classList.remove('fullscreen-mode');
            toggleBtn.innerHTML = '<i class="fas fa-expand"></i>';
            toggleBtn.title = 'ملء الشاشة';
            this.isFullscreen = false;
        }
    }
    
    toggleDarkMode() {
        const body = document.body;
        const toggleBtn = document.getElementById('readerDarkMode');
        
        if (!this.isDarkMode) {
            body.setAttribute('data-theme', 'dark');
            toggleBtn.innerHTML = '<i class="fas fa-sun"></i>';
            toggleBtn.title = 'الوضع النهاري';
            this.isDarkMode = true;
        } else {
            body.removeAttribute('data-theme');
            toggleBtn.innerHTML = '<i class="fas fa-moon"></i>';
            toggleBtn.title = 'الوضع الليلي';
            this.isDarkMode = false;
        }
        
        localStorage.setItem('readerDarkMode', this.isDarkMode);
    }
    
    toggleSidebar() {
        const sidebar = document.getElementById('readerSidebar');
        const main = document.getElementById('readerMain');
        
        if (sidebar.classList.contains('d-none')) {
            sidebar.classList.remove('d-none');
            main.classList.remove('col');
            main.classList.add('col-md-9');
        } else {
            sidebar.classList.add('d-none');
            main.classList.remove('col-md-9');
            main.classList.add('col');
        }
    }
    
    showBookmarkModal() {
        document.getElementById('bookmarkPage').value = this.currentPage;
        document.getElementById('bookmarkTitle').value = `صفحة ${this.currentPage}`;
        const modal = new bootstrap.Modal(document.getElementById('bookmarkModal'));
        modal.show();
    }
    
    saveBookmark() {
        const title = document.getElementById('bookmarkTitle').value;
        const note = document.getElementById('bookmarkNote').value;
        const page = parseInt(document.getElementById('bookmarkPage').value);
        
        if (title.trim()) {
            const bookmark = {
                id: Date.now(),
                title: title.trim(),
                note: note.trim(),
                page: page,
                date: new Date().toLocaleDateString('ar-SA')
            };
            
            this.bookmarks.push(bookmark);
            localStorage.setItem('bookmarks', JSON.stringify(this.bookmarks));
            this.loadBookmarks();
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('bookmarkModal'));
            modal.hide();
            
            this.showToast('تم حفظ العلامة المرجعية', 'success');
        }
    }
    
    loadBookmarks() {
        const container = document.querySelector('.bookmarks-list');
        container.innerHTML = '';
        
        if (this.bookmarks.length === 0) {
            container.innerHTML = '<p class="text-muted small">لا توجد علامات مرجعية</p>';
            return;
        }
        
        this.bookmarks.forEach(bookmark => {
            const item = document.createElement('div');
            item.className = 'bookmark-item';
            item.innerHTML = `
                <div class="bookmark-title">${bookmark.title}</div>
                <div class="bookmark-page">صفحة ${bookmark.page} - ${bookmark.date}</div>
                ${bookmark.note ? `<div class="bookmark-note small text-muted mt-1">${bookmark.note}</div>` : ''}
            `;
            
            item.addEventListener('click', () => this.goToPage(bookmark.page));
            container.appendChild(item);
        });
    }
    
    saveNote() {
        const content = document.getElementById('noteContent').value;
        const page = this.currentPage;
        
        if (content.trim()) {
            const note = {
                id: Date.now(),
                content: content.trim(),
                page: page,
                date: new Date().toLocaleDateString('ar-SA')
            };
            
            this.notes.push(note);
            localStorage.setItem('notes', JSON.stringify(this.notes));
            this.loadNotes();
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('noteModal'));
            modal.hide();
            
            this.showToast('تم حفظ الملاحظة', 'success');
        }
    }
    
    loadNotes() {
        const container = document.querySelector('.notes-list');
        container.innerHTML = '';
        
        if (this.notes.length === 0) {
            container.innerHTML = '<p class="text-muted small">لا توجد ملاحظات</p>';
            return;
        }
        
        this.notes.forEach(note => {
            const item = document.createElement('div');
            item.className = 'note-item';
            item.innerHTML = `
                <div class="note-content">${note.content}</div>
                <div class="note-meta">صفحة ${note.page} - ${note.date}</div>
            `;
            
            item.addEventListener('click', () => this.goToPage(note.page));
            container.appendChild(item);
        });
    }
    
    handleKeyboard(e) {
        switch(e.key) {
            case 'ArrowLeft':
                e.preventDefault();
                this.nextPage();
                break;
            case 'ArrowRight':
                e.preventDefault();
                this.previousPage();
                break;
            case 'Home':
                e.preventDefault();
                this.goToPage(1);
                break;
            case 'End':
                e.preventDefault();
                this.goToPage(this.totalPages);
                break;
            case 'F11':
                e.preventDefault();
                this.toggleFullscreen();
                break;
            case 'Escape':
                if (this.isFullscreen) {
                    e.preventDefault();
                    this.toggleFullscreen();
                }
                break;
        }
    }
    
    toggleAutoFlip(enabled) {
        if (enabled) {
            this.autoFlipInterval = setInterval(() => {
                if (this.currentPage < this.totalPages) {
                    this.nextPage();
                } else {
                    this.toggleAutoFlip(false);
                    document.getElementById('autoFlip').checked = false;
                }
            }, 5000);
        } else {
            if (this.autoFlipInterval) {
                clearInterval(this.autoFlipInterval);
                this.autoFlipInterval = null;
            }
        }
    }
    
    setFlipSpeed(speed) {
        if (typeof $ !== 'undefined' && $.fn.turn) {
            $('#flipbook').turn('duration', parseInt(speed));
        }
    }
    
    updateUI() {
        document.getElementById('currentPage').value = this.currentPage;
        document.getElementById('totalPages').textContent = this.totalPages;
        this.updateProgress();
    }
    
    updateProgress() {
        const progress = (this.currentPage / this.totalPages) * 100;
        document.getElementById('progressBar').style.width = `${progress}%`;
    }
    
    showToast(message, type = 'info') {
        // استخدام نفس وظيفة التوست من الملف الرئيسي
        if (typeof showToast === 'function') {
            showToast(message, type);
        }
    }
}

// تهيئة القارئ عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    window.bookReader = new BookReader();
    
    // تحميل الوضع الليلي المحفوظ
    const savedDarkMode = localStorage.getItem('readerDarkMode') === 'true';
    if (savedDarkMode) {
        window.bookReader.toggleDarkMode();
    }
});
