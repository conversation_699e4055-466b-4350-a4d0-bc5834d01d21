/**
 * ملف ضمان الاستقرار - مكتبتي الحرة
 * Stability Assurance - My Free Library
 */

(function() {
    'use strict';
    
    /**
     * تهيئة ضمان الاستقرار
     */
    function initStability() {
        // منع الحركة للمستخدمين الذين يفضلون ذلك
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            disableAllAnimations();
        }
        
        // منع التمرير الأفقي
        preventHorizontalScroll();
        
        // تحسين الصور
        optimizeImages();
        
        // منع الحركة غير المرغوب فيها
        preventUnwantedMovement();
        
        // تحسين الأداء
        optimizePerformance();
        
        console.log('تم تفعيل نظام ضمان الاستقرار');
    }
    
    /**
     * إلغاء جميع الانيميشن
     */
    function disableAllAnimations() {
        const style = document.createElement('style');
        style.textContent = `
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
                scroll-behavior: auto !important;
            }
        `;
        document.head.appendChild(style);
    }
    
    /**
     * منع التمرير الأفقي
     */
    function preventHorizontalScroll() {
        document.body.style.overflowX = 'hidden';
        document.documentElement.style.overflowX = 'hidden';
        
        // مراقبة العناصر التي قد تسبب تمرير أفقي
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // Element node
                            checkElementWidth(node);
                        }
                    });
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    /**
     * فحص عرض العنصر
     */
    function checkElementWidth(element) {
        if (element.scrollWidth > document.documentElement.clientWidth) {
            element.style.maxWidth = '100%';
            element.style.overflowX = 'hidden';
        }
    }
    
    /**
     * تحسين الصور
     */
    function optimizeImages() {
        const images = document.querySelectorAll('img');
        
        images.forEach(function(img) {
            // منع تحرك الصور أثناء التحميل
            img.style.maxWidth = '100%';
            img.style.height = 'auto';
            img.style.display = 'block';
            
            // إضافة placeholder أثناء التحميل
            if (!img.complete) {
                img.style.backgroundColor = '#f8f9fa';
                img.style.minHeight = '200px';
                
                img.addEventListener('load', function() {
                    this.style.backgroundColor = '';
                    this.style.minHeight = '';
                });
            }
        });
    }
    
    /**
     * منع الحركة غير المرغوب فيها
     */
    function preventUnwantedMovement() {
        // منع تأثيرات hover المزعجة
        const style = document.createElement('style');
        style.textContent = `
            .card:hover,
            .btn:hover,
            .book-card:hover {
                transform: none !important;
            }
            
            .hero-image {
                animation: none !important;
                transform: none !important;
            }
            
            .fade-in {
                animation: none !important;
                opacity: 1 !important;
                transform: none !important;
            }
        `;
        document.head.appendChild(style);
        
        // إزالة event listeners المسببة للحركة
        removeMovementEventListeners();
    }
    
    /**
     * إزالة مستمعي الأحداث المسببة للحركة
     */
    function removeMovementEventListeners() {
        const elements = document.querySelectorAll('.card, .btn, .book-card');
        
        elements.forEach(function(element) {
            // إنشاء نسخة جديدة من العنصر بدون event listeners
            const newElement = element.cloneNode(true);
            element.parentNode.replaceChild(newElement, element);
        });
    }
    
    /**
     * تحسين الأداء
     */
    function optimizePerformance() {
        // تحسين الرسم
        const style = document.createElement('style');
        style.textContent = `
            .card, .btn, .book-card {
                will-change: auto;
                backface-visibility: hidden;
                transform: translateZ(0);
            }
            
            .hero-section, .section {
                contain: layout style paint;
            }
        `;
        document.head.appendChild(style);
        
        // تحسين التمرير
        optimizeScrolling();
    }
    
    /**
     * تحسين التمرير
     */
    function optimizeScrolling() {
        let ticking = false;
        
        function updateScrollPosition() {
            // تحديث موضع التمرير بدون حركة
            const scrollTop = window.pageYOffset;
            
            // تحديث شريط التنقل بدون انيميشن
            const navbar = document.querySelector('.navbar');
            if (navbar) {
                if (scrollTop > 100) {
                    navbar.style.backgroundColor = 'rgba(255, 255, 255, 0.95)';
                    navbar.style.backdropFilter = 'blur(10px)';
                } else {
                    navbar.style.backgroundColor = '';
                    navbar.style.backdropFilter = '';
                }
            }
            
            ticking = false;
        }
        
        function requestScrollUpdate() {
            if (!ticking) {
                requestAnimationFrame(updateScrollPosition);
                ticking = true;
            }
        }
        
        window.addEventListener('scroll', requestScrollUpdate, { passive: true });
    }
    
    /**
     * مراقبة تغيير حجم النافذة
     */
    function handleResize() {
        window.addEventListener('resize', function() {
            // منع أي حركة أثناء تغيير الحجم
            document.body.style.transition = 'none';
            
            setTimeout(function() {
                document.body.style.transition = '';
                
                // إعادة فحص عرض العناصر
                const elements = document.querySelectorAll('*');
                elements.forEach(checkElementWidth);
            }, 100);
        });
    }
    
    /**
     * إصلاح المشاكل الشائعة
     */
    function fixCommonIssues() {
        // إصلاح مشكلة Bootstrap
        const rows = document.querySelectorAll('.row');
        rows.forEach(function(row) {
            row.style.marginLeft = '0';
            row.style.marginRight = '0';
        });
        
        // إصلاح مشكلة الأعمدة
        const cols = document.querySelectorAll('[class*="col-"]');
        cols.forEach(function(col) {
            col.style.paddingLeft = '15px';
            col.style.paddingRight = '15px';
        });
        
        // إصلاح مشكلة الحاويات
        const containers = document.querySelectorAll('.container, .container-fluid');
        containers.forEach(function(container) {
            container.style.overflowX = 'hidden';
        });
    }
    
    /**
     * تشغيل النظام عند تحميل الصفحة
     */
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            initStability();
            handleResize();
            fixCommonIssues();
        });
    } else {
        initStability();
        handleResize();
        fixCommonIssues();
    }
    
    // تشغيل إضافي بعد تحميل كامل
    window.addEventListener('load', function() {
        setTimeout(function() {
            fixCommonIssues();
            optimizeImages();
        }, 100);
    });
    
})();
