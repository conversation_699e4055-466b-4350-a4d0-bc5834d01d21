<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>جميع الكتب - مكتبتي الحرة</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">

    <!-- CSS محسن للجمال والتفاعل -->
    <style>
        /* خلفية جميلة */
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            overflow-x: hidden;
            position: relative;
        }

        /* تحسين رأس الصفحة */
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0 2rem;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,255,0.1)"><polygon points="1000,100 1000,0 0,100"/></svg>');
            background-size: cover;
        }

        .page-header h1 {
            font-size: 3rem;
            font-weight: 800;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            margin-bottom: 1rem;
            animation: slideInDown 1s ease-out;
        }

        .page-header p {
            font-size: 1.2rem;
            opacity: 0.9;
            animation: slideInUp 1s ease-out 0.2s both;
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* تحسين قسم الفلاتر */
        .filters-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            margin: -3rem auto 3rem;
            position: relative;
            z-index: 10;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .filters-section:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
        }

        /* تحسين أزرار الفلتر */
        .filter-btn {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #e9ecef;
            color: #495057;
            border-radius: 50px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
        }

        .filter-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s ease;
        }

        .filter-btn:hover::before {
            left: 100%;
        }

        .filter-btn:hover,
        .filter-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(103, 126, 234, 0.3);
        }

        /* تحسين عدادات النتائج */
        .results-info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* تحسين أزرار العرض */
        .view-toggle .btn {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            background: white;
            color: #6c757d;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .view-toggle .btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
            color: white;
            box-shadow: 0 4px 15px rgba(103, 126, 234, 0.3);
        }

        .view-toggle .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.1);
        }

        /* تحسين البطاقات */
        .book-card {
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0,0,0,0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
        }

        .book-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transform: scaleX(0);
            transition: transform 0.3s ease;
            transform-origin: left;
        }

        .book-card:hover::before {
            transform: scaleX(1);
        }

        .book-card:hover {
            transform: translateY(-12px) scale(1.02);
            box-shadow: 0 20px 60px rgba(103, 126, 234, 0.15);
        }

        .book-card img {
            transition: all 0.4s ease;
            object-fit: cover;
        }

        .book-card:hover img {
            transform: scale(1.1);
            filter: brightness(1.1) saturate(1.2);
        }

        /* تحسين الشارات */
        .badge {
            font-size: 0.75rem;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s ease;
        }

        .badge:hover::before {
            left: 100%;
        }

        .badge.bg-success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
        }

        .badge.bg-warning {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%) !important;
        }

        .badge.bg-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        }

        /* تحسين التصنيفات */
        .category-badge {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #2d3748;
            border-radius: 50px;
            padding: 0.25rem 0.75rem;
            font-size: 0.8rem;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        /* تحسين التقييمات */
        .rating .fa-star {
            color: #fbbf24;
            filter: drop-shadow(0 1px 2px rgba(251, 191, 36, 0.3));
            transition: all 0.2s ease;
        }

        .rating .fa-star:hover {
            transform: scale(1.2);
            color: #f59e0b;
        }

        /* تحسين الأزرار */
        .btn {
            border-radius: 50px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
            border: none;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transition: all 0.3s ease;
            transform: translate(-50%, -50%);
        }

        .btn:hover::before {
            width: 200px;
            height: 200px;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .btn-success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .btn-outline-success {
            border: 2px solid #48bb78;
            color: #48bb78;
            background: transparent;
        }

        .btn-outline-success:hover {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-color: transparent;
            color: white;
        }

        .btn-outline-danger {
            border: 2px solid #f56565;
            color: #f56565;
            background: transparent;
        }

        .btn-outline-danger:hover {
            background: #f56565;
            border-color: transparent;
            color: white;
        }

        /* تحسين التنقل بين الصفحات */
        .pagination {
            gap: 0.5rem;
        }

        .page-link {
            border: none;
            border-radius: 50px;
            padding: 0.75rem 1rem;
            font-weight: 600;
            background: white;
            color: #667eea;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .page-link:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(103, 126, 234, 0.3);
        }

        .page-item.active .page-link {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(103, 126, 234, 0.3);
        }

        /* تأثيرات الحركة */
        .animate-on-scroll {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .animate-on-scroll.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* تجاوب الشاشات الصغيرة */
        @media (max-width: 768px) {
            .page-header h1 {
                font-size: 2rem;
            }

            .filters-section {
                margin: -2rem 1rem 2rem;
                padding: 1.5rem;
            }

            .book-card:hover {
                transform: translateY(-8px) scale(1.01);
            }
        }
    </style>
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold text-primary" href="index.html">
                <i class="fas fa-book-open me-2"></i>
                مكتبتي الحرة
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="categories.html">الأقسام</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="books.html">جميع الكتب</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html">حول الموقع</a>
                    </li>
                </ul>
                
                <div class="d-flex align-items-center">
                    <!-- Dark Mode Toggle -->
                    <button class="btn btn-outline-secondary me-2" id="darkModeToggle">
                        <i class="fas fa-moon"></i>
                    </button>
                    
                    <!-- Login/Register Buttons -->
                    <a href="login.html" class="btn btn-outline-primary me-2">تسجيل الدخول</a>
                    <a href="register.html" class="btn btn-primary">إنشاء حساب</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <div class="hero-badge mb-4">
                        <span class="badge bg-light text-primary px-4 py-2 rounded-pill">
                            <i class="fas fa-books me-2"></i>
                            مكتبة شاملة ومتنوعة
                        </span>
                    </div>
                    <h1 class="display-3 fw-bold mb-3">
                        <i class="fas fa-book-open me-3"></i>
                        مكتبة الكتب
                    </h1>
                    <p class="lead mb-4">
                        اكتشف عالماً من المعرفة والثقافة من خلال مجموعتنا الواسعة من الكتب العربية والعالمية
                        <br>
                        <span class="text-warning fw-bold">أكثر من 1,250 كتاب متاح للقراءة والتحميل مجاناً</span>
                    </p>

                    <!-- إحصائيات سريعة -->
                    <div class="row justify-content-center mt-4">
                        <div class="col-md-3 col-6 mb-3">
                            <div class="stat-card bg-white bg-opacity-20 rounded-pill p-3">
                                <h4 class="mb-1 text-warning">1,250</h4>
                                <small>كتاب متاح</small>
                            </div>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <div class="stat-card bg-white bg-opacity-20 rounded-pill p-3">
                                <h4 class="mb-1 text-warning">8</h4>
                                <small>تصنيف مختلف</small>
                            </div>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <div class="stat-card bg-white bg-opacity-20 rounded-pill p-3">
                                <h4 class="mb-1 text-warning">89,650</h4>
                                <small>تحميل</small>
                            </div>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <div class="stat-card bg-white bg-opacity-20 rounded-pill p-3">
                                <h4 class="mb-1 text-warning">15,420</h4>
                                <small>قارئ نشط</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Search and Filter Section -->
    <section class="filters-section py-5">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="filter-card p-4">
                        <!-- البحث الرئيسي -->
                        <div class="row mb-4">
                            <div class="col-lg-8 mb-3">
                                <div class="search-box position-relative">
                                    <div class="input-group">
                                        <span class="input-group-text bg-transparent border-0">
                                            <i class="fas fa-search text-primary"></i>
                                        </span>
                                        <input type="text" class="form-control form-control-lg border-0"
                                               placeholder="ابحث عن كتاب، مؤلف، أو موضوع..." id="searchInput">
                                        <button class="btn btn-primary btn-lg px-4" type="button" id="searchBtn">
                                            <i class="fas fa-search me-2"></i>
                                            بحث
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 mb-3">
                                <div class="results-info text-center">
                                    <i class="fas fa-books me-2"></i>
                                    <span id="resultsCount">8 كتب متاحة</span>
                                </div>
                            </div>
                        </div>

                        <!-- فلاتر التصنيف -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="fw-bold mb-3 text-primary">
                                    <i class="fas fa-filter me-2"></i>
                                    تصفية حسب التصنيف
                                </h6>
                                <div class="category-filters d-flex flex-wrap gap-2">
                                    <button class="filter-btn active" data-category="all">
                                        <i class="fas fa-th-large me-2"></i>
                                        جميع الكتب
                                    </button>
                                    <button class="filter-btn" data-category="literature">
                                        <i class="fas fa-feather-alt me-2"></i>
                                        الأدب
                                    </button>
                                    <button class="filter-btn" data-category="science">
                                        <i class="fas fa-flask me-2"></i>
                                        العلوم
                                    </button>
                                    <button class="filter-btn" data-category="history">
                                        <i class="fas fa-landmark me-2"></i>
                                        التاريخ
                                    </button>
                                    <button class="filter-btn" data-category="philosophy">
                                        <i class="fas fa-brain me-2"></i>
                                        الفلسفة
                                    </button>
                                    <button class="filter-btn" data-category="religion">
                                        <i class="fas fa-mosque me-2"></i>
                                        الدين
                                    </button>
                                    <button class="filter-btn" data-category="novels">
                                        <i class="fas fa-book me-2"></i>
                                        الروايات
                                    </button>
                                    <button class="filter-btn" data-category="poetry">
                                        <i class="fas fa-pen-fancy me-2"></i>
                                        الشعر
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- خيارات الترتيب والعرض -->
                        <div class="row align-items-center">
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-center gap-3">
                                    <label class="fw-semibold text-secondary">ترتيب حسب:</label>
                                    <select class="form-select" id="sortFilter" style="max-width: 200px;">
                                        <option value="newest">الأحدث إضافة</option>
                                        <option value="popular">الأكثر تحميلاً</option>
                                        <option value="rating">الأعلى تقييماً</option>
                                        <option value="alphabetical">أبجدي (أ-ي)</option>
                                        <option value="author">حسب المؤلف</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-center justify-content-md-end gap-3">
                                    <label class="fw-semibold text-secondary">طريقة العرض:</label>
                                    <div class="view-toggle btn-group" role="group">
                                        <button type="button" class="btn active" id="gridView" title="عرض شبكي">
                                            <i class="fas fa-th me-1"></i>
                                            شبكة
                                        </button>
                                        <button type="button" class="btn" id="listView" title="عرض قائمة">
                                            <i class="fas fa-list me-1"></i>
                                            قائمة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
                </div>
            </div>
        </div>
    </section>

    <!-- Books Section -->
    <section class="books-section py-5">
        <div class="container">
            <!-- Books Grid -->
            <div id="booksContainer" class="row">
                <!-- كتاب 1 -->
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                    <div class="card book-card h-100 shadow-sm">
                        <div class="position-relative">
                            <img src="assets/images/books/book1.jpg" class="card-img-top" alt="مئة عام من العزلة" style="height: 250px; object-fit: cover;">
                            <span class="badge bg-success position-absolute top-0 end-0 m-2">جديد</span>
                        </div>
                        <div class="card-body d-flex flex-column">
                            <h6 class="card-title">مئة عام من العزلة</h6>
                            <p class="card-text text-muted small">غابرييل غارسيا ماركيز</p>
                            <p class="card-text small">رواية ملحمية تحكي قصة عائلة بوينديا عبر مئة عام...</p>
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div class="rating">
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <small class="text-muted ms-1">(4.8)</small>
                                    </div>
                                    <span class="badge bg-primary">أدب</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-download me-1"></i>1,234 تحميل
                                    </small>
                                    <small class="text-muted">
                                        <i class="fas fa-file-pdf me-1"></i>2.5 MB
                                    </small>
                                </div>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-primary btn-sm flex-fill" onclick="viewBook(1)">
                                        <i class="fas fa-eye me-1"></i>عرض
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" onclick="downloadBook(1)">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" onclick="toggleFavorite(1)">
                                        <i class="far fa-heart"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- كتاب 2 -->
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                    <div class="card book-card h-100 shadow-sm">
                        <div class="position-relative">
                            <img src="assets/images/books/book2.jpg" class="card-img-top" alt="الأسود يليق بك" style="height: 250px; object-fit: cover;">
                            <span class="badge bg-warning position-absolute top-0 end-0 m-2">مميز</span>
                        </div>
                        <div class="card-body d-flex flex-column">
                            <h6 class="card-title">الأسود يليق بك</h6>
                            <p class="card-text text-muted small">أحلام مستغانمي</p>
                            <p class="card-text small">رواية عاطفية تتناول قصة حب في زمن الحرب...</p>
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div class="rating">
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="far fa-star text-warning"></i>
                                        <small class="text-muted ms-1">(4.5)</small>
                                    </div>
                                    <span class="badge bg-danger">رومانسي</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-download me-1"></i>890 تحميل
                                    </small>
                                    <small class="text-muted">
                                        <i class="fas fa-file-pdf me-1"></i>1.8 MB
                                    </small>
                                </div>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-primary btn-sm flex-fill" onclick="viewBook(2)">
                                        <i class="fas fa-eye me-1"></i>عرض
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" onclick="downloadBook(2)">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" onclick="toggleFavorite(2)">
                                        <i class="far fa-heart"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- كتاب 3 -->
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                    <div class="card book-card h-100 shadow-sm">
                        <div class="position-relative">
                            <img src="assets/images/books/book3.jpg" class="card-img-top" alt="مدن الملح" style="height: 250px; object-fit: cover;">
                        </div>
                        <div class="card-body d-flex flex-column">
                            <h6 class="card-title">مدن الملح</h6>
                            <p class="card-text text-muted small">عبد الرحمن منيف</p>
                            <p class="card-text small">ملحمة روائية تصور التحولات الاجتماعية في الخليج...</p>
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div class="rating">
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <small class="text-muted ms-1">(4.9)</small>
                                    </div>
                                    <span class="badge bg-info">تاريخي</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-download me-1"></i>2,156 تحميل
                                    </small>
                                    <small class="text-muted">
                                        <i class="fas fa-file-pdf me-1"></i>3.2 MB
                                    </small>
                                </div>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-primary btn-sm flex-fill" onclick="viewBook(3)">
                                        <i class="fas fa-eye me-1"></i>عرض
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" onclick="downloadBook(3)">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" onclick="toggleFavorite(3)">
                                        <i class="far fa-heart"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- كتاب 4 -->
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                    <div class="card book-card h-100 shadow-sm">
                        <div class="position-relative">
                            <img src="assets/images/books/book4.jpg" class="card-img-top" alt="رجال في الشمس" style="height: 250px; object-fit: cover;">
                        </div>
                        <div class="card-body d-flex flex-column">
                            <h6 class="card-title">رجال في الشمس</h6>
                            <p class="card-text text-muted small">غسان كنفاني</p>
                            <p class="card-text small">قصة مؤثرة عن معاناة اللاجئين الفلسطينيين...</p>
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div class="rating">
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="far fa-star text-warning"></i>
                                        <small class="text-muted ms-1">(4.3)</small>
                                    </div>
                                    <span class="badge bg-secondary">سياسي</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-download me-1"></i>756 تحميل
                                    </small>
                                    <small class="text-muted">
                                        <i class="fas fa-file-pdf me-1"></i>1.2 MB
                                    </small>
                                </div>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-primary btn-sm flex-fill" onclick="viewBook(4)">
                                        <i class="fas fa-eye me-1"></i>عرض
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" onclick="downloadBook(4)">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" onclick="toggleFavorite(4)">
                                        <i class="far fa-heart"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- كتاب 5 -->
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                    <div class="card book-card h-100 shadow-sm">
                        <div class="position-relative">
                            <img src="assets/images/books/book5.jpg" class="card-img-top" alt="موسم الهجرة إلى الشمال" style="height: 250px; object-fit: cover;">
                        </div>
                        <div class="card-body d-flex flex-column">
                            <h6 class="card-title">موسم الهجرة إلى الشمال</h6>
                            <p class="card-text text-muted small">الطيب صالح</p>
                            <p class="card-text small">رواية كلاسيكية تتناول صراع الهوية والثقافة...</p>
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div class="rating">
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <small class="text-muted ms-1">(4.7)</small>
                                    </div>
                                    <span class="badge bg-primary">أدب</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-download me-1"></i>1,567 تحميل
                                    </small>
                                    <small class="text-muted">
                                        <i class="fas fa-file-pdf me-1"></i>1.9 MB
                                    </small>
                                </div>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-primary btn-sm flex-fill" onclick="viewBook(5)">
                                        <i class="fas fa-eye me-1"></i>عرض
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" onclick="downloadBook(5)">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" onclick="toggleFavorite(5)">
                                        <i class="far fa-heart"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- كتاب 6 -->
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                    <div class="card book-card h-100 shadow-sm">
                        <div class="position-relative">
                            <img src="assets/images/books/book6.jpg" class="card-img-top" alt="ثلاثية نجيب محفوظ" style="height: 250px; object-fit: cover;">
                        </div>
                        <div class="card-body d-flex flex-column">
                            <h6 class="card-title">ثلاثية نجيب محفوظ</h6>
                            <p class="card-text text-muted small">نجيب محفوظ</p>
                            <p class="card-text small">ملحمة أدبية تصور الحياة المصرية عبر ثلاثة أجيال...</p>
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div class="rating">
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <small class="text-muted ms-1">(4.9)</small>
                                    </div>
                                    <span class="badge bg-primary">أدب</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-download me-1"></i>3,245 تحميل
                                    </small>
                                    <small class="text-muted">
                                        <i class="fas fa-file-pdf me-1"></i>4.1 MB
                                    </small>
                                </div>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-primary btn-sm flex-fill" onclick="viewBook(6)">
                                        <i class="fas fa-eye me-1"></i>عرض
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" onclick="downloadBook(6)">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" onclick="toggleFavorite(6)">
                                        <i class="far fa-heart"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- كتاب 7 -->
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                    <div class="card book-card h-100 shadow-sm">
                        <div class="position-relative">
                            <img src="assets/images/books/book7.jpg" class="card-img-top" alt="عصر الحكمة" style="height: 250px; object-fit: cover;">
                        </div>
                        <div class="card-body d-flex flex-column">
                            <h6 class="card-title">عصر الحكمة</h6>
                            <p class="card-text text-muted small">جيم الخليلي</p>
                            <p class="card-text small">كتاب يستكشف العصر الذهبي للعلوم في الحضارة الإسلامية...</p>
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div class="rating">
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="far fa-star text-warning"></i>
                                        <small class="text-muted ms-1">(4.4)</small>
                                    </div>
                                    <span class="badge bg-success">علوم</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-download me-1"></i>987 تحميل
                                    </small>
                                    <small class="text-muted">
                                        <i class="fas fa-file-pdf me-1"></i>2.8 MB
                                    </small>
                                </div>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-primary btn-sm flex-fill" onclick="viewBook(7)">
                                        <i class="fas fa-eye me-1"></i>عرض
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" onclick="downloadBook(7)">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" onclick="toggleFavorite(7)">
                                        <i class="far fa-heart"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- كتاب 8 -->
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                    <div class="card book-card h-100 shadow-sm">
                        <div class="position-relative">
                            <img src="assets/images/books/book8.jpg" class="card-img-top" alt="تاريخ الطبري" style="height: 250px; object-fit: cover;">
                        </div>
                        <div class="card-body d-flex flex-column">
                            <h6 class="card-title">تاريخ الطبري</h6>
                            <p class="card-text text-muted small">محمد بن جرير الطبري</p>
                            <p class="card-text small">مرجع تاريخي مهم يغطي تاريخ الأمم والملوك...</p>
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div class="rating">
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <small class="text-muted ms-1">(4.8)</small>
                                    </div>
                                    <span class="badge bg-info">تاريخ</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-download me-1"></i>1,876 تحميل
                                    </small>
                                    <small class="text-muted">
                                        <i class="fas fa-file-pdf me-1"></i>5.2 MB
                                    </small>
                                </div>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-primary btn-sm flex-fill" onclick="viewBook(8)">
                                        <i class="fas fa-eye me-1"></i>عرض
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" onclick="downloadBook(8)">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" onclick="toggleFavorite(8)">
                                        <i class="far fa-heart"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- No Results -->
            <div id="noResults" class="text-center py-5 d-none">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h4>لا توجد نتائج</h4>
                <p class="text-muted">جرب تغيير معايير البحث أو المرشحات</p>
            </div>

            <!-- Pagination -->
            <nav aria-label="تنقل الصفحات" class="mt-5">
                <ul class="pagination justify-content-center" id="pagination">
                    <!-- Pagination will be generated here -->
                </ul>
            </nav>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-book-open me-2"></i>
                        مكتبتي الحرة
                    </h5>
                    <p class="text-light">مكتبة رقمية مجانية تهدف إلى نشر المعرفة وإتاحة الكتب للجميع بدون قيود.</p>
                </div>
                <div class="col-lg-2 mb-4">
                    <h6 class="fw-bold mb-3">روابط سريعة</h6>
                    <ul class="list-unstyled">
                        <li><a href="index.html" class="text-light text-decoration-none">الرئيسية</a></li>
                        <li><a href="books.html" class="text-light text-decoration-none">الكتب</a></li>
                        <li><a href="categories.html" class="text-light text-decoration-none">الأقسام</a></li>
                        <li><a href="about.html" class="text-light text-decoration-none">حول الموقع</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 mb-4">
                    <h6 class="fw-bold mb-3">الحساب</h6>
                    <ul class="list-unstyled">
                        <li><a href="login.html" class="text-light text-decoration-none">تسجيل الدخول</a></li>
                        <li><a href="register.html" class="text-light text-decoration-none">إنشاء حساب</a></li>
                        <li><a href="profile.html" class="text-light text-decoration-none">الملف الشخصي</a></li>
                        <li><a href="favorites.html" class="text-light text-decoration-none">المفضلة</a></li>
                    </ul>
                </div>
                <div class="col-lg-4 mb-4">
                    <h6 class="fw-bold mb-3">تواصل معنا</h6>
                    <div class="d-flex gap-3">
                        <a href="#" class="text-light"><i class="fab fa-facebook fa-2x"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-twitter fa-2x"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-instagram fa-2x"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-youtube fa-2x"></i></a>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p class="mb-0">&copy; 2024 مكتبتي الحرة. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/config.js"></script>
    <!-- Sample Data -->
    <script src="assets/js/sample-data.js"></script>
    <!-- Stability JS (يجب تحميله أولاً) -->
    <script src="assets/js/stability.js"></script>
    <script src="assets/js/main.js"></script>

    <!-- JavaScript محسن لصفحة الكتب -->
    <script>
        // متغيرات عامة
        let currentBooks = [];
        let filteredBooks = [];
        let currentCategory = 'all';
        let currentSort = 'newest';
        let currentView = 'grid';

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
            loadBooks();
            initializeFilters();
            initializeAnimations();
            initializeSearch();

            console.log('تم تحميل صفحة الكتب بنجاح');
        });

        // تهيئة الصفحة
        function initializePage() {
            // تحديث العدادات في الرأس
            updateHeaderStats();

            // تفعيل الحركات عند التمرير
            initScrollAnimations();

            // إضافة تأثيرات الجسيمات
            addParticleEffects();
        }

        // تحميل الكتب
        function loadBooks() {
            if (typeof SampleData !== 'undefined' && SampleData.books) {
                currentBooks = SampleData.books;
                filteredBooks = [...currentBooks];
                displayBooks();
                updateResultsCount();
            } else {
                console.warn('بيانات الكتب غير متوفرة');
                showNoResults();
            }
        }

        // عرض الكتب
        function displayBooks() {
            const container = document.getElementById('booksContainer');
            if (!container) return;

            if (filteredBooks.length === 0) {
                showNoResults();
                return;
            }

            const booksHTML = filteredBooks.map((book, index) => `
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4 animate-on-scroll" style="animation-delay: ${index * 100}ms">
                    <div class="card book-card h-100 shadow-sm glow-effect zoom-effect floating-shadow">
                        <div class="position-relative wave-effect">
                            <img src="${book.cover}" class="card-img-top" alt="${book.title}" style="height: 250px; object-fit: cover;">
                            ${book.new ? '<span class="badge bg-success position-absolute top-0 end-0 m-2 pulse-effect">جديد</span>' : ''}
                            ${book.featured ? '<span class="badge bg-warning position-absolute top-0 start-0 m-2">مميز</span>' : ''}
                        </div>
                        <div class="card-body d-flex flex-column">
                            <h6 class="card-title gradient-text" data-text="${book.title}">${book.title}</h6>
                            <p class="card-text text-muted small">${book.author}</p>
                            <p class="card-text small">${book.description.substring(0, 100)}...</p>
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div class="rating">
                                        ${generateStars(book.rating)}
                                        <small class="text-muted ms-1">(${book.rating})</small>
                                    </div>
                                    <span class="badge category-badge">${book.category}</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-download me-1"></i>${formatNumber(book.downloads)} تحميل
                                    </small>
                                    <small class="text-muted">
                                        <i class="fas fa-file-pdf me-1"></i>${book.size}
                                    </small>
                                </div>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-primary btn-sm flex-fill ripple-effect" onclick="viewBook(${book.id})">
                                        <i class="fas fa-eye me-1"></i>عرض
                                    </button>
                                    <button class="btn btn-outline-success btn-sm bounce-effect" onclick="downloadBook(${book.id})">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm shake-effect" onclick="toggleFavorite(${book.id})">
                                        <i class="far fa-heart"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');

            container.innerHTML = booksHTML;

            // تفعيل الحركات للعناصر الجديدة
            setTimeout(() => {
                initScrollAnimations();
                addCardEffects();
            }, 100);
        }

        // إنشاء النجوم للتقييم
        function generateStars(rating) {
            const fullStars = Math.floor(rating);
            const hasHalfStar = rating % 1 !== 0;
            let starsHTML = '';

            for (let i = 0; i < fullStars; i++) {
                starsHTML += '<i class="fas fa-star text-warning"></i>';
            }

            if (hasHalfStar) {
                starsHTML += '<i class="fas fa-star-half-alt text-warning"></i>';
            }

            const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
            for (let i = 0; i < emptyStars; i++) {
                starsHTML += '<i class="far fa-star text-warning"></i>';
            }

            return starsHTML;
        }

        // تنسيق الأرقام
        function formatNumber(num) {
            if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'k';
            }
            return num.toString();
        }

        // تهيئة الفلاتر
        function initializeFilters() {
            // فلاتر التصنيف
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    // إزالة الفئة النشطة من جميع الأزرار
                    document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));

                    // إضافة الفئة النشطة للزر المحدد
                    this.classList.add('active');

                    // تطبيق الفلتر
                    currentCategory = this.dataset.category;
                    applyFilters();

                    // تأثير بصري
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });

            // فلتر الترتيب
            const sortFilter = document.getElementById('sortFilter');
            if (sortFilter) {
                sortFilter.addEventListener('change', function() {
                    currentSort = this.value;
                    applyFilters();

                    // تأثير بصري
                    this.style.transform = 'scale(1.02)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 200);
                });
            }

            // أزرار العرض
            const gridView = document.getElementById('gridView');
            const listView = document.getElementById('listView');

            if (gridView) {
                gridView.addEventListener('click', function() {
                    switchView('grid');
                });
            }

            if (listView) {
                listView.addEventListener('click', function() {
                    switchView('list');
                });
            }
        }

        // تطبيق الفلاتر
        function applyFilters() {
            // فلترة حسب التصنيف
            if (currentCategory === 'all') {
                filteredBooks = [...currentBooks];
            } else {
                filteredBooks = currentBooks.filter(book =>
                    book.category.toLowerCase().includes(currentCategory.toLowerCase()) ||
                    book.slug === currentCategory
                );
            }

            // ترتيب النتائج
            sortBooks();

            // عرض النتائج مع تأثير
            animateResults();
            updateResultsCount();
        }

        // ترتيب الكتب
        function sortBooks() {
            switch (currentSort) {
                case 'newest':
                    filteredBooks.sort((a, b) => b.id - a.id);
                    break;
                case 'popular':
                    filteredBooks.sort((a, b) => b.downloads - a.downloads);
                    break;
                case 'rating':
                    filteredBooks.sort((a, b) => b.rating - a.rating);
                    break;
                case 'alphabetical':
                    filteredBooks.sort((a, b) => a.title.localeCompare(b.title, 'ar'));
                    break;
                case 'author':
                    filteredBooks.sort((a, b) => a.author.localeCompare(b.author, 'ar'));
                    break;
            }
        }

        // تحريك النتائج
        function animateResults() {
            const container = document.getElementById('booksContainer');

            // تأثير الاختفاء
            container.style.opacity = '0';
            container.style.transform = 'translateY(20px)';

            setTimeout(() => {
                displayBooks();

                // تأثير الظهور
                container.style.transition = 'all 0.5s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 200);
        }

        // تبديل طريقة العرض
        function switchView(viewType) {
            currentView = viewType;

            const gridBtn = document.getElementById('gridView');
            const listBtn = document.getElementById('listView');
            const container = document.getElementById('booksContainer');

            if (viewType === 'grid') {
                if (container) container.className = 'row';
                if (gridBtn) gridBtn.classList.add('active');
                if (listBtn) listBtn.classList.remove('active');
            } else {
                if (container) container.className = 'row list-view';
                if (listBtn) listBtn.classList.add('active');
                if (gridBtn) gridBtn.classList.remove('active');
            }

            // حفظ التفضيل
            localStorage.setItem('viewType', viewType);

            // إعادة عرض الكتب
            displayBooks();
        }

        // تحديث عدد النتائج
        function updateResultsCount() {
            const countElement = document.getElementById('resultsCount');
            if (countElement) {
                const count = filteredBooks.length;
                countElement.innerHTML = `<i class="fas fa-books me-2"></i>${count} كتاب متاح`;

                // تأثير النبض
                countElement.style.animation = 'pulse 0.5s ease';
                setTimeout(() => {
                    countElement.style.animation = '';
                }, 500);
            }
        }

        // تهيئة البحث
        function initializeSearch() {
            const searchInput = document.getElementById('searchInput');
            const searchBtn = document.getElementById('searchBtn');

            if (searchInput) {
                let searchTimeout;

                searchInput.addEventListener('input', function() {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => {
                        performSearch(this.value);
                    }, 300);

                    // تأثير بصري
                    this.style.borderColor = '#667eea';
                    this.style.boxShadow = '0 0 10px rgba(103, 126, 234, 0.3)';
                });

                searchInput.addEventListener('blur', function() {
                    this.style.borderColor = '';
                    this.style.boxShadow = '';
                });
            }

            if (searchBtn) {
                searchBtn.addEventListener('click', function() {
                    performSearch(searchInput.value);

                    // تأثير الضغط
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            }
        }

        // تنفيذ البحث
        function performSearch(query) {
            if (!query || query.length < 2) {
                filteredBooks = [...currentBooks];
            } else {
                filteredBooks = currentBooks.filter(book =>
                    book.title.toLowerCase().includes(query.toLowerCase()) ||
                    book.author.toLowerCase().includes(query.toLowerCase()) ||
                    book.description.toLowerCase().includes(query.toLowerCase()) ||
                    book.category.toLowerCase().includes(query.toLowerCase())
                );
            }

            // تطبيق الترتيب
            sortBooks();

            // عرض النتائج
            animateResults();
            updateResultsCount();

            // إظهار رسالة إذا لم توجد نتائج
            if (filteredBooks.length === 0) {
                showNoResults();
            }
        }

        // إظهار رسالة عدم وجود نتائج
        function showNoResults() {
            const container = document.getElementById('booksContainer');
            container.innerHTML = `
                <div class="col-12 text-center py-5">
                    <div class="no-results">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد نتائج</h4>
                        <p class="text-muted">جرب البحث بكلمات مختلفة أو تغيير الفلاتر</p>
                        <button class="btn btn-primary" onclick="resetFilters()">
                            <i class="fas fa-refresh me-2"></i>
                            إعادة تعيين الفلاتر
                        </button>
                    </div>
                </div>
            `;
        }

        // إعادة تعيين الفلاتر
        function resetFilters() {
            currentCategory = 'all';
            currentSort = 'newest';

            // إعادة تعيين واجهة المستخدم
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.dataset.category === 'all') {
                    btn.classList.add('active');
                }
            });

            const sortFilter = document.getElementById('sortFilter');
            const searchInput = document.getElementById('searchInput');

            if (sortFilter) sortFilter.value = 'newest';
            if (searchInput) searchInput.value = '';

            // إعادة تحميل الكتب
            loadBooks();
        }

        // تهيئة الحركات
        function initializeAnimations() {
            // تأثيرات التمرير
            initScrollAnimations();

            // تأثيرات البطاقات
            addCardEffects();

            // تأثيرات الجسيمات
            addParticleEffects();
        }

        // تأثيرات التمرير
        function initScrollAnimations() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // مراقبة العناصر
            document.querySelectorAll('.animate-on-scroll').forEach(element => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(30px)';
                element.style.transition = 'all 0.6s ease';
                observer.observe(element);
            });
        }

        // إضافة تأثيرات البطاقات
        function addCardEffects() {
            document.querySelectorAll('.book-card').forEach(card => {
                // تأثير الوهج عند التمرير
                card.addEventListener('mouseenter', function() {
                    this.style.boxShadow = '0 25px 80px rgba(103, 126, 234, 0.3)';
                    this.style.transform = 'translateY(-15px) scale(1.03)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.boxShadow = '';
                    this.style.transform = '';
                });
            });
        }

        // إضافة تأثيرات الجسيمات
        function addParticleEffects() {
            const header = document.querySelector('.page-header');
            if (!header) return;

            // إنشاء جسيمات متحركة
            for (let i = 0; i < 15; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.cssText = `
                    position: absolute;
                    width: ${Math.random() * 4 + 2}px;
                    height: ${Math.random() * 4 + 2}px;
                    background: rgba(255, 255, 255, ${Math.random() * 0.5 + 0.2});
                    border-radius: 50%;
                    pointer-events: none;
                    animation: particleFloat ${Math.random() * 10 + 10}s linear infinite;
                    left: ${Math.random() * 100}%;
                    top: ${Math.random() * 100}%;
                `;
                header.appendChild(particle);
            }
        }

        // وظائف الكتب
        function viewBook(bookId) {
            // تأثير بصري
            if (event && event.target) {
                event.target.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    event.target.style.transform = '';
                    window.location.href = `book-details.html?id=${bookId}`;
                }, 150);
            } else {
                window.location.href = `book-details.html?id=${bookId}`;
            }
        }

        function downloadBook(bookId) {
            if (!isLoggedIn()) {
                showToast('يجب تسجيل الدخول أولاً للتحميل', 'warning');
                return;
            }

            // تأثير بصري
            if (event && event.target) {
                const btn = event.target.closest('button');
                btn.style.transform = 'scale(0.9)';
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

                setTimeout(() => {
                    btn.style.transform = '';
                    btn.innerHTML = '<i class="fas fa-download"></i>';
                    showToast('تم بدء التحميل بنجاح', 'success');
                }, 1000);
            }
        }

        function toggleFavorite(bookId) {
            if (!isLoggedIn()) {
                showToast('يجب تسجيل الدخول أولاً', 'warning');
                return;
            }

            if (event && event.target) {
                const btn = event.target.closest('button');
                const icon = btn.querySelector('i');

                if (icon.classList.contains('far')) {
                    icon.classList.remove('far');
                    icon.classList.add('fas');
                    btn.classList.remove('btn-outline-danger');
                    btn.classList.add('btn-danger');
                    showToast('تم إضافة الكتاب للمفضلة', 'success');
                } else {
                    icon.classList.remove('fas');
                    icon.classList.add('far');
                    btn.classList.remove('btn-danger');
                    btn.classList.add('btn-outline-danger');
                    showToast('تم إزالة الكتاب من المفضلة', 'info');
                }

                // تأثير النبض
                btn.style.animation = 'pulse 0.5s ease';
                setTimeout(() => {
                    btn.style.animation = '';
                }, 500);
            }
        }

        // وظائف مساعدة
        function updateHeaderStats() {
            // تحديث الإحصائيات في الرأس
            console.log('تحديث إحصائيات الرأس');
        }

        function isLoggedIn() {
            return localStorage.getItem('isLoggedIn') === 'true';
        }

        function showToast(message, type = 'info') {
            // إنشاء التنبيه
            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type} border-0`;
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;

            // إضافة للصفحة
            let container = document.getElementById('toastContainer');
            if (!container) {
                container = document.createElement('div');
                container.id = 'toastContainer';
                container.className = 'toast-container position-fixed top-0 end-0 p-3';
                container.style.zIndex = '9999';
                document.body.appendChild(container);
            }

            container.appendChild(toast);

            // إظهار التنبيه
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();

            // إزالة بعد الإخفاء
            toast.addEventListener('hidden.bs.toast', function() {
                this.remove();
            });
        }
    </script>
</body>
</html>
