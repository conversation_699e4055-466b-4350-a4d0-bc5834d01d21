<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء صور الكتب - مكتبتي الحرة</title>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
            padding: 20px;
        }
        
        .book-cover {
            width: 200px;
            height: 300px;
            margin: 10px;
            display: inline-block;
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .book-cover canvas {
            width: 100%;
            height: 100%;
        }
        
        .download-btn {
            margin-top: 10px;
            padding: 5px 10px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>إنشاء صور الكتب التجريبية</h1>
    <p>انقر على "إنشاء الصور" لتوليد صور افتراضية للكتب</p>
    
    <button onclick="generateBookCovers()" style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 5px; font-size: 16px;">
        إنشاء الصور
    </button>
    
    <div id="bookCovers"></div>

    <script>
        const books = [
            { title: "مئة عام من العزلة", author: "غابرييل غارسيا ماركيز", color: "#8B4513" },
            { title: "الأسود يليق بك", author: "أحلام مستغانمي", color: "#2F4F4F" },
            { title: "مدن الملح", author: "عبد الرحمن منيف", color: "#DAA520" },
            { title: "رجال في الشمس", author: "غسان كنفاني", color: "#CD853F" },
            { title: "موسم الهجرة إلى الشمال", author: "الطيب صالح", color: "#4682B4" },
            { title: "ثلاثية نجيب محفوظ", author: "نجيب محفوظ", color: "#8B0000" },
            { title: "عصر الحكمة", author: "جيم الخليلي", color: "#2E8B57" },
            { title: "تاريخ الطبري", author: "محمد بن جرير الطبري", color: "#B8860B" }
        ];

        function generateBookCovers() {
            const container = document.getElementById('bookCovers');
            container.innerHTML = '';

            books.forEach((book, index) => {
                const bookDiv = document.createElement('div');
                bookDiv.className = 'book-cover';
                
                const canvas = document.createElement('canvas');
                canvas.width = 200;
                canvas.height = 300;
                
                const ctx = canvas.getContext('2d');
                
                // خلفية الكتاب
                const gradient = ctx.createLinearGradient(0, 0, 0, 300);
                gradient.addColorStop(0, book.color);
                gradient.addColorStop(1, darkenColor(book.color, 0.3));
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, 200, 300);
                
                // إطار
                ctx.strokeStyle = darkenColor(book.color, 0.5);
                ctx.lineWidth = 3;
                ctx.strokeRect(10, 10, 180, 280);
                
                // عنوان الكتاب
                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'center';
                
                // تقسيم العنوان إلى أسطر
                const titleLines = wrapText(ctx, book.title, 170);
                titleLines.forEach((line, i) => {
                    ctx.fillText(line, 100, 80 + (i * 25));
                });
                
                // اسم المؤلف
                ctx.font = '14px Arial';
                ctx.fillStyle = '#f0f0f0';
                const authorLines = wrapText(ctx, book.author, 170);
                authorLines.forEach((line, i) => {
                    ctx.fillText(line, 100, 200 + (i * 20));
                });
                
                // زخرفة
                ctx.strokeStyle = 'rgba(255,255,255,0.3)';
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.moveTo(30, 150);
                ctx.lineTo(170, 150);
                ctx.stroke();
                
                bookDiv.appendChild(canvas);
                
                // زر التحميل
                const downloadBtn = document.createElement('button');
                downloadBtn.className = 'download-btn';
                downloadBtn.textContent = `تحميل book${index + 1}.jpg`;
                downloadBtn.onclick = () => downloadImage(canvas, `book${index + 1}.jpg`);
                
                const wrapper = document.createElement('div');
                wrapper.appendChild(bookDiv);
                wrapper.appendChild(downloadBtn);
                
                container.appendChild(wrapper);
            });
        }

        function wrapText(ctx, text, maxWidth) {
            const words = text.split(' ');
            const lines = [];
            let currentLine = words[0];

            for (let i = 1; i < words.length; i++) {
                const word = words[i];
                const width = ctx.measureText(currentLine + " " + word).width;
                if (width < maxWidth) {
                    currentLine += " " + word;
                } else {
                    lines.push(currentLine);
                    currentLine = word;
                }
            }
            lines.push(currentLine);
            return lines;
        }

        function darkenColor(color, factor) {
            // تحويل اللون إلى RGB وتعتيمه
            const hex = color.replace('#', '');
            const r = parseInt(hex.substr(0, 2), 16);
            const g = parseInt(hex.substr(2, 2), 16);
            const b = parseInt(hex.substr(4, 2), 16);
            
            const newR = Math.floor(r * (1 - factor));
            const newG = Math.floor(g * (1 - factor));
            const newB = Math.floor(b * (1 - factor));
            
            return `rgb(${newR}, ${newG}, ${newB})`;
        }

        function downloadImage(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/jpeg', 0.9);
            link.click();
        }
    </script>
</body>
</html>
