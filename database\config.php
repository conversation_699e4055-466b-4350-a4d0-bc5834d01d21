<?php
/**
 * إعدادات قاعدة البيانات - مكتبتي الحرة
 * Database Configuration - My Free Library
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'my_free_library');
define('DB_USER', 'root'); // غير هذا في الإنتاج
define('DB_PASS', ''); // غير هذا في الإنتاج
define('DB_CHARSET', 'utf8mb4');

// إعدادات الأمان
define('JWT_SECRET', 'your-secret-key-here-change-in-production');
define('ENCRYPTION_KEY', 'your-encryption-key-here-32-chars');
define('PASSWORD_SALT', 'your-password-salt-here');

// إعدادات الجلسات
define('SESSION_LIFETIME', 86400); // 24 ساعة
define('REMEMBER_ME_LIFETIME', 2592000); // 30 يوم

// إعدادات الملفات
define('UPLOAD_PATH', '../public/uploads/');
define('COVERS_PATH', '../public/covers/');
define('MAX_FILE_SIZE', 52428800); // 50MB
define('ALLOWED_FILE_TYPES', ['pdf']);
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'webp']);

// إعدادات الموقع
define('SITE_URL', 'http://localhost:8000');
define('API_URL', 'http://localhost:8000/api');
define('ADMIN_EMAIL', '<EMAIL>');

// إعدادات البريد الإلكتروني (للاستخدام المستقبلي)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('SMTP_ENCRYPTION', 'tls');

// إعدادات التطوير
define('DEBUG_MODE', true); // غير إلى false في الإنتاج
define('LOG_ERRORS', true);
define('ERROR_LOG_PATH', '../logs/error.log');

/**
 * فئة قاعدة البيانات
 * Database Class
 */
class Database {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
            ];
            
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            if (DEBUG_MODE) {
                die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
            } else {
                die("خطأ في الاتصال بقاعدة البيانات");
            }
        }
    }
    
    /**
     * الحصول على مثيل قاعدة البيانات
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * الحصول على الاتصال
     */
    public function getConnection() {
        return $this->connection;
    }
    
    /**
     * تنفيذ استعلام
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            $this->logError("Database Query Error: " . $e->getMessage() . " | SQL: " . $sql);
            throw $e;
        }
    }
    
    /**
     * الحصول على صف واحد
     */
    public function fetch($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    /**
     * الحصول على جميع الصفوف
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    /**
     * إدراج بيانات والحصول على المعرف
     */
    public function insert($sql, $params = []) {
        $this->query($sql, $params);
        return $this->connection->lastInsertId();
    }
    
    /**
     * تحديث بيانات
     */
    public function update($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    /**
     * حذف بيانات
     */
    public function delete($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    /**
     * بدء معاملة
     */
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }
    
    /**
     * تأكيد المعاملة
     */
    public function commit() {
        return $this->connection->commit();
    }
    
    /**
     * إلغاء المعاملة
     */
    public function rollback() {
        return $this->connection->rollback();
    }
    
    /**
     * تسجيل الأخطاء
     */
    private function logError($message) {
        if (LOG_ERRORS) {
            $timestamp = date('Y-m-d H:i:s');
            $logMessage = "[$timestamp] $message" . PHP_EOL;
            file_put_contents(ERROR_LOG_PATH, $logMessage, FILE_APPEND | LOCK_EX);
        }
    }
    
    /**
     * منع النسخ
     */
    private function __clone() {}
    
    /**
     * منع إلغاء التسلسل
     */
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

/**
 * وظائف مساعدة
 * Helper Functions
 */

/**
 * الحصول على قاعدة البيانات
 */
function getDB() {
    return Database::getInstance();
}

/**
 * تنظيف البيانات
 */
function sanitize($data) {
    if (is_array($data)) {
        return array_map('sanitize', $data);
    }
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * تشفير كلمة المرور
 */
function hashPassword($password) {
    return password_hash($password . PASSWORD_SALT, PASSWORD_DEFAULT);
}

/**
 * التحقق من كلمة المرور
 */
function verifyPassword($password, $hash) {
    return password_verify($password . PASSWORD_SALT, $hash);
}

/**
 * إنشاء رمز عشوائي
 */
function generateToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * الحصول على عنوان IP
 */
function getClientIP() {
    $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            $ip = trim($ips[0]);
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}

/**
 * الحصول على User Agent
 */
function getUserAgent() {
    return $_SERVER['HTTP_USER_AGENT'] ?? '';
}

/**
 * تحويل حجم الملف إلى نص قابل للقراءة
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= pow(1024, $pow);
    
    return round($bytes, 2) . ' ' . $units[$pow];
}

/**
 * التحقق من نوع الملف
 */
function isAllowedFileType($filename, $allowedTypes) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    return in_array($extension, $allowedTypes);
}

/**
 * إنشاء اسم ملف فريد
 */
function generateUniqueFilename($originalName) {
    $extension = pathinfo($originalName, PATHINFO_EXTENSION);
    $filename = pathinfo($originalName, PATHINFO_FILENAME);
    $filename = preg_replace('/[^a-zA-Z0-9\-_]/', '', $filename);
    return $filename . '_' . time() . '_' . uniqid() . '.' . $extension;
}

// تعيين المنطقة الزمنية
date_default_timezone_set('Asia/Riyadh');

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?>
