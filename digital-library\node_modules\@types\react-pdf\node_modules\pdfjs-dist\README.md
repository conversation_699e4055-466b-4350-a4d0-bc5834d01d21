# PDF.js

PDF.js is a Portable Document Format (PDF) library that is built with HTML5.
Our goal is to create a general-purpose, web standards-based platform for
parsing and rendering PDFs.

This is a pre-built version of the PDF.js source code. It is automatically
generated by the build scripts.

For usage with older browsers or environments, without support for modern
features such as optional chaining, nullish coalescing,
and private `class` fields/methods; please see the `legacy/` folder.

See https://github.com/mozilla/pdf.js for learning and contributing.
