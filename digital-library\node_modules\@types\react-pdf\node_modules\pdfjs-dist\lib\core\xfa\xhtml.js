/**
 * @licstart The following is the entire license notice for the
 * JavaScript code in this page
 *
 * Copyright 2022 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * @licend The above is the entire license notice for the
 * JavaScript code in this page
 */
"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.XhtmlNamespace = void 0;

var _xfa_object = require("./xfa_object.js");

var _namespaces = require("./namespaces.js");

var _html_utils = require("./html_utils.js");

var _utils = require("./utils.js");

const XHTML_NS_ID = _namespaces.NamespaceIds.xhtml.id;
const $richText = Symbol();
const VALID_STYLES = new Set(["color", "font", "font-family", "font-size", "font-stretch", "font-style", "font-weight", "margin", "margin-bottom", "margin-left", "margin-right", "margin-top", "letter-spacing", "line-height", "orphans", "page-break-after", "page-break-before", "page-break-inside", "tab-interval", "tab-stop", "text-align", "text-decoration", "text-indent", "vertical-align", "widows", "kerning-mode", "xfa-font-horizontal-scale", "xfa-font-vertical-scale", "xfa-spacerun", "xfa-tab-stops"]);
const StyleMapping = new Map([["page-break-after", "breakAfter"], ["page-break-before", "breakBefore"], ["page-break-inside", "breakInside"], ["kerning-mode", value => value === "none" ? "none" : "normal"], ["xfa-font-horizontal-scale", value => `scaleX(${Math.max(0, Math.min(parseInt(value) / 100)).toFixed(2)})`], ["xfa-font-vertical-scale", value => `scaleY(${Math.max(0, Math.min(parseInt(value) / 100)).toFixed(2)})`], ["xfa-spacerun", ""], ["xfa-tab-stops", ""], ["font-size", (value, original) => {
  value = original.fontSize = (0, _utils.getMeasurement)(value);
  return (0, _html_utils.measureToString)(0.99 * value);
}], ["letter-spacing", value => (0, _html_utils.measureToString)((0, _utils.getMeasurement)(value))], ["line-height", value => (0, _html_utils.measureToString)((0, _utils.getMeasurement)(value))], ["margin", value => (0, _html_utils.measureToString)((0, _utils.getMeasurement)(value))], ["margin-bottom", value => (0, _html_utils.measureToString)((0, _utils.getMeasurement)(value))], ["margin-left", value => (0, _html_utils.measureToString)((0, _utils.getMeasurement)(value))], ["margin-right", value => (0, _html_utils.measureToString)((0, _utils.getMeasurement)(value))], ["margin-top", value => (0, _html_utils.measureToString)((0, _utils.getMeasurement)(value))], ["text-indent", value => (0, _html_utils.measureToString)((0, _utils.getMeasurement)(value))], ["font-family", value => value], ["vertical-align", value => (0, _html_utils.measureToString)((0, _utils.getMeasurement)(value))]]);
const spacesRegExp = /\s+/g;
const crlfRegExp = /[\r\n]+/g;
const crlfForRichTextRegExp = /\r\n?/g;

function mapStyle(styleStr, node, richText) {
  const style = Object.create(null);

  if (!styleStr) {
    return style;
  }

  const original = Object.create(null);

  for (const [key, value] of styleStr.split(";").map(s => s.split(":", 2))) {
    const mapping = StyleMapping.get(key);

    if (mapping === "") {
      continue;
    }

    let newValue = value;

    if (mapping) {
      if (typeof mapping === "string") {
        newValue = mapping;
      } else {
        newValue = mapping(value, original);
      }
    }

    if (key.endsWith("scale")) {
      if (style.transform) {
        style.transform = `${style[key]} ${newValue}`;
      } else {
        style.transform = newValue;
      }
    } else {
      style[key.replaceAll(/-([a-zA-Z])/g, (_, x) => x.toUpperCase())] = newValue;
    }
  }

  if (style.fontFamily) {
    (0, _html_utils.setFontFamily)({
      typeface: style.fontFamily,
      weight: style.fontWeight || "normal",
      posture: style.fontStyle || "normal",
      size: original.fontSize || 0
    }, node, node[_xfa_object.$globalData].fontFinder, style);
  }

  if (richText && style.verticalAlign && style.verticalAlign !== "0px" && style.fontSize) {
    const SUB_SUPER_SCRIPT_FACTOR = 0.583;
    const VERTICAL_FACTOR = 0.333;
    const fontSize = (0, _utils.getMeasurement)(style.fontSize);
    style.fontSize = (0, _html_utils.measureToString)(fontSize * SUB_SUPER_SCRIPT_FACTOR);
    style.verticalAlign = (0, _html_utils.measureToString)(Math.sign((0, _utils.getMeasurement)(style.verticalAlign)) * fontSize * VERTICAL_FACTOR);
  }

  if (richText && style.fontSize) {
    style.fontSize = `calc(${style.fontSize} * var(--scale-factor))`;
  }

  (0, _html_utils.fixTextIndent)(style);
  return style;
}

function checkStyle(node) {
  if (!node.style) {
    return "";
  }

  return node.style.trim().split(/\s*;\s*/).filter(s => !!s).map(s => s.split(/\s*:\s*/, 2)).filter(([key, value]) => {
    if (key === "font-family") {
      node[_xfa_object.$globalData].usedTypefaces.add(value);
    }

    return VALID_STYLES.has(key);
  }).map(kv => kv.join(":")).join(";");
}

const NoWhites = new Set(["body", "html"]);

class XhtmlObject extends _xfa_object.XmlObject {
  constructor(attributes, name) {
    super(XHTML_NS_ID, name);
    this[$richText] = false;
    this.style = attributes.style || "";
  }

  [_xfa_object.$clean](builder) {
    super[_xfa_object.$clean](builder);

    this.style = checkStyle(this);
  }

  [_xfa_object.$acceptWhitespace]() {
    return !NoWhites.has(this[_xfa_object.$nodeName]);
  }

  [_xfa_object.$onText](str, richText = false) {
    if (!richText) {
      str = str.replace(crlfRegExp, "");

      if (!this.style.includes("xfa-spacerun:yes")) {
        str = str.replace(spacesRegExp, " ");
      }
    } else {
      this[$richText] = true;
    }

    if (str) {
      this[_xfa_object.$content] += str;
    }
  }

  [_xfa_object.$pushGlyphs](measure, mustPop = true) {
    const xfaFont = Object.create(null);
    const margin = {
      top: NaN,
      bottom: NaN,
      left: NaN,
      right: NaN
    };
    let lineHeight = null;

    for (const [key, value] of this.style.split(";").map(s => s.split(":", 2))) {
      switch (key) {
        case "font-family":
          xfaFont.typeface = (0, _utils.stripQuotes)(value);
          break;

        case "font-size":
          xfaFont.size = (0, _utils.getMeasurement)(value);
          break;

        case "font-weight":
          xfaFont.weight = value;
          break;

        case "font-style":
          xfaFont.posture = value;
          break;

        case "letter-spacing":
          xfaFont.letterSpacing = (0, _utils.getMeasurement)(value);
          break;

        case "margin":
          const values = value.split(/ \t/).map(x => (0, _utils.getMeasurement)(x));

          switch (values.length) {
            case 1:
              margin.top = margin.bottom = margin.left = margin.right = values[0];
              break;

            case 2:
              margin.top = margin.bottom = values[0];
              margin.left = margin.right = values[1];
              break;

            case 3:
              margin.top = values[0];
              margin.bottom = values[2];
              margin.left = margin.right = values[1];
              break;

            case 4:
              margin.top = values[0];
              margin.left = values[1];
              margin.bottom = values[2];
              margin.right = values[3];
              break;
          }

          break;

        case "margin-top":
          margin.top = (0, _utils.getMeasurement)(value);
          break;

        case "margin-bottom":
          margin.bottom = (0, _utils.getMeasurement)(value);
          break;

        case "margin-left":
          margin.left = (0, _utils.getMeasurement)(value);
          break;

        case "margin-right":
          margin.right = (0, _utils.getMeasurement)(value);
          break;

        case "line-height":
          lineHeight = (0, _utils.getMeasurement)(value);
          break;
      }
    }

    measure.pushData(xfaFont, margin, lineHeight);

    if (this[_xfa_object.$content]) {
      measure.addString(this[_xfa_object.$content]);
    } else {
      for (const child of this[_xfa_object.$getChildren]()) {
        if (child[_xfa_object.$nodeName] === "#text") {
          measure.addString(child[_xfa_object.$content]);
          continue;
        }

        child[_xfa_object.$pushGlyphs](measure);
      }
    }

    if (mustPop) {
      measure.popFont();
    }
  }

  [_xfa_object.$toHTML](availableSpace) {
    const children = [];
    this[_xfa_object.$extra] = {
      children
    };

    this[_xfa_object.$childrenToHTML]({});

    if (children.length === 0 && !this[_xfa_object.$content]) {
      return _utils.HTMLResult.EMPTY;
    }

    let value;

    if (this[$richText]) {
      value = this[_xfa_object.$content] ? this[_xfa_object.$content].replace(crlfForRichTextRegExp, "\n") : undefined;
    } else {
      value = this[_xfa_object.$content] || undefined;
    }

    return _utils.HTMLResult.success({
      name: this[_xfa_object.$nodeName],
      attributes: {
        href: this.href,
        style: mapStyle(this.style, this, this[$richText])
      },
      children,
      value
    });
  }

}

class A extends XhtmlObject {
  constructor(attributes) {
    super(attributes, "a");
    this.href = (0, _html_utils.fixURL)(attributes.href) || "";
  }

}

class B extends XhtmlObject {
  constructor(attributes) {
    super(attributes, "b");
  }

  [_xfa_object.$pushGlyphs](measure) {
    measure.pushFont({
      weight: "bold"
    });

    super[_xfa_object.$pushGlyphs](measure);

    measure.popFont();
  }

}

class Body extends XhtmlObject {
  constructor(attributes) {
    super(attributes, "body");
  }

  [_xfa_object.$toHTML](availableSpace) {
    const res = super[_xfa_object.$toHTML](availableSpace);

    const {
      html
    } = res;

    if (!html) {
      return _utils.HTMLResult.EMPTY;
    }

    html.name = "div";
    html.attributes.class = ["xfaRich"];
    return res;
  }

}

class Br extends XhtmlObject {
  constructor(attributes) {
    super(attributes, "br");
  }

  [_xfa_object.$text]() {
    return "\n";
  }

  [_xfa_object.$pushGlyphs](measure) {
    measure.addString("\n");
  }

  [_xfa_object.$toHTML](availableSpace) {
    return _utils.HTMLResult.success({
      name: "br"
    });
  }

}

class Html extends XhtmlObject {
  constructor(attributes) {
    super(attributes, "html");
  }

  [_xfa_object.$toHTML](availableSpace) {
    const children = [];
    this[_xfa_object.$extra] = {
      children
    };

    this[_xfa_object.$childrenToHTML]({});

    if (children.length === 0) {
      return _utils.HTMLResult.success({
        name: "div",
        attributes: {
          class: ["xfaRich"],
          style: {}
        },
        value: this[_xfa_object.$content] || ""
      });
    }

    if (children.length === 1) {
      const child = children[0];

      if (child.attributes && child.attributes.class.includes("xfaRich")) {
        return _utils.HTMLResult.success(child);
      }
    }

    return _utils.HTMLResult.success({
      name: "div",
      attributes: {
        class: ["xfaRich"],
        style: {}
      },
      children
    });
  }

}

class I extends XhtmlObject {
  constructor(attributes) {
    super(attributes, "i");
  }

  [_xfa_object.$pushGlyphs](measure) {
    measure.pushFont({
      posture: "italic"
    });

    super[_xfa_object.$pushGlyphs](measure);

    measure.popFont();
  }

}

class Li extends XhtmlObject {
  constructor(attributes) {
    super(attributes, "li");
  }

}

class Ol extends XhtmlObject {
  constructor(attributes) {
    super(attributes, "ol");
  }

}

class P extends XhtmlObject {
  constructor(attributes) {
    super(attributes, "p");
  }

  [_xfa_object.$pushGlyphs](measure) {
    super[_xfa_object.$pushGlyphs](measure, false);

    measure.addString("\n");
    measure.addPara();
    measure.popFont();
  }

  [_xfa_object.$text]() {
    const siblings = this[_xfa_object.$getParent]()[_xfa_object.$getChildren]();

    if (siblings.at(-1) === this) {
      return super[_xfa_object.$text]();
    }

    return super[_xfa_object.$text]() + "\n";
  }

}

class Span extends XhtmlObject {
  constructor(attributes) {
    super(attributes, "span");
  }

}

class Sub extends XhtmlObject {
  constructor(attributes) {
    super(attributes, "sub");
  }

}

class Sup extends XhtmlObject {
  constructor(attributes) {
    super(attributes, "sup");
  }

}

class Ul extends XhtmlObject {
  constructor(attributes) {
    super(attributes, "ul");
  }

}

class XhtmlNamespace {
  static [_namespaces.$buildXFAObject](name, attributes) {
    if (XhtmlNamespace.hasOwnProperty(name)) {
      return XhtmlNamespace[name](attributes);
    }

    return undefined;
  }

  static a(attributes) {
    return new A(attributes);
  }

  static b(attributes) {
    return new B(attributes);
  }

  static body(attributes) {
    return new Body(attributes);
  }

  static br(attributes) {
    return new Br(attributes);
  }

  static html(attributes) {
    return new Html(attributes);
  }

  static i(attributes) {
    return new I(attributes);
  }

  static li(attributes) {
    return new Li(attributes);
  }

  static ol(attributes) {
    return new Ol(attributes);
  }

  static p(attributes) {
    return new P(attributes);
  }

  static span(attributes) {
    return new Span(attributes);
  }

  static sub(attributes) {
    return new Sub(attributes);
  }

  static sup(attributes) {
    return new Sup(attributes);
  }

  static ul(attributes) {
    return new Ul(attributes);
  }

}

exports.XhtmlNamespace = XhtmlNamespace;