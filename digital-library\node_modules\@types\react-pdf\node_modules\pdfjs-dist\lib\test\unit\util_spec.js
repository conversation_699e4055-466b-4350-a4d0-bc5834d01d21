/**
 * @licstart The following is the entire license notice for the
 * JavaScript code in this page
 *
 * Copyright 2022 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * @licend The above is the entire license notice for the
 * JavaScript code in this page
 */
"use strict";

var _util = require("../../shared/util.js");

describe("util", function () {
  describe("bytesToString", function () {
    it("handles non-array arguments", function () {
      expect(function () {
        (0, _util.bytesToString)(null);
      }).toThrow(new Error("Invalid argument for bytesToString"));
    });
    it("handles array arguments with a length not exceeding the maximum", function () {
      expect((0, _util.bytesToString)(new Uint8Array([]))).toEqual("");
      expect((0, _util.bytesToString)(new Uint8Array([102, 111, 111]))).toEqual("foo");
    });
    it("handles array arguments with a length exceeding the maximum", function () {
      const length = 10000;
      const bytes = new Uint8Array(length);

      for (let i = 0; i < length; i++) {
        bytes[i] = "a".charCodeAt(0);
      }

      const string = "a".repeat(length);
      expect((0, _util.bytesToString)(bytes)).toEqual(string);
    });
  });
  describe("isArrayBuffer", function () {
    it("handles array buffer values", function () {
      expect((0, _util.isArrayBuffer)(new ArrayBuffer(0))).toEqual(true);
      expect((0, _util.isArrayBuffer)(new Uint8Array(0))).toEqual(true);
    });
    it("handles non-array buffer values", function () {
      expect((0, _util.isArrayBuffer)("true")).toEqual(false);
      expect((0, _util.isArrayBuffer)(1)).toEqual(false);
      expect((0, _util.isArrayBuffer)(null)).toEqual(false);
      expect((0, _util.isArrayBuffer)(undefined)).toEqual(false);
    });
  });
  describe("string32", function () {
    it("converts unsigned 32-bit integers to strings", function () {
      expect((0, _util.string32)(0x74727565)).toEqual("true");
      expect((0, _util.string32)(0x74797031)).toEqual("typ1");
      expect((0, _util.string32)(0x4f54544f)).toEqual("OTTO");
    });
  });
  describe("stringToBytes", function () {
    it("handles non-string arguments", function () {
      expect(function () {
        (0, _util.stringToBytes)(null);
      }).toThrow(new Error("Invalid argument for stringToBytes"));
    });
    it("handles string arguments", function () {
      expect((0, _util.stringToBytes)("")).toEqual(new Uint8Array([]));
      expect((0, _util.stringToBytes)("foo")).toEqual(new Uint8Array([102, 111, 111]));
    });
  });
  describe("stringToPDFString", function () {
    it("handles ISO Latin 1 strings", function () {
      const str = "\x8Dstring\x8E";
      expect((0, _util.stringToPDFString)(str)).toEqual("\u201Cstring\u201D");
    });
    it("handles UTF-16 big-endian strings", function () {
      const str = "\xFE\xFF\x00\x73\x00\x74\x00\x72\x00\x69\x00\x6E\x00\x67";
      expect((0, _util.stringToPDFString)(str)).toEqual("string");
    });
    it("handles UTF-16 little-endian strings", function () {
      const str = "\xFF\xFE\x73\x00\x74\x00\x72\x00\x69\x00\x6E\x00\x67\x00";
      expect((0, _util.stringToPDFString)(str)).toEqual("string");
    });
    it("handles UTF-8 strings", function () {
      const simpleStr = "\xEF\xBB\xBF\x73\x74\x72\x69\x6E\x67";
      expect((0, _util.stringToPDFString)(simpleStr)).toEqual("string");
      const complexStr = "\xEF\xBB\xBF\xE8\xA1\xA8\xE3\x83\x9D\xE3\x81\x82\x41\xE9\xB7\x97" + "\xC5\x92\xC3\xA9\xEF\xBC\xA2\xE9\x80\x8D\xC3\x9C\xC3\x9F\xC2\xAA" + "\xC4\x85\xC3\xB1\xE4\xB8\x82\xE3\x90\x80\xF0\xA0\x80\x80";
      expect((0, _util.stringToPDFString)(complexStr)).toEqual("表ポあA鷗ŒéＢ逍Üßªąñ丂㐀𠀀");
    });
    it("handles empty strings", function () {
      const str1 = "";
      expect((0, _util.stringToPDFString)(str1)).toEqual("");
      const str2 = "\xFE\xFF";
      expect((0, _util.stringToPDFString)(str2)).toEqual("");
      const str3 = "\xFF\xFE";
      expect((0, _util.stringToPDFString)(str3)).toEqual("");
      const str4 = "\xEF\xBB\xBF";
      expect((0, _util.stringToPDFString)(str4)).toEqual("");
    });
  });
  describe("ReadableStream", function () {
    it("should return an Object", function () {
      const readable = new ReadableStream();
      expect(typeof readable).toEqual("object");
    });
    it("should have property getReader", function () {
      const readable = new ReadableStream();
      expect(typeof readable.getReader).toEqual("function");
    });
  });
  describe("URL", function () {
    it("should return an Object", function () {
      const url = new URL("https://example.com");
      expect(typeof url).toEqual("object");
    });
    it("should have property `href`", function () {
      const url = new URL("https://example.com");
      expect(typeof url.href).toEqual("string");
    });
  });
  describe("createValidAbsoluteUrl", function () {
    it("handles invalid URLs", function () {
      expect((0, _util.createValidAbsoluteUrl)(undefined, undefined)).toEqual(null);
      expect((0, _util.createValidAbsoluteUrl)(null, null)).toEqual(null);
      expect((0, _util.createValidAbsoluteUrl)("/foo", "/bar")).toEqual(null);
    });
    it("handles URLs that do not use an allowed protocol", function () {
      expect((0, _util.createValidAbsoluteUrl)("magnet:?foo", null)).toEqual(null);
    });
    it("correctly creates a valid URL for allowed protocols", function () {
      expect((0, _util.createValidAbsoluteUrl)("http://www.mozilla.org/foo", null)).toEqual(new URL("http://www.mozilla.org/foo"));
      expect((0, _util.createValidAbsoluteUrl)("/foo", "http://www.mozilla.org")).toEqual(new URL("http://www.mozilla.org/foo"));
      expect((0, _util.createValidAbsoluteUrl)("https://www.mozilla.org/foo", null)).toEqual(new URL("https://www.mozilla.org/foo"));
      expect((0, _util.createValidAbsoluteUrl)("/foo", "https://www.mozilla.org")).toEqual(new URL("https://www.mozilla.org/foo"));
      expect((0, _util.createValidAbsoluteUrl)("ftp://www.mozilla.org/foo", null)).toEqual(new URL("ftp://www.mozilla.org/foo"));
      expect((0, _util.createValidAbsoluteUrl)("/foo", "ftp://www.mozilla.org")).toEqual(new URL("ftp://www.mozilla.org/foo"));
      expect((0, _util.createValidAbsoluteUrl)("mailto:<EMAIL>", null)).toEqual(new URL("mailto:<EMAIL>"));
      expect((0, _util.createValidAbsoluteUrl)("/foo", "mailto:<EMAIL>")).toEqual(null);
      expect((0, _util.createValidAbsoluteUrl)("tel:+0123456789", null)).toEqual(new URL("tel:+0123456789"));
      expect((0, _util.createValidAbsoluteUrl)("/foo", "tel:0123456789")).toEqual(null);
    });
  });
  describe("createPromiseCapability", function () {
    it("should resolve with correct data", async function () {
      const promiseCapability = (0, _util.createPromiseCapability)();
      expect(promiseCapability.settled).toEqual(false);
      promiseCapability.resolve({
        test: "abc"
      });
      const data = await promiseCapability.promise;
      expect(promiseCapability.settled).toEqual(true);
      expect(data).toEqual({
        test: "abc"
      });
    });
    it("should reject with correct reason", async function () {
      const promiseCapability = (0, _util.createPromiseCapability)();
      expect(promiseCapability.settled).toEqual(false);
      promiseCapability.reject(new Error("reason"));

      try {
        await promiseCapability.promise;
        expect(false).toEqual(true);
      } catch (reason) {
        expect(promiseCapability.settled).toEqual(true);
        expect(reason instanceof Error).toEqual(true);
        expect(reason.message).toEqual("reason");
      }
    });
  });
  describe("escapeString", function () {
    it("should escape (, ), \\n, \\r, and \\", function () {
      expect((0, _util.escapeString)("((a\\a))\n(b(b\\b)\rb)")).toEqual("\\(\\(a\\\\a\\)\\)\\n\\(b\\(b\\\\b\\)\\rb\\)");
    });
  });
  describe("getModificationDate", function () {
    it("should get a correctly formatted date", function () {
      const date = new Date(Date.UTC(3141, 5, 9, 2, 6, 53));
      expect((0, _util.getModificationDate)(date)).toEqual("31410609020653");
    });
  });
  describe("isAscii", function () {
    it("handles ascii/non-ascii strings", function () {
      expect((0, _util.isAscii)("hello world")).toEqual(true);
      expect((0, _util.isAscii)("こんにちは世界の")).toEqual(false);
      expect((0, _util.isAscii)("hello world in Japanese is こんにちは世界の")).toEqual(false);
    });
  });
  describe("stringToUTF16BEString", function () {
    it("should encode a string in UTF16BE with a BOM", function () {
      expect((0, _util.stringToUTF16BEString)("hello world")).toEqual("\xfe\xff\0h\0e\0l\0l\0o\0 \0w\0o\0r\0l\0d");
      expect((0, _util.stringToUTF16BEString)("こんにちは世界の")).toEqual("\xfe\xff\x30\x53\x30\x93\x30\x6b\x30\x61" + "\x30\x6f\x4e\x16\x75\x4c\x30\x6e");
    });
  });
});