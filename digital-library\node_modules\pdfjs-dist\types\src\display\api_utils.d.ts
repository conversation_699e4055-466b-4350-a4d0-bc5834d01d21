export function getDataProp(val: any): Uint8Array<any>;
export function getFactoryUrlProp(val: any): string | null;
export function getUrlProp(val: any): string | null;
export function isNameProxy(v: any): boolean;
export function isRefProxy(v: any): boolean;
export const isValidExplicitDest: (dest?: any) => boolean;
export class LoopbackPort {
    postMessage(obj: any, transfer: any): void;
    addEventListener(name: any, listener: any, options?: null): void;
    removeEventListener(name: any, listener: any): void;
    terminate(): void;
    #private;
}
