export class ColorPicker {
    static "__#23@#l10nColor": null;
    static get _keyboardManager(): any;
    constructor({ editor, uiManager }: {
        editor?: null | undefined;
        uiManager?: null | undefined;
    });
    renderButton(): HTMLButtonElement;
    renderMainDropdown(): HTMLDivElement;
    _colorSelectFromKeyboard(event: any): void;
    _moveToNext(event: any): void;
    _moveToPrevious(event: any): void;
    _moveToBeginning(event: any): void;
    _moveToEnd(event: any): void;
    hideDropdown(): void;
    _hideDropdownFromKeyboard(): void;
    updateColor(color: any): void;
    destroy(): void;
    #private;
}
