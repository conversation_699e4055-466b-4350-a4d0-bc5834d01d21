<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مكتبتي الحرة - المكتبة الرقمية المجانية</title>
    <meta name="description" content="مكتبة رقمية عربية مجانية تحتوي على آلاف الكتب في جميع المجالات">
    <meta name="keywords" content="كتب, مكتبة, قراءة, تحميل, عربي, مجاني">
    <meta name="author" content="مكتبتي الحرة">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#4e73df">
    <link rel="manifest" href="manifest.json">
    <link rel="icon" type="image/png" href="assets/images/favicon.png">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Cairo & Amiri -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">

    <!-- CSS محسن للجمال والتفاعل -->
    <style>
        /* متغيرات الألوان المحسنة */
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --elegant-shadow: 0 10px 40px rgba(0,0,0,0.1);
            --hover-shadow: 0 20px 60px rgba(0,0,0,0.15);
            --card-shadow: 0 8px 32px rgba(0,0,0,0.08);
            --text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* تحسينات عامة */
        * {
            box-sizing: border-box;
        }

        body {
            overflow-x: hidden;
            position: relative;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Cairo', sans-serif;
        }

        /* تحسين شريط التنقل */
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: var(--elegant-shadow);
            transition: all 0.3s ease;
        }

        .navbar-brand {
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
            font-size: 1.5rem;
            text-shadow: var(--text-shadow);
        }

        .nav-link {
            position: relative;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: var(--primary-gradient);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link:hover::before,
        .nav-link.active::before {
            width: 80%;
        }

        .nav-link:hover {
            color: #667eea !important;
            transform: translateY(-2px);
        }

        /* تحسين القسم الرئيسي */
        .hero-section {
            background: var(--primary-gradient);
            position: relative;
            overflow: hidden;
            min-height: 600px;
            display: flex;
            align-items: center;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,255,0.1)"><polygon points="1000,100 1000,0 0,100"/></svg>');
            background-size: cover;
        }

        .hero-section::after {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: heroFloat 20s ease-in-out infinite;
        }

        @keyframes heroFloat {
            0%, 100% { transform: translate(0, 0) rotate(0deg); }
            33% { transform: translate(-30px, -30px) rotate(120deg); }
            66% { transform: translate(30px, -20px) rotate(240deg); }
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 800;
            color: white;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            margin-bottom: 1.5rem;
            animation: slideInUp 1s ease-out;
        }

        .hero-subtitle {
            font-size: 1.3rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 2rem;
            animation: slideInUp 1s ease-out 0.2s both;
        }

        .hero-buttons {
            animation: slideInUp 1s ease-out 0.4s both;
        }

        .hero-image {
            position: relative;
            z-index: 2;
            animation: floatImage 6s ease-in-out infinite;
            filter: drop-shadow(0 20px 40px rgba(0,0,0,0.3));
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes floatImage {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        /* أزرار محسنة */
        .btn-elegant {
            background: var(--secondary-gradient);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 50px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: var(--card-shadow);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-elegant::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s ease;
        }

        .btn-elegant:hover::before {
            left: 100%;
        }

        .btn-elegant:hover {
            transform: translateY(-3px);
            box-shadow: var(--hover-shadow);
            color: white;
        }

        .btn-outline-elegant {
            background: transparent;
            border: 2px solid white;
            color: white;
            padding: 12px 30px;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-outline-elegant:hover {
            background: white;
            color: #667eea;
            transform: translateY(-3px);
            box-shadow: var(--hover-shadow);
        }

        /* تحسين مربع البحث */
        .search-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            position: relative;
            z-index: 10;
            margin-top: -50px;
        }

        .search-box {
            background: white;
            border-radius: 20px;
            box-shadow: var(--elegant-shadow);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
        }

        .search-box:hover {
            transform: translateY(-5px);
            box-shadow: var(--hover-shadow);
        }

        .form-control, .form-select {
            border: none;
            background: transparent;
            font-size: 1.1rem;
            padding: 15px 20px;
        }

        .form-control:focus, .form-select:focus {
            box-shadow: none;
            border: none;
            background: transparent;
        }

        /* العناصر المتحركة في Hero */
        .floating-circles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            animation: float 6s ease-in-out infinite;
        }

        .circle-1 {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .circle-2 {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 20%;
            animation-delay: 2s;
        }

        .circle-3 {
            width: 60px;
            height: 60px;
            top: 80%;
            left: 60%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* كتب ثلاثية الأبعاد */
        .hero-books-stack {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 400px;
            perspective: 1000px;
        }

        .book-3d {
            position: absolute;
            width: 120px;
            height: 160px;
            transform-style: preserve-3d;
            animation: bookFloat 4s ease-in-out infinite;
        }

        .book-1 {
            z-index: 3;
            animation-delay: 0s;
        }

        .book-2 {
            z-index: 2;
            transform: translateX(-40px) rotateY(-15deg);
            animation-delay: 1s;
        }

        .book-3 {
            z-index: 1;
            transform: translateX(40px) rotateY(15deg);
            animation-delay: 2s;
        }

        .book-cover {
            width: 100%;
            height: 100%;
            border-radius: 8px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            position: relative;
            overflow: hidden;
        }

        .book-cover::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%);
            animation: shine 3s ease-in-out infinite;
        }

        .book-spine {
            position: absolute;
            right: -8px;
            top: 0;
            width: 8px;
            height: 100%;
            background: rgba(0,0,0,0.2);
            transform: rotateY(90deg);
            transform-origin: left;
        }

        @keyframes bookFloat {
            0%, 100% { transform: translateY(0px) rotateX(0deg); }
            50% { transform: translateY(-15px) rotateX(5deg); }
        }

        @keyframes shine {
            0%, 100% { transform: translateX(-100%); }
            50% { transform: translateX(100%); }
        }

        /* الأيقونات المتحركة */
        .floating-icons {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .floating-icon {
            position: absolute;
            font-size: 2rem;
            color: rgba(255,255,255,0.3);
            animation: iconFloat 8s ease-in-out infinite;
        }

        .icon-1 {
            top: 15%;
            left: 15%;
            animation-delay: 0s;
        }

        .icon-2 {
            top: 25%;
            right: 10%;
            animation-delay: 2s;
        }

        .icon-3 {
            bottom: 30%;
            left: 20%;
            animation-delay: 4s;
        }

        .icon-4 {
            bottom: 20%;
            right: 25%;
            animation-delay: 6s;
        }

        @keyframes iconFloat {
            0%, 100% {
                transform: translateY(0px) scale(1);
                opacity: 0.3;
            }
            50% {
                transform: translateY(-30px) scale(1.2);
                opacity: 0.6;
            }
        }

        /* الموجة السفلية */
        .hero-wave {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            overflow: hidden;
            line-height: 0;
        }

        .hero-wave svg {
            position: relative;
            display: block;
            width: calc(100% + 1.3px);
            height: 60px;
            fill: #f8f9fa;
        }

        /* إحصائيات Hero */
        .hero-stats .stat-item {
            padding: 1rem;
            border-radius: 15px;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
        }

        .hero-stats .stat-item:hover {
            transform: translateY(-5px);
            background: rgba(255,255,255,0.2);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 800;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        /* تحسينات البطاقات */
        .card {
            border: none;
            border-radius: 20px;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            background: white;
            box-shadow: var(--card-shadow);
        }

        .card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: var(--hover-shadow);
        }

        .book-card {
            position: relative;
            overflow: hidden;
        }

        .book-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s ease;
            z-index: 1;
        }

        .book-card:hover::before {
            left: 100%;
        }

        .book-card img {
            transition: all 0.4s ease;
        }

        .book-card:hover img {
            transform: scale(1.1);
        }

        .book-card .card-body {
            position: relative;
            z-index: 2;
        }

        /* شارات الكتب */
        .badge {
            font-size: 0.75rem;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-weight: 600;
        }

        /* تحسين الأزرار */
        .btn {
            border-radius: 50px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255,255,255,0.3);
            border-radius: 50%;
            transition: all 0.3s ease;
            transform: translate(-50%, -50%);
        }

        .btn:hover::before {
            width: 300px;
            height: 300px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        /* تجاوب الشاشات الصغيرة */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .hero-books-stack {
                height: 300px;
            }

            .book-3d {
                width: 80px;
                height: 120px;
            }

            .floating-icon {
                font-size: 1.5rem;
            }

            .hero-stats .stat-number {
                font-size: 1.5rem;
            }
        }
        .container-fluid {
            overflow-x: hidden;
        }

        /* تحسين الأداء */
        img {
            max-width: 100%;
            height: auto;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold text-primary" href="index.html">
                <i class="fas fa-book-open me-2"></i>
                مكتبتي الحرة
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.html">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="books.html">
                            <i class="fas fa-book me-1"></i>تصفح الكتب
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="book-details.html?id=1">
                            <i class="fas fa-star me-1"></i>الكتب المميزة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reader.html">
                            <i class="fas fa-book-reader me-1"></i>القارئ
                        </a>
                    </li>
                </ul>
                
                <div class="d-flex align-items-center">
                    <!-- Dark Mode Toggle -->
                    <button class="btn btn-outline-secondary me-2" id="darkModeToggle">
                        <i class="fas fa-moon"></i>
                    </button>
                    
                    <!-- Login/Register Buttons -->
                    <a href="login.html" class="btn btn-outline-primary me-2">تسجيل الدخول</a>
                    <a href="register.html" class="btn btn-primary">إنشاء حساب</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section text-white">
        <div class="container">
            <div class="row align-items-center min-vh-75">
                <div class="col-lg-6 hero-content">
                    <div class="hero-badge mb-4">
                        <span class="badge bg-light text-primary px-4 py-2 rounded-pill">
                            <i class="fas fa-star me-2"></i>
                            المكتبة الرقمية الأولى عربياً
                        </span>
                    </div>
                    <h1 class="hero-title">
                        مرحباً بك في
                        <span class="d-block text-warning">مكتبتي الحرة</span>
                    </h1>
                    <p class="hero-subtitle">
                        اكتشف عالماً لا محدود من المعرفة والثقافة. آلاف الكتب العربية والعالمية
                        متاحة للقراءة والتحميل مجاناً بأعلى جودة وأحدث التقنيات.
                    </p>
                    <div class="hero-buttons d-flex flex-wrap gap-3">
                        <a href="books.html" class="btn btn-elegant btn-lg">
                            <i class="fas fa-book-open me-2"></i>
                            استكشف المكتبة
                        </a>
                        <a href="register.html" class="btn btn-outline-elegant btn-lg">
                            <i class="fas fa-user-plus me-2"></i>
                            انضم إلينا مجاناً
                        </a>
                    </div>

                    <!-- إحصائيات سريعة -->
                    <div class="hero-stats mt-5">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="stat-item">
                                    <h3 class="stat-number text-warning mb-1" id="heroBooks">1,250</h3>
                                    <p class="stat-label mb-0">كتاب</p>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-item">
                                    <h3 class="stat-number text-warning mb-1" id="heroUsers">15,420</h3>
                                    <p class="stat-label mb-0">قارئ</p>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-item">
                                    <h3 class="stat-number text-warning mb-1" id="heroDownloads">89,650</h3>
                                    <p class="stat-label mb-0">تحميل</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 text-center">
                    <div class="hero-image-container position-relative">
                        <!-- دوائر زخرفية -->
                        <div class="floating-circles">
                            <div class="circle circle-1"></div>
                            <div class="circle circle-2"></div>
                            <div class="circle circle-3"></div>
                        </div>

                        <!-- صورة الكتب -->
                        <div class="hero-books-stack">
                            <div class="book-3d book-1">
                                <div class="book-cover bg-primary"></div>
                                <div class="book-spine"></div>
                            </div>
                            <div class="book-3d book-2">
                                <div class="book-cover bg-success"></div>
                                <div class="book-spine"></div>
                            </div>
                            <div class="book-3d book-3">
                                <div class="book-cover bg-warning"></div>
                                <div class="book-spine"></div>
                            </div>
                        </div>

                        <!-- أيقونات متحركة -->
                        <div class="floating-icons">
                            <i class="fas fa-book floating-icon icon-1"></i>
                            <i class="fas fa-graduation-cap floating-icon icon-2"></i>
                            <i class="fas fa-lightbulb floating-icon icon-3"></i>
                            <i class="fas fa-heart floating-icon icon-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- موجة سفلية -->
        <div class="hero-wave">
            <svg viewBox="0 0 1200 120" preserveAspectRatio="none">
                <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25" fill="currentColor"></path>
                <path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5" fill="currentColor"></path>
                <path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" fill="currentColor"></path>
            </svg>
        </div>
    </section>

    <!-- Search Section -->
    <section class="search-section py-4 bg-light">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="search-box bg-white p-4 rounded-3 shadow-sm">
                        <form class="d-flex" onsubmit="performSearch(event)">
                            <div class="input-group">
                                <input type="text" id="searchInput" class="form-control form-control-lg"
                                       placeholder="ابحث عن كتاب، مؤلف، أو موضوع..."
                                       autocomplete="off">
                                <select id="categoryFilter" class="form-select" style="max-width: 150px;">
                                    <option value="">جميع الأقسام</option>
                                    <option value="literature">الأدب</option>
                                    <option value="science">العلوم</option>
                                    <option value="history">التاريخ</option>
                                    <option value="philosophy">الفلسفة</option>
                                    <option value="religion">الدين</option>
                                    <option value="children">الأطفال</option>
                                    <option value="novels">الروايات</option>
                                    <option value="poetry">الشعر</option>
                                </select>
                                <button class="btn btn-primary btn-lg" type="submit">
                                    <i class="fas fa-search me-1"></i>
                                    بحث
                                </button>
                            </div>
                        </form>

                        <!-- اقتراحات البحث -->
                        <div id="searchSuggestions" class="mt-2" style="display: none;">
                            <small class="text-muted">اقتراحات:</small>
                            <div class="d-flex flex-wrap gap-2 mt-1">
                                <span class="badge bg-light text-dark search-suggestion" onclick="quickSearch('مئة عام من العزلة')">مئة عام من العزلة</span>
                                <span class="badge bg-light text-dark search-suggestion" onclick="quickSearch('نجيب محفوظ')">نجيب محفوظ</span>
                                <span class="badge bg-light text-dark search-suggestion" onclick="quickSearch('الأدب العربي')">الأدب العربي</span>
                                <span class="badge bg-light text-dark search-suggestion" onclick="quickSearch('التاريخ الإسلامي')">التاريخ الإسلامي</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Sections -->
    <section class="featured-sections py-5">
        <div class="container">
            <!-- Latest Books -->
            <div class="row mb-5">
                <div class="col-12">
                    <h2 class="section-title mb-4">
                        <i class="fas fa-star text-warning me-2"></i>
                        أحدث الكتب
                    </h2>
                    <div class="row" id="latestBooks">
                        <!-- كتاب 1 -->
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <div class="card book-card h-100 shadow-sm">
                                <div class="position-relative">
                                    <img src="assets/images/books/book1.jpg" class="card-img-top" alt="مئة عام من العزلة" style="height: 250px; object-fit: cover;">
                                    <span class="badge bg-success position-absolute top-0 end-0 m-2">جديد</span>
                                </div>
                                <div class="card-body d-flex flex-column">
                                    <h6 class="card-title">مئة عام من العزلة</h6>
                                    <p class="card-text text-muted small">غابرييل غارسيا ماركيز</p>
                                    <div class="mt-auto">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="rating">
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <small class="text-muted ms-1">(4.8)</small>
                                            </div>
                                            <small class="text-muted">
                                                <i class="fas fa-download me-1"></i>1.2k
                                            </small>
                                        </div>
                                        <div class="d-flex gap-2 mt-2">
                                            <button class="btn btn-primary btn-sm flex-fill" onclick="viewBook(1)">
                                                <i class="fas fa-eye me-1"></i>عرض
                                            </button>
                                            <button class="btn btn-outline-success btn-sm" onclick="downloadBook(1)">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="btn btn-outline-danger btn-sm" onclick="toggleFavorite(1)">
                                                <i class="far fa-heart"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- كتاب 2 -->
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <div class="card book-card h-100 shadow-sm">
                                <div class="position-relative">
                                    <img src="assets/images/books/book2.jpg" class="card-img-top" alt="الأسود يليق بك" style="height: 250px; object-fit: cover;">
                                    <span class="badge bg-warning position-absolute top-0 end-0 m-2">مميز</span>
                                </div>
                                <div class="card-body d-flex flex-column">
                                    <h6 class="card-title">الأسود يليق بك</h6>
                                    <p class="card-text text-muted small">أحلام مستغانمي</p>
                                    <div class="mt-auto">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="rating">
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="far fa-star text-warning"></i>
                                                <small class="text-muted ms-1">(4.5)</small>
                                            </div>
                                            <small class="text-muted">
                                                <i class="fas fa-download me-1"></i>890
                                            </small>
                                        </div>
                                        <div class="d-flex gap-2 mt-2">
                                            <button class="btn btn-primary btn-sm flex-fill" onclick="viewBook(2)">
                                                <i class="fas fa-eye me-1"></i>عرض
                                            </button>
                                            <button class="btn btn-outline-success btn-sm" onclick="downloadBook(2)">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="btn btn-outline-danger btn-sm" onclick="toggleFavorite(2)">
                                                <i class="far fa-heart"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- كتاب 3 -->
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <div class="card book-card h-100 shadow-sm">
                                <div class="position-relative">
                                    <img src="assets/images/books/book3.jpg" class="card-img-top" alt="مدن الملح" style="height: 250px; object-fit: cover;">
                                </div>
                                <div class="card-body d-flex flex-column">
                                    <h6 class="card-title">مدن الملح</h6>
                                    <p class="card-text text-muted small">عبد الرحمن منيف</p>
                                    <div class="mt-auto">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="rating">
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <small class="text-muted ms-1">(4.9)</small>
                                            </div>
                                            <small class="text-muted">
                                                <i class="fas fa-download me-1"></i>2.1k
                                            </small>
                                        </div>
                                        <div class="d-flex gap-2 mt-2">
                                            <button class="btn btn-primary btn-sm flex-fill" onclick="viewBook(3)">
                                                <i class="fas fa-eye me-1"></i>عرض
                                            </button>
                                            <button class="btn btn-outline-success btn-sm" onclick="downloadBook(3)">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="btn btn-outline-danger btn-sm" onclick="toggleFavorite(3)">
                                                <i class="far fa-heart"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- كتاب 4 -->
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <div class="card book-card h-100 shadow-sm">
                                <div class="position-relative">
                                    <img src="assets/images/books/book4.jpg" class="card-img-top" alt="رجال في الشمس" style="height: 250px; object-fit: cover;">
                                </div>
                                <div class="card-body d-flex flex-column">
                                    <h6 class="card-title">رجال في الشمس</h6>
                                    <p class="card-text text-muted small">غسان كنفاني</p>
                                    <div class="mt-auto">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="rating">
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="far fa-star text-warning"></i>
                                                <small class="text-muted ms-1">(4.3)</small>
                                            </div>
                                            <small class="text-muted">
                                                <i class="fas fa-download me-1"></i>756
                                            </small>
                                        </div>
                                        <div class="d-flex gap-2 mt-2">
                                            <button class="btn btn-primary btn-sm flex-fill" onclick="viewBook(4)">
                                                <i class="fas fa-eye me-1"></i>عرض
                                            </button>
                                            <button class="btn btn-outline-success btn-sm" onclick="downloadBook(4)">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="btn btn-outline-danger btn-sm" onclick="toggleFavorite(4)">
                                                <i class="far fa-heart"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Most Popular -->
            <div class="row mb-5">
                <div class="col-12">
                    <h2 class="section-title mb-4">
                        <i class="fas fa-fire text-danger me-2"></i>
                        الأكثر قراءة
                    </h2>
                    <div class="row" id="popularBooks">
                        <!-- Books will be loaded here via JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Editor's Choice -->
            <div class="row">
                <div class="col-12">
                    <h2 class="section-title mb-4">
                        <i class="fas fa-award text-success me-2"></i>
                        اختيارات المحرر
                    </h2>
                    <div class="row" id="editorChoice">
                        <!-- Books will be loaded here via JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Categories Preview -->
    <section class="categories-preview py-5 bg-light">
        <div class="container">
            <h2 class="text-center mb-5">تصفح حسب التصنيف</h2>
            <div class="row">
                <div class="col-lg-2 col-md-4 col-6 mb-4">
                    <a href="categories.html?cat=literature" class="category-card text-decoration-none">
                        <div class="card h-100 text-center border-0 shadow-sm">
                            <div class="card-body">
                                <i class="fas fa-feather-alt fa-3x text-primary mb-3"></i>
                                <h5 class="card-title">الأدب</h5>
                                <small class="text-muted">250 كتاب</small>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-lg-2 col-md-4 col-6 mb-4">
                    <a href="categories.html?cat=science" class="category-card text-decoration-none">
                        <div class="card h-100 text-center border-0 shadow-sm">
                            <div class="card-body">
                                <i class="fas fa-flask fa-3x text-success mb-3"></i>
                                <h5 class="card-title">العلوم</h5>
                                <small class="text-muted">180 كتاب</small>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-lg-2 col-md-4 col-6 mb-4">
                    <a href="categories.html?cat=history" class="category-card text-decoration-none">
                        <div class="card h-100 text-center border-0 shadow-sm">
                            <div class="card-body">
                                <i class="fas fa-landmark fa-3x text-warning mb-3"></i>
                                <h5 class="card-title">التاريخ</h5>
                                <small class="text-muted">320 كتاب</small>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-lg-2 col-md-4 col-6 mb-4">
                    <a href="categories.html?cat=philosophy" class="category-card text-decoration-none">
                        <div class="card h-100 text-center border-0 shadow-sm">
                            <div class="card-body">
                                <i class="fas fa-brain fa-3x text-info mb-3"></i>
                                <h5 class="card-title">الفلسفة</h5>
                                <small class="text-muted">95 كتاب</small>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-lg-2 col-md-4 col-6 mb-4">
                    <a href="categories.html?cat=religion" class="category-card text-decoration-none">
                        <div class="card h-100 text-center border-0 shadow-sm">
                            <div class="card-body">
                                <i class="fas fa-mosque fa-3x text-secondary mb-3"></i>
                                <h5 class="card-title">الدين</h5>
                                <small class="text-muted">150 كتاب</small>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-lg-2 col-md-4 col-6 mb-4">
                    <a href="categories.html?cat=children" class="category-card text-decoration-none">
                        <div class="card h-100 text-center border-0 shadow-sm">
                            <div class="card-body">
                                <i class="fas fa-child fa-3x text-danger mb-3"></i>
                                <h5 class="card-title">الأطفال</h5>
                                <small class="text-muted">75 كتاب</small>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-book-open me-2"></i>
                        مكتبتي الحرة
                    </h5>
                    <p class="text-light">مكتبة رقمية مجانية تهدف إلى نشر المعرفة وإتاحة الكتب للجميع بدون قيود.</p>
                </div>
                <div class="col-lg-2 mb-4">
                    <h6 class="fw-bold mb-3">روابط سريعة</h6>
                    <ul class="list-unstyled">
                        <li><a href="index.html" class="text-light text-decoration-none">الرئيسية</a></li>
                        <li><a href="books.html" class="text-light text-decoration-none">الكتب</a></li>
                        <li><a href="categories.html" class="text-light text-decoration-none">الأقسام</a></li>
                        <li><a href="about.html" class="text-light text-decoration-none">حول الموقع</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 mb-4">
                    <h6 class="fw-bold mb-3">الحساب</h6>
                    <ul class="list-unstyled">
                        <li><a href="login.html" class="text-light text-decoration-none">تسجيل الدخول</a></li>
                        <li><a href="register.html" class="text-light text-decoration-none">إنشاء حساب</a></li>
                        <li><a href="profile.html" class="text-light text-decoration-none">الملف الشخصي</a></li>
                        <li><a href="favorites.html" class="text-light text-decoration-none">المفضلة</a></li>
                    </ul>
                </div>
                <div class="col-lg-4 mb-4">
                    <h6 class="fw-bold mb-3">تواصل معنا</h6>
                    <div class="d-flex gap-3">
                        <a href="#" class="text-light"><i class="fab fa-facebook fa-2x"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-twitter fa-2x"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-instagram fa-2x"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-youtube fa-2x"></i></a>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p class="mb-0">&copy; 2024 مكتبتي الحرة. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Stability JS (يجب تحميله أولاً) -->
    <script src="assets/js/stability.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/config.js"></script>
    <script src="assets/js/main.js"></script>

    <!-- JavaScript محسن للصفحة الرئيسية -->
    <script>
        // وظائف البحث
        function performSearch(event) {
            event.preventDefault();
            const searchTerm = document.getElementById('searchInput').value.trim();
            const category = document.getElementById('categoryFilter').value;

            if (searchTerm) {
                // إعادة توجيه لصفحة البحث
                const params = new URLSearchParams();
                params.append('search', searchTerm);
                if (category) params.append('category', category);

                window.location.href = `books.html?${params.toString()}`;
            } else {
                showToast('يرجى إدخال كلمة للبحث', 'warning');
            }
        }

        function quickSearch(term) {
            document.getElementById('searchInput').value = term;
            performSearch(new Event('submit'));
        }

        // إظهار اقتراحات البحث عند التركيز
        document.getElementById('searchInput').addEventListener('focus', function() {
            document.getElementById('searchSuggestions').style.display = 'block';
        });

        document.getElementById('searchInput').addEventListener('blur', function() {
            setTimeout(() => {
                document.getElementById('searchSuggestions').style.display = 'none';
            }, 200);
        });

        // وظائف الكتب
        function viewBook(bookId) {
            window.location.href = `book-details.html?id=${bookId}`;
        }

        function downloadBook(bookId) {
            if (!isLoggedIn()) {
                showToast('يجب تسجيل الدخول أولاً للتحميل', 'warning');
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 1500);
                return;
            }

            // محاكاة التحميل
            showToast('جاري بدء التحميل...', 'info');
            setTimeout(() => {
                showToast('تم بدء التحميل بنجاح', 'success');
            }, 1000);
        }

        function toggleFavorite(bookId) {
            if (!isLoggedIn()) {
                showToast('يجب تسجيل الدخول أولاً', 'warning');
                return;
            }

            const btn = event.target.closest('button');
            const icon = btn.querySelector('i');

            if (icon.classList.contains('far')) {
                icon.classList.remove('far');
                icon.classList.add('fas');
                btn.classList.remove('btn-outline-danger');
                btn.classList.add('btn-danger');
                showToast('تم إضافة الكتاب للمفضلة', 'success');
            } else {
                icon.classList.remove('fas');
                icon.classList.add('far');
                btn.classList.remove('btn-danger');
                btn.classList.add('btn-outline-danger');
                showToast('تم إزالة الكتاب من المفضلة', 'info');
            }
        }

        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثيرات بصرية للبطاقات
            const bookCards = document.querySelectorAll('.book-card');
            bookCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.transition = 'transform 0.2s ease';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // تحديث عدادات التحميل عشوائياً (للعرض)
            updateDownloadCounts();
        });

        function updateDownloadCounts() {
            const downloadCounts = document.querySelectorAll('.fa-download + .text-muted');
            downloadCounts.forEach(count => {
                const currentCount = parseInt(count.textContent.replace(/[^\d]/g, ''));
                if (currentCount) {
                    const newCount = currentCount + Math.floor(Math.random() * 5);
                    count.innerHTML = `<i class="fas fa-download me-1"></i>${newCount > 1000 ? (newCount/1000).toFixed(1) + 'k' : newCount}`;
                }
            });
        }

        // تحديث العدادات كل 30 ثانية
        setInterval(updateDownloadCounts, 30000);

        // تفعيل الحركات عند التمرير
        initScrollAnimations();

        // تفعيل تأثيرات الجسيمات
        initParticleEffects();

        // وظيفة مساعدة للتحقق من تسجيل الدخول
        function isLoggedIn() {
            return localStorage.getItem('isLoggedIn') === 'true' ||
                   sessionStorage.getItem('isLoggedIn') === 'true';
        }

        // تفعيل الحركات عند التمرير
        function initScrollAnimations() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';

                        // إضافة تأخير للعناصر المتتالية
                        const delay = Array.from(entry.target.parentNode.children).indexOf(entry.target) * 100;
                        entry.target.style.transitionDelay = delay + 'ms';
                    }
                });
            }, observerOptions);

            // مراقبة البطاقات
            document.querySelectorAll('.book-card').forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'all 0.6s ease';
                observer.observe(card);
            });
        }

        // تأثيرات الجسيمات
        function initParticleEffects() {
            const heroSection = document.querySelector('.hero-section');
            if (!heroSection) return;

            // إنشاء جسيمات متحركة
            for (let i = 0; i < 20; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.cssText = `
                    position: absolute;
                    width: ${Math.random() * 4 + 2}px;
                    height: ${Math.random() * 4 + 2}px;
                    background: rgba(255, 255, 255, ${Math.random() * 0.5 + 0.2});
                    border-radius: 50%;
                    pointer-events: none;
                    animation: particleFloat ${Math.random() * 10 + 10}s linear infinite;
                    left: ${Math.random() * 100}%;
                    top: ${Math.random() * 100}%;
                `;
                heroSection.appendChild(particle);
            }

            // إضافة CSS للجسيمات
            const style = document.createElement('style');
            style.textContent = `
                @keyframes particleFloat {
                    0% {
                        transform: translateY(100vh) rotate(0deg);
                        opacity: 0;
                    }
                    10% {
                        opacity: 1;
                    }
                    90% {
                        opacity: 1;
                    }
                    100% {
                        transform: translateY(-100vh) rotate(360deg);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }

        // تحسين تأثيرات البطاقات
        function enhanceCardEffects() {
            document.querySelectorAll('.book-card').forEach(card => {
                // تأثير الإضاءة عند التمرير
                card.addEventListener('mouseenter', function() {
                    this.style.boxShadow = '0 25px 80px rgba(103, 126, 234, 0.3)';
                    this.style.transform = 'translateY(-15px) scale(1.03)';

                    // تأثير الوهج
                    const glow = document.createElement('div');
                    glow.className = 'card-glow';
                    glow.style.cssText = `
                        position: absolute;
                        top: -2px;
                        left: -2px;
                        right: -2px;
                        bottom: -2px;
                        background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
                        border-radius: 22px;
                        z-index: -1;
                        filter: blur(10px);
                        opacity: 0.7;
                        animation: glowPulse 2s ease-in-out infinite alternate;
                    `;
                    this.style.position = 'relative';
                    this.appendChild(glow);
                });

                card.addEventListener('mouseleave', function() {
                    this.style.boxShadow = '';
                    this.style.transform = '';

                    const glow = this.querySelector('.card-glow');
                    if (glow) {
                        glow.remove();
                    }
                });
            });

            // إضافة CSS للوهج
            const glowStyle = document.createElement('style');
            glowStyle.textContent = `
                @keyframes glowPulse {
                    0% { opacity: 0.5; transform: scale(0.98); }
                    100% { opacity: 0.8; transform: scale(1.02); }
                }
            `;
            document.head.appendChild(glowStyle);
        }

        // تفعيل التحسينات
        setTimeout(enhanceCardEffects, 1000);

        // وظيفة إظهار الرسائل
        function showToast(message, type = 'info') {
            // إنشاء عنصر التنبيه
            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type} border-0`;
            toast.setAttribute('role', 'alert');
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;

            // إضافة التنبيه للصفحة
            let toastContainer = document.getElementById('toastContainer');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toastContainer';
                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                toastContainer.style.zIndex = '9999';
                document.body.appendChild(toastContainer);
            }

            toastContainer.appendChild(toast);

            // إظهار التنبيه
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();

            // إزالة التنبيه بعد إخفائه
            toast.addEventListener('hidden.bs.toast', function() {
                this.remove();
            });
        }
    </script>
</body>
</html>
