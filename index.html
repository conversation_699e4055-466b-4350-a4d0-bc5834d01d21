<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مكتبتي الحرة - المكتبة الرقمية المجانية</title>
    <meta name="description" content="مكتبة رقمية عربية مجانية تحتوي على آلاف الكتب في جميع المجالات">
    <meta name="keywords" content="كتب, مكتبة, قراءة, تحميل, عربي, مجاني">
    <meta name="author" content="مكتبتي الحرة">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#4e73df">
    <link rel="manifest" href="manifest.json">
    <link rel="icon" type="image/png" href="assets/images/favicon.png">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Cairo & Amiri -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">

    <!-- CSS إضافي للاستقرار -->
    <style>
        /* ضمان الاستقرار الكامل */
        * {
            box-sizing: border-box;
        }

        body {
            overflow-x: hidden;
            position: relative;
        }

        /* منع الحركة غير المرغوب فيها */
        .hero-section {
            position: relative;
            overflow: hidden;
        }

        .hero-image {
            animation: none !important;
            transform: none !important;
        }

        /* تحسين الاستقرار للبطاقات */
        .card {
            position: relative;
            transform: none !important;
        }

        .card:hover {
            transform: none !important;
        }

        /* منع التمرير الأفقي */
        .container-fluid {
            overflow-x: hidden;
        }

        /* تحسين الأداء */
        img {
            max-width: 100%;
            height: auto;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold text-primary" href="index.html">
                <i class="fas fa-book-open me-2"></i>
                مكتبتي الحرة
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.html">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="books.html">
                            <i class="fas fa-book me-1"></i>تصفح الكتب
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="book-details.html?id=1">
                            <i class="fas fa-star me-1"></i>الكتب المميزة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reader.html">
                            <i class="fas fa-book-reader me-1"></i>القارئ
                        </a>
                    </li>
                </ul>
                
                <div class="d-flex align-items-center">
                    <!-- Dark Mode Toggle -->
                    <button class="btn btn-outline-secondary me-2" id="darkModeToggle">
                        <i class="fas fa-moon"></i>
                    </button>
                    
                    <!-- Login/Register Buttons -->
                    <a href="login.html" class="btn btn-outline-primary me-2">تسجيل الدخول</a>
                    <a href="register.html" class="btn btn-primary">إنشاء حساب</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section bg-gradient-primary text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-4 fw-bold mb-4">مرحباً بك في مكتبتي الحرة</h1>
                    <p class="lead mb-4">اكتشف عالماً من المعرفة المجانية. آلاف الكتب في مختلف المجالات متاحة للقراءة والتحميل مجاناً</p>
                    <div class="d-flex gap-3">
                        <a href="books.html" class="btn btn-light btn-lg">
                            <i class="fas fa-search me-2"></i>
                            تصفح الكتب
                        </a>
                        <a href="categories.html" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-list me-2"></i>
                            الأقسام
                        </a>
                    </div>
                </div>
                <div class="col-lg-6 text-center">
                    <img src="assets/images/hero-books.png" alt="مكتبة الكتب" class="img-fluid hero-image">
                </div>
            </div>
        </div>
    </section>

    <!-- Search Section -->
    <section class="search-section py-4 bg-light">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="search-box bg-white p-4 rounded-3 shadow-sm">
                        <form class="d-flex" onsubmit="performSearch(event)">
                            <div class="input-group">
                                <input type="text" id="searchInput" class="form-control form-control-lg"
                                       placeholder="ابحث عن كتاب، مؤلف، أو موضوع..."
                                       autocomplete="off">
                                <select id="categoryFilter" class="form-select" style="max-width: 150px;">
                                    <option value="">جميع الأقسام</option>
                                    <option value="literature">الأدب</option>
                                    <option value="science">العلوم</option>
                                    <option value="history">التاريخ</option>
                                    <option value="philosophy">الفلسفة</option>
                                    <option value="religion">الدين</option>
                                    <option value="children">الأطفال</option>
                                    <option value="novels">الروايات</option>
                                    <option value="poetry">الشعر</option>
                                </select>
                                <button class="btn btn-primary btn-lg" type="submit">
                                    <i class="fas fa-search me-1"></i>
                                    بحث
                                </button>
                            </div>
                        </form>

                        <!-- اقتراحات البحث -->
                        <div id="searchSuggestions" class="mt-2" style="display: none;">
                            <small class="text-muted">اقتراحات:</small>
                            <div class="d-flex flex-wrap gap-2 mt-1">
                                <span class="badge bg-light text-dark search-suggestion" onclick="quickSearch('مئة عام من العزلة')">مئة عام من العزلة</span>
                                <span class="badge bg-light text-dark search-suggestion" onclick="quickSearch('نجيب محفوظ')">نجيب محفوظ</span>
                                <span class="badge bg-light text-dark search-suggestion" onclick="quickSearch('الأدب العربي')">الأدب العربي</span>
                                <span class="badge bg-light text-dark search-suggestion" onclick="quickSearch('التاريخ الإسلامي')">التاريخ الإسلامي</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Sections -->
    <section class="featured-sections py-5">
        <div class="container">
            <!-- Latest Books -->
            <div class="row mb-5">
                <div class="col-12">
                    <h2 class="section-title mb-4">
                        <i class="fas fa-star text-warning me-2"></i>
                        أحدث الكتب
                    </h2>
                    <div class="row" id="latestBooks">
                        <!-- كتاب 1 -->
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <div class="card book-card h-100 shadow-sm">
                                <div class="position-relative">
                                    <img src="assets/images/books/book1.jpg" class="card-img-top" alt="مئة عام من العزلة" style="height: 250px; object-fit: cover;">
                                    <span class="badge bg-success position-absolute top-0 end-0 m-2">جديد</span>
                                </div>
                                <div class="card-body d-flex flex-column">
                                    <h6 class="card-title">مئة عام من العزلة</h6>
                                    <p class="card-text text-muted small">غابرييل غارسيا ماركيز</p>
                                    <div class="mt-auto">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="rating">
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <small class="text-muted ms-1">(4.8)</small>
                                            </div>
                                            <small class="text-muted">
                                                <i class="fas fa-download me-1"></i>1.2k
                                            </small>
                                        </div>
                                        <div class="d-flex gap-2 mt-2">
                                            <button class="btn btn-primary btn-sm flex-fill" onclick="viewBook(1)">
                                                <i class="fas fa-eye me-1"></i>عرض
                                            </button>
                                            <button class="btn btn-outline-success btn-sm" onclick="downloadBook(1)">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="btn btn-outline-danger btn-sm" onclick="toggleFavorite(1)">
                                                <i class="far fa-heart"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- كتاب 2 -->
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <div class="card book-card h-100 shadow-sm">
                                <div class="position-relative">
                                    <img src="assets/images/books/book2.jpg" class="card-img-top" alt="الأسود يليق بك" style="height: 250px; object-fit: cover;">
                                    <span class="badge bg-warning position-absolute top-0 end-0 m-2">مميز</span>
                                </div>
                                <div class="card-body d-flex flex-column">
                                    <h6 class="card-title">الأسود يليق بك</h6>
                                    <p class="card-text text-muted small">أحلام مستغانمي</p>
                                    <div class="mt-auto">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="rating">
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="far fa-star text-warning"></i>
                                                <small class="text-muted ms-1">(4.5)</small>
                                            </div>
                                            <small class="text-muted">
                                                <i class="fas fa-download me-1"></i>890
                                            </small>
                                        </div>
                                        <div class="d-flex gap-2 mt-2">
                                            <button class="btn btn-primary btn-sm flex-fill" onclick="viewBook(2)">
                                                <i class="fas fa-eye me-1"></i>عرض
                                            </button>
                                            <button class="btn btn-outline-success btn-sm" onclick="downloadBook(2)">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="btn btn-outline-danger btn-sm" onclick="toggleFavorite(2)">
                                                <i class="far fa-heart"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- كتاب 3 -->
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <div class="card book-card h-100 shadow-sm">
                                <div class="position-relative">
                                    <img src="assets/images/books/book3.jpg" class="card-img-top" alt="مدن الملح" style="height: 250px; object-fit: cover;">
                                </div>
                                <div class="card-body d-flex flex-column">
                                    <h6 class="card-title">مدن الملح</h6>
                                    <p class="card-text text-muted small">عبد الرحمن منيف</p>
                                    <div class="mt-auto">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="rating">
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <small class="text-muted ms-1">(4.9)</small>
                                            </div>
                                            <small class="text-muted">
                                                <i class="fas fa-download me-1"></i>2.1k
                                            </small>
                                        </div>
                                        <div class="d-flex gap-2 mt-2">
                                            <button class="btn btn-primary btn-sm flex-fill" onclick="viewBook(3)">
                                                <i class="fas fa-eye me-1"></i>عرض
                                            </button>
                                            <button class="btn btn-outline-success btn-sm" onclick="downloadBook(3)">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="btn btn-outline-danger btn-sm" onclick="toggleFavorite(3)">
                                                <i class="far fa-heart"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- كتاب 4 -->
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <div class="card book-card h-100 shadow-sm">
                                <div class="position-relative">
                                    <img src="assets/images/books/book4.jpg" class="card-img-top" alt="رجال في الشمس" style="height: 250px; object-fit: cover;">
                                </div>
                                <div class="card-body d-flex flex-column">
                                    <h6 class="card-title">رجال في الشمس</h6>
                                    <p class="card-text text-muted small">غسان كنفاني</p>
                                    <div class="mt-auto">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="rating">
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="far fa-star text-warning"></i>
                                                <small class="text-muted ms-1">(4.3)</small>
                                            </div>
                                            <small class="text-muted">
                                                <i class="fas fa-download me-1"></i>756
                                            </small>
                                        </div>
                                        <div class="d-flex gap-2 mt-2">
                                            <button class="btn btn-primary btn-sm flex-fill" onclick="viewBook(4)">
                                                <i class="fas fa-eye me-1"></i>عرض
                                            </button>
                                            <button class="btn btn-outline-success btn-sm" onclick="downloadBook(4)">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="btn btn-outline-danger btn-sm" onclick="toggleFavorite(4)">
                                                <i class="far fa-heart"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Most Popular -->
            <div class="row mb-5">
                <div class="col-12">
                    <h2 class="section-title mb-4">
                        <i class="fas fa-fire text-danger me-2"></i>
                        الأكثر قراءة
                    </h2>
                    <div class="row" id="popularBooks">
                        <!-- Books will be loaded here via JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Editor's Choice -->
            <div class="row">
                <div class="col-12">
                    <h2 class="section-title mb-4">
                        <i class="fas fa-award text-success me-2"></i>
                        اختيارات المحرر
                    </h2>
                    <div class="row" id="editorChoice">
                        <!-- Books will be loaded here via JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Categories Preview -->
    <section class="categories-preview py-5 bg-light">
        <div class="container">
            <h2 class="text-center mb-5">تصفح حسب التصنيف</h2>
            <div class="row">
                <div class="col-lg-2 col-md-4 col-6 mb-4">
                    <a href="categories.html?cat=literature" class="category-card text-decoration-none">
                        <div class="card h-100 text-center border-0 shadow-sm">
                            <div class="card-body">
                                <i class="fas fa-feather-alt fa-3x text-primary mb-3"></i>
                                <h5 class="card-title">الأدب</h5>
                                <small class="text-muted">250 كتاب</small>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-lg-2 col-md-4 col-6 mb-4">
                    <a href="categories.html?cat=science" class="category-card text-decoration-none">
                        <div class="card h-100 text-center border-0 shadow-sm">
                            <div class="card-body">
                                <i class="fas fa-flask fa-3x text-success mb-3"></i>
                                <h5 class="card-title">العلوم</h5>
                                <small class="text-muted">180 كتاب</small>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-lg-2 col-md-4 col-6 mb-4">
                    <a href="categories.html?cat=history" class="category-card text-decoration-none">
                        <div class="card h-100 text-center border-0 shadow-sm">
                            <div class="card-body">
                                <i class="fas fa-landmark fa-3x text-warning mb-3"></i>
                                <h5 class="card-title">التاريخ</h5>
                                <small class="text-muted">320 كتاب</small>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-lg-2 col-md-4 col-6 mb-4">
                    <a href="categories.html?cat=philosophy" class="category-card text-decoration-none">
                        <div class="card h-100 text-center border-0 shadow-sm">
                            <div class="card-body">
                                <i class="fas fa-brain fa-3x text-info mb-3"></i>
                                <h5 class="card-title">الفلسفة</h5>
                                <small class="text-muted">95 كتاب</small>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-lg-2 col-md-4 col-6 mb-4">
                    <a href="categories.html?cat=religion" class="category-card text-decoration-none">
                        <div class="card h-100 text-center border-0 shadow-sm">
                            <div class="card-body">
                                <i class="fas fa-mosque fa-3x text-secondary mb-3"></i>
                                <h5 class="card-title">الدين</h5>
                                <small class="text-muted">150 كتاب</small>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-lg-2 col-md-4 col-6 mb-4">
                    <a href="categories.html?cat=children" class="category-card text-decoration-none">
                        <div class="card h-100 text-center border-0 shadow-sm">
                            <div class="card-body">
                                <i class="fas fa-child fa-3x text-danger mb-3"></i>
                                <h5 class="card-title">الأطفال</h5>
                                <small class="text-muted">75 كتاب</small>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-book-open me-2"></i>
                        مكتبتي الحرة
                    </h5>
                    <p class="text-light">مكتبة رقمية مجانية تهدف إلى نشر المعرفة وإتاحة الكتب للجميع بدون قيود.</p>
                </div>
                <div class="col-lg-2 mb-4">
                    <h6 class="fw-bold mb-3">روابط سريعة</h6>
                    <ul class="list-unstyled">
                        <li><a href="index.html" class="text-light text-decoration-none">الرئيسية</a></li>
                        <li><a href="books.html" class="text-light text-decoration-none">الكتب</a></li>
                        <li><a href="categories.html" class="text-light text-decoration-none">الأقسام</a></li>
                        <li><a href="about.html" class="text-light text-decoration-none">حول الموقع</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 mb-4">
                    <h6 class="fw-bold mb-3">الحساب</h6>
                    <ul class="list-unstyled">
                        <li><a href="login.html" class="text-light text-decoration-none">تسجيل الدخول</a></li>
                        <li><a href="register.html" class="text-light text-decoration-none">إنشاء حساب</a></li>
                        <li><a href="profile.html" class="text-light text-decoration-none">الملف الشخصي</a></li>
                        <li><a href="favorites.html" class="text-light text-decoration-none">المفضلة</a></li>
                    </ul>
                </div>
                <div class="col-lg-4 mb-4">
                    <h6 class="fw-bold mb-3">تواصل معنا</h6>
                    <div class="d-flex gap-3">
                        <a href="#" class="text-light"><i class="fab fa-facebook fa-2x"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-twitter fa-2x"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-instagram fa-2x"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-youtube fa-2x"></i></a>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p class="mb-0">&copy; 2024 مكتبتي الحرة. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Stability JS (يجب تحميله أولاً) -->
    <script src="assets/js/stability.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/config.js"></script>
    <script src="assets/js/main.js"></script>

    <!-- JavaScript محسن للصفحة الرئيسية -->
    <script>
        // وظائف البحث
        function performSearch(event) {
            event.preventDefault();
            const searchTerm = document.getElementById('searchInput').value.trim();
            const category = document.getElementById('categoryFilter').value;

            if (searchTerm) {
                // إعادة توجيه لصفحة البحث
                const params = new URLSearchParams();
                params.append('search', searchTerm);
                if (category) params.append('category', category);

                window.location.href = `books.html?${params.toString()}`;
            } else {
                showToast('يرجى إدخال كلمة للبحث', 'warning');
            }
        }

        function quickSearch(term) {
            document.getElementById('searchInput').value = term;
            performSearch(new Event('submit'));
        }

        // إظهار اقتراحات البحث عند التركيز
        document.getElementById('searchInput').addEventListener('focus', function() {
            document.getElementById('searchSuggestions').style.display = 'block';
        });

        document.getElementById('searchInput').addEventListener('blur', function() {
            setTimeout(() => {
                document.getElementById('searchSuggestions').style.display = 'none';
            }, 200);
        });

        // وظائف الكتب
        function viewBook(bookId) {
            window.location.href = `book-details.html?id=${bookId}`;
        }

        function downloadBook(bookId) {
            if (!isLoggedIn()) {
                showToast('يجب تسجيل الدخول أولاً للتحميل', 'warning');
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 1500);
                return;
            }

            // محاكاة التحميل
            showToast('جاري بدء التحميل...', 'info');
            setTimeout(() => {
                showToast('تم بدء التحميل بنجاح', 'success');
            }, 1000);
        }

        function toggleFavorite(bookId) {
            if (!isLoggedIn()) {
                showToast('يجب تسجيل الدخول أولاً', 'warning');
                return;
            }

            const btn = event.target.closest('button');
            const icon = btn.querySelector('i');

            if (icon.classList.contains('far')) {
                icon.classList.remove('far');
                icon.classList.add('fas');
                btn.classList.remove('btn-outline-danger');
                btn.classList.add('btn-danger');
                showToast('تم إضافة الكتاب للمفضلة', 'success');
            } else {
                icon.classList.remove('fas');
                icon.classList.add('far');
                btn.classList.remove('btn-danger');
                btn.classList.add('btn-outline-danger');
                showToast('تم إزالة الكتاب من المفضلة', 'info');
            }
        }

        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثيرات بصرية للبطاقات
            const bookCards = document.querySelectorAll('.book-card');
            bookCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.transition = 'transform 0.2s ease';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // تحديث عدادات التحميل عشوائياً (للعرض)
            updateDownloadCounts();
        });

        function updateDownloadCounts() {
            const downloadCounts = document.querySelectorAll('.fa-download + .text-muted');
            downloadCounts.forEach(count => {
                const currentCount = parseInt(count.textContent.replace(/[^\d]/g, ''));
                if (currentCount) {
                    const newCount = currentCount + Math.floor(Math.random() * 5);
                    count.innerHTML = `<i class="fas fa-download me-1"></i>${newCount > 1000 ? (newCount/1000).toFixed(1) + 'k' : newCount}`;
                }
            });
        }

        // تحديث العدادات كل 30 ثانية
        setInterval(updateDownloadCounts, 30000);

        // وظيفة مساعدة للتحقق من تسجيل الدخول
        function isLoggedIn() {
            return localStorage.getItem('isLoggedIn') === 'true' ||
                   sessionStorage.getItem('isLoggedIn') === 'true';
        }

        // وظيفة إظهار الرسائل
        function showToast(message, type = 'info') {
            // إنشاء عنصر التنبيه
            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type} border-0`;
            toast.setAttribute('role', 'alert');
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;

            // إضافة التنبيه للصفحة
            let toastContainer = document.getElementById('toastContainer');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toastContainer';
                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                toastContainer.style.zIndex = '9999';
                document.body.appendChild(toastContainer);
            }

            toastContainer.appendChild(toast);

            // إظهار التنبيه
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();

            // إزالة التنبيه بعد إخفائه
            toast.addEventListener('hidden.bs.toast', function() {
                this.remove();
            });
        }
    </script>
</body>
</html>
