<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - مكتبتي الحرة</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold text-primary" href="index.html">
                <i class="fas fa-book-open me-2"></i>
                مكتبتي الحرة
            </a>
            
            <div class="d-flex align-items-center">
                <!-- Dark Mode Toggle -->
                <button class="btn btn-outline-secondary me-2" id="darkModeToggle">
                    <i class="fas fa-moon"></i>
                </button>
                
                <a href="index.html" class="btn btn-outline-primary">
                    <i class="fas fa-home me-1"></i>
                    الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <!-- Login Section -->
    <section class="login-section py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-5 col-md-7">
                    <div class="card shadow-lg border-0 rounded-4">
                        <div class="card-body p-5">
                            <!-- Header -->
                            <div class="text-center mb-4">
                                <div class="login-icon mb-3">
                                    <i class="fas fa-user-circle fa-4x text-primary"></i>
                                </div>
                                <h2 class="fw-bold text-dark mb-2">تسجيل الدخول</h2>
                                <p class="text-muted">مرحباً بك مرة أخرى في مكتبتي الحرة</p>
                            </div>

                            <!-- Login Form -->
                            <form id="loginForm" novalidate>
                                <div class="mb-3">
                                    <label for="email" class="form-label fw-semibold">
                                        <i class="fas fa-envelope me-2 text-primary"></i>
                                        البريد الإلكتروني
                                    </label>
                                    <input type="email" class="form-control form-control-lg" id="email" 
                                           placeholder="أدخل بريدك الإلكتروني" required>
                                    <div class="invalid-feedback">
                                        يرجى إدخال بريد إلكتروني صحيح
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="password" class="form-label fw-semibold">
                                        <i class="fas fa-lock me-2 text-primary"></i>
                                        كلمة المرور
                                    </label>
                                    <div class="input-group">
                                        <input type="password" class="form-control form-control-lg" id="password" 
                                               placeholder="أدخل كلمة المرور" required>
                                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="invalid-feedback">
                                        يرجى إدخال كلمة المرور
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="rememberMe">
                                            <label class="form-check-label" for="rememberMe">
                                                تذكرني
                                            </label>
                                        </div>
                                        <a href="forgot-password.html" class="text-decoration-none">
                                            نسيت كلمة المرور؟
                                        </a>
                                    </div>
                                </div>

                                <div class="d-grid mb-3">
                                    <button type="submit" class="btn btn-primary btn-lg fw-semibold" id="loginBtn">
                                        <i class="fas fa-sign-in-alt me-2"></i>
                                        تسجيل الدخول
                                    </button>
                                </div>

                                <!-- Divider -->
                                <div class="text-center mb-3">
                                    <div class="divider">
                                        <span class="divider-text bg-white px-3 text-muted">أو</span>
                                    </div>
                                </div>

                                <!-- Social Login -->
                                <div class="row g-2 mb-3">
                                    <div class="col-6">
                                        <button type="button" class="btn btn-outline-danger w-100">
                                            <i class="fab fa-google me-2"></i>
                                            Google
                                        </button>
                                    </div>
                                    <div class="col-6">
                                        <button type="button" class="btn btn-outline-primary w-100">
                                            <i class="fab fa-facebook me-2"></i>
                                            Facebook
                                        </button>
                                    </div>
                                </div>

                                <!-- Register Link -->
                                <div class="text-center">
                                    <p class="mb-0">
                                        ليس لديك حساب؟ 
                                        <a href="register.html" class="text-decoration-none fw-semibold">
                                            إنشاء حساب جديد
                                        </a>
                                    </p>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Demo Accounts -->
                    <div class="card mt-4 border-warning">
                        <div class="card-body">
                            <h6 class="card-title text-warning">
                                <i class="fas fa-info-circle me-2"></i>
                                حسابات تجريبية
                            </h6>
                            <div class="row">
                                <div class="col-md-6 mb-2">
                                    <small class="text-muted d-block">مستخدم عادي:</small>
                                    <code><EMAIL></code><br>
                                    <code>password123</code>
                                </div>
                                <div class="col-md-6 mb-2">
                                    <small class="text-muted d-block">مدير:</small>
                                    <code><EMAIL></code><br>
                                    <code>admin123</code>
                                </div>
                            </div>
                            <button class="btn btn-outline-warning btn-sm mt-2" onclick="fillDemoUser()">
                                <i class="fas fa-user me-1"></i>
                                تجربة كمستخدم
                            </button>
                            <button class="btn btn-outline-danger btn-sm mt-2" onclick="fillDemoAdmin()">
                                <i class="fas fa-user-shield me-1"></i>
                                تجربة كمدير
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">
                        <i class="fas fa-book-open me-2"></i>
                        &copy; 2024 مكتبتي الحرة. جميع الحقوق محفوظة.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="d-flex justify-content-md-end gap-3">
                        <a href="#" class="text-light"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/auth.js"></script>

    <style>
        .login-section {
            min-height: calc(100vh - 200px);
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .card {
            backdrop-filter: blur(10px);
            background-color: rgba(255, 255, 255, 0.95);
        }

        [data-theme="dark"] .card {
            background-color: rgba(45, 45, 45, 0.95);
            color: white;
        }

        .login-icon {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .divider {
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background-color: #dee2e6;
        }

        .divider-text {
            position: relative;
            z-index: 1;
        }

        [data-theme="dark"] .divider-text {
            background-color: #2d2d2d !important;
            color: #adb5bd !important;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
        }

        .btn-primary:hover {
            background: linear-gradient(45deg, #5a6fd8, #6a4190);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .card-body {
            position: relative;
            overflow: hidden;
        }

        .card-body::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            animation: shine 3s infinite;
        }

        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
    </style>
</body>
</html>
