<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>غير متصل - مكتبتي الحرة</title>
    
    <!-- Bootstrap CSS -->
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <style>
        .offline-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .offline-card {
            background: white;
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            margin: 2rem;
        }
        
        .offline-icon {
            font-size: 4rem;
            color: #6c757d;
            margin-bottom: 2rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .connection-status {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }
        
        .status-offline {
            background-color: #dc3545;
            color: white;
        }
        
        .status-online {
            background-color: #28a745;
            color: white;
        }
        
        .cached-content {
            margin-top: 2rem;
            padding: 1.5rem;
            background-color: #f8f9fa;
            border-radius: 10px;
        }
        
        .cached-item {
            display: flex;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .cached-item:last-child {
            border-bottom: none;
        }
        
        .cached-item i {
            margin-left: 0.5rem;
            color: #6c757d;
        }
        
        .retry-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 50px;
            padding: 0.75rem 2rem;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .retry-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
            color: white;
        }
        
        .loading-spinner {
            display: none;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-card">
            <!-- أيقونة الحالة -->
            <div class="offline-icon">
                <i class="fas fa-wifi-slash" id="statusIcon"></i>
            </div>
            
            <!-- حالة الاتصال -->
            <div class="connection-status status-offline" id="connectionStatus">
                <i class="fas fa-exclamation-circle me-2"></i>
                غير متصل بالإنترنت
            </div>
            
            <!-- العنوان والوصف -->
            <h2 class="mb-3">أنت غير متصل حالياً</h2>
            <p class="text-muted mb-4">
                يبدو أنك فقدت الاتصال بالإنترنت. لا تقلق، يمكنك الوصول إلى بعض المحتوى المحفوظ محلياً.
            </p>
            
            <!-- زر إعادة المحاولة -->
            <button class="btn retry-btn" onclick="checkConnection()">
                <i class="fas fa-redo me-2"></i>
                إعادة المحاولة
            </button>
            
            <!-- مؤشر التحميل -->
            <div class="loading-spinner" id="loadingSpinner">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحقق...</span>
                </div>
            </div>
            
            <!-- المحتوى المحفوظ محلياً -->
            <div class="cached-content">
                <h5 class="mb-3">
                    <i class="fas fa-archive me-2"></i>
                    المحتوى المتاح محلياً
                </h5>
                
                <div class="cached-item">
                    <i class="fas fa-home"></i>
                    <a href="index.html" class="text-decoration-none">الصفحة الرئيسية</a>
                </div>
                
                <div class="cached-item">
                    <i class="fas fa-book"></i>
                    <a href="books.html" class="text-decoration-none">مكتبة الكتب</a>
                </div>
                
                <div class="cached-item">
                    <i class="fas fa-user"></i>
                    <a href="login.html" class="text-decoration-none">تسجيل الدخول</a>
                </div>
                
                <div class="cached-item">
                    <i class="fas fa-user-plus"></i>
                    <a href="register.html" class="text-decoration-none">إنشاء حساب</a>
                </div>
            </div>
            
            <!-- نصائح للعمل بدون اتصال -->
            <div class="mt-4">
                <h6 class="text-muted">نصائح للعمل بدون اتصال:</h6>
                <ul class="list-unstyled text-start text-muted small">
                    <li><i class="fas fa-check text-success me-2"></i>يمكنك تصفح الكتب المحفوظة مسبقاً</li>
                    <li><i class="fas fa-check text-success me-2"></i>يمكنك قراءة الكتب المحملة</li>
                    <li><i class="fas fa-check text-success me-2"></i>ستتم مزامنة أنشطتك عند العودة للاتصال</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="assets/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // مراقبة حالة الاتصال
        function updateConnectionStatus() {
            const isOnline = navigator.onLine;
            const statusElement = document.getElementById('connectionStatus');
            const iconElement = document.getElementById('statusIcon');
            
            if (isOnline) {
                statusElement.className = 'connection-status status-online';
                statusElement.innerHTML = '<i class="fas fa-check-circle me-2"></i>متصل بالإنترنت';
                iconElement.className = 'fas fa-wifi';
                
                // إعادة توجيه تلقائي بعد 2 ثانية
                setTimeout(() => {
                    window.location.href = '/';
                }, 2000);
            } else {
                statusElement.className = 'connection-status status-offline';
                statusElement.innerHTML = '<i class="fas fa-exclamation-circle me-2"></i>غير متصل بالإنترنت';
                iconElement.className = 'fas fa-wifi-slash';
            }
        }
        
        // فحص الاتصال
        function checkConnection() {
            const loadingSpinner = document.getElementById('loadingSpinner');
            const retryBtn = document.querySelector('.retry-btn');
            
            loadingSpinner.style.display = 'block';
            retryBtn.disabled = true;
            
            // محاولة الاتصال بالخادم
            fetch('/', { method: 'HEAD', cache: 'no-cache' })
                .then(() => {
                    // نجح الاتصال
                    updateConnectionStatus();
                })
                .catch(() => {
                    // فشل الاتصال
                    updateConnectionStatus();
                })
                .finally(() => {
                    loadingSpinner.style.display = 'none';
                    retryBtn.disabled = false;
                });
        }
        
        // مستمعي الأحداث
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);
        
        // فحص أولي
        updateConnectionStatus();
        
        // فحص دوري كل 30 ثانية
        setInterval(checkConnection, 30000);
        
        // عرض الكتب المحفوظة محلياً
        function loadCachedBooks() {
            if ('caches' in window) {
                caches.open('api-v1.0.0').then(cache => {
                    cache.keys().then(requests => {
                        const bookRequests = requests.filter(req => 
                            req.url.includes('/api/books')
                        );
                        
                        if (bookRequests.length > 0) {
                            const cachedContent = document.querySelector('.cached-content');
                            const booksItem = document.createElement('div');
                            booksItem.className = 'cached-item';
                            booksItem.innerHTML = `
                                <i class="fas fa-database"></i>
                                <span>كتب محفوظة محلياً (${bookRequests.length})</span>
                            `;
                            cachedContent.appendChild(booksItem);
                        }
                    });
                });
            }
        }
        
        // تحميل الكتب المحفوظة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', loadCachedBooks);
        
        // تأثيرات بصرية
        document.querySelectorAll('.cached-item a').forEach(link => {
            link.addEventListener('mouseenter', function() {
                this.style.transform = 'translateX(5px)';
                this.style.transition = 'transform 0.2s ease';
            });
            
            link.addEventListener('mouseleave', function() {
                this.style.transform = 'translateX(0)';
            });
        });
    </script>
</body>
</html>
