{"name": "my-free-library", "version": "1.0.0", "description": "مكتبة رقمية مجانية باللغة العربية - A free digital library in Arabic", "main": "index.html", "scripts": {"start": "python start-server.py", "dev": "python start-server.py -p 3000", "serve": "python -m http.server 8000", "build": "echo 'Building project...' && npm run minify", "minify": "echo 'Minification would happen here in a real build process'", "test": "echo 'Tests would run here'", "lint": "echo 'Linting would happen here'", "format": "echo 'Code formatting would happen here'"}, "keywords": ["digital-library", "arabic", "books", "reading", "free", "education", "مكتبة-رقمية", "كتب", "قراءة", "تعليم"], "author": {"name": "مطور مكتبتي الحرة", "email": "<EMAIL>", "url": "https://my-free-library.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/username/my-free-library.git"}, "bugs": {"url": "https://github.com/username/my-free-library/issues"}, "homepage": "https://my-free-library.com", "dependencies": {}, "devDependencies": {}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "config": {"port": 8000, "host": "localhost"}, "directories": {"assets": "./assets", "public": "./public"}, "files": ["index.html", "assets/", "public/", "README.md", "LICENSE"], "metadata": {"title": "مكتبتي الحرة", "description": "مكتبة رقمية مجانية تتيح للمستخدمين تصفح وقراءة وتحميل الكتب في مختلف المجالات", "language": "ar", "direction": "rtl", "charset": "UTF-8", "viewport": "width=device-width, initial-scale=1.0", "theme-color": "#4a90e2", "background-color": "#ffffff"}, "features": ["تصفح الكتب حسب التصنيفات", "البحث المتقدم عن الكتب", "قراءة الكتب أونلاين مع قارئ 3D Flipbook", "تحميل الكتب للمسجلين فقط", "نظام المفضلة والعلامات المرجعية", "الوضع الليلي والنهاري", "تصميم متجاوب للجوال", "لوحة تحكم للمديرين", "إدارة الكتب والمستخدمين", "تقارير وإحصائيات مفصلة"], "technologies": {"frontend": ["HTML5", "CSS3", "JavaScript ES6", "Bootstrap 5.3", "Font Awesome 6.4", "Google Fonts (Cairo)"], "libraries": ["PDF.js", "Turn.js", "Chart.js", "Bootstrap Icons"], "tools": ["Python HTTP Server", "VS Code Live Server"]}, "structure": {"pages": ["index.html - الصفحة الرئيسية", "books.html - صف<PERSON>ة جميع الكتب", "book-details.html - صف<PERSON>ة تفاصيل الكتاب", "reader.html - قا<PERSON><PERSON> الكتب", "login.html - صف<PERSON><PERSON> تسجيل الدخول", "admin.html - لو<PERSON>ة التحكم"], "assets": {"css": ["style.css - التصميم الرئيسي", "reader.css - تصميم القارئ", "admin.css - تصميم لوحة التحكم"], "js": ["main.js - الوظائف الرئيسية", "auth.js - نظام المصادقة", "reader.js - قا<PERSON><PERSON> الكتب", "book-details.js - تفاصيل الكتاب", "books.js - صف<PERSON><PERSON> الكتب", "admin.js - لوحة التحكم", "config.js - إعد<PERSON>ات الموقع"], "images": ["books/ - أغلفة الكتب", "avatars/ - صور المستخدمين", "default-book.png - غلاف افتراضي"]}}, "demo": {"users": {"regular": {"email": "<EMAIL>", "password": "password123", "role": "user"}, "admin": {"email": "<EMAIL>", "password": "admin123", "role": "admin"}}, "features": ["تسجيل دخول تجريبي", "بيانات كتب وهمية", "واجهة تفاعلية كاملة", "جميع الوظائف متاحة للتجربة"]}, "future": {"backend": ["PHP/Node.js API", "MySQL/PostgreSQL Database", "File Upload System", "Real Authentication"], "features": ["نظام تقييم متقدم", "خوارزمية اقتراح ذكية", "مشاركة اجتماعية", "دعم لغات إضافية", "تطبيق جوال", "نسخ احتياطي تلقائي"]}}