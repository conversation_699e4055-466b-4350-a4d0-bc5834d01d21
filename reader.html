<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قارئ الكتب - مكتبتي الحرة</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Google Fonts - Amiri -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">

    <style>
        body {
            background-color: #f8f9fa;
            overflow-x: hidden;
        }

        .reader-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .reader-header {
            background: white;
            border-bottom: 1px solid #dee2e6;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .reader-content {
            flex: 1;
            overflow-y: auto;
            position: relative;
        }

        .book-pages {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background: white;
            min-height: 100%;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .book-page {
            margin-bottom: 2rem;
            padding: 2rem;
            line-height: 1.8;
            font-family: 'Amiri', serif;
            font-size: 1.1rem;
            text-align: justify;
            display: none;
        }

        .book-page.active {
            display: block;
        }

        .chapter-title {
            font-family: 'Cairo', sans-serif;
            font-weight: 700;
            color: #4e73df;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .reading-progress {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: #e9ecef;
            z-index: 1001;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #4e73df, #36b9cc);
            transition: width 0.3s ease;
        }

        .reader-controls {
            background: white;
            border-top: 1px solid #dee2e6;
            padding: 1rem;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1rem;
        }

        .page-info {
            background: #f8f9fa;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .book-pages {
                padding: 1rem;
            }

            .book-page {
                padding: 1rem;
                font-size: 1rem;
            }

            .reader-controls {
                flex-wrap: wrap;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body class="reader-body">
    <!-- Reader Header -->
    <header class="reader-header bg-dark text-white py-2">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <div class="d-flex align-items-center">
                        <a href="book-details.html" class="btn btn-outline-light btn-sm me-2">
                            <i class="fas fa-arrow-right"></i>
                        </a>
                        <h6 class="mb-0 text-truncate">مئة عام من العزلة</h6>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="reader-controls d-flex justify-content-center align-items-center gap-2">
                        <button class="btn btn-outline-light btn-sm" id="prevPage" title="الصفحة السابقة">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                        
                        <div class="page-info d-flex align-items-center gap-2">
                            <span class="small">صفحة</span>
                            <input type="number" id="currentPage" class="form-control form-control-sm text-center" 
                                   style="width: 60px;" value="1" min="1">
                            <span class="small">من</span>
                            <span id="totalPages" class="small">417</span>
                        </div>
                        
                        <button class="btn btn-outline-light btn-sm" id="nextPage" title="الصفحة التالية">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="reader-options d-flex justify-content-end align-items-center gap-2">
                        <!-- Zoom Controls -->
                        <div class="zoom-controls d-flex align-items-center gap-1">
                            <button class="btn btn-outline-light btn-sm" id="zoomOut" title="تصغير">
                                <i class="fas fa-search-minus"></i>
                            </button>
                            <span id="zoomLevel" class="small">100%</span>
                            <button class="btn btn-outline-light btn-sm" id="zoomIn" title="تكبير">
                                <i class="fas fa-search-plus"></i>
                            </button>
                        </div>
                        
                        <!-- View Mode Toggle -->
                        <button class="btn btn-outline-light btn-sm" id="viewModeToggle" title="تغيير وضع العرض">
                            <i class="fas fa-book-open"></i>
                        </button>
                        
                        <!-- Fullscreen Toggle -->
                        <button class="btn btn-outline-light btn-sm" id="fullscreenToggle" title="ملء الشاشة">
                            <i class="fas fa-expand"></i>
                        </button>
                        
                        <!-- Dark Mode Toggle -->
                        <button class="btn btn-outline-light btn-sm" id="readerDarkMode" title="الوضع الليلي">
                            <i class="fas fa-moon"></i>
                        </button>
                        
                        <!-- Settings -->
                        <button class="btn btn-outline-light btn-sm" data-bs-toggle="dropdown" title="الإعدادات">
                            <i class="fas fa-cog"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><h6 class="dropdown-header">إعدادات القراءة</h6></li>
                            <li>
                                <div class="dropdown-item">
                                    <label class="form-label small">سرعة تقليب الصفحات</label>
                                    <input type="range" class="form-range" id="flipSpeed" min="300" max="1000" value="600">
                                </div>
                            </li>
                            <li>
                                <div class="dropdown-item">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="autoFlip">
                                        <label class="form-check-label small" for="autoFlip">
                                            تقليب تلقائي
                                        </label>
                                    </div>
                                </div>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="#" id="addBookmark">
                                    <i class="fas fa-bookmark me-2"></i>
                                    إضافة علامة مرجعية
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="#" id="showBookmarks">
                                    <i class="fas fa-list me-2"></i>
                                    العلامات المرجعية
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Reader Content -->
    <main class="reader-content">
        <div class="container-fluid h-100">
            <div class="row h-100">
                <!-- Sidebar (Hidden by default) -->
                <div class="col-md-3 reader-sidebar d-none" id="readerSidebar">
                    <div class="sidebar-content bg-light h-100 p-3">
                        <ul class="nav nav-tabs" id="sidebarTabs">
                            <li class="nav-item">
                                <button class="nav-link active" data-bs-toggle="tab" data-bs-target="#bookmarks">
                                    <i class="fas fa-bookmark"></i>
                                    العلامات
                                </button>
                            </li>
                            <li class="nav-item">
                                <button class="nav-link" data-bs-toggle="tab" data-bs-target="#contents">
                                    <i class="fas fa-list"></i>
                                    المحتويات
                                </button>
                            </li>
                            <li class="nav-item">
                                <button class="nav-link" data-bs-toggle="tab" data-bs-target="#notes">
                                    <i class="fas fa-sticky-note"></i>
                                    الملاحظات
                                </button>
                            </li>
                        </ul>
                        
                        <div class="tab-content mt-3">
                            <div class="tab-pane fade show active" id="bookmarks">
                                <div class="bookmarks-list">
                                    <p class="text-muted small">لا توجد علامات مرجعية</p>
                                </div>
                            </div>
                            
                            <div class="tab-pane fade" id="contents">
                                <div class="contents-list">
                                    <ul class="list-unstyled">
                                        <li><a href="#" class="text-decoration-none" data-page="1">الفصل الأول</a></li>
                                        <li><a href="#" class="text-decoration-none" data-page="25">الفصل الثاني</a></li>
                                        <li><a href="#" class="text-decoration-none" data-page="48">الفصل الثالث</a></li>
                                        <li><a href="#" class="text-decoration-none" data-page="72">الفصل الرابع</a></li>
                                        <li><a href="#" class="text-decoration-none" data-page="95">الفصل الخامس</a></li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="tab-pane fade" id="notes">
                                <div class="notes-list">
                                    <p class="text-muted small">لا توجد ملاحظات</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main Reader Area -->
                <div class="col reader-main" id="readerMain">
                    <div class="reader-container h-100 d-flex align-items-center justify-content-center">
                        <!-- 3D FlipBook Container -->
                        <div id="flipbook" class="flipbook-container">
                            <!-- Pages will be loaded here -->
                            <div class="page">
                                <div class="page-content d-flex align-items-center justify-content-center">
                                    <div class="text-center">
                                        <div class="spinner-border text-primary mb-3" role="status">
                                            <span class="visually-hidden">جاري التحميل...</span>
                                        </div>
                                        <p>جاري تحميل الكتاب...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- PDF Viewer (Alternative) -->
                        <div id="pdfViewer" class="pdf-viewer d-none">
                            <canvas id="pdfCanvas"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Reading Progress Bar -->
    <div class="reading-progress">
        <div class="progress" style="height: 3px;">
            <div class="progress-bar bg-primary" id="progressBar" style="width: 0%"></div>
        </div>
    </div>

    <!-- Bookmark Modal -->
    <div class="modal fade" id="bookmarkModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة علامة مرجعية</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="bookmarkForm">
                        <div class="mb-3">
                            <label for="bookmarkTitle" class="form-label">عنوان العلامة</label>
                            <input type="text" class="form-control" id="bookmarkTitle" required>
                        </div>
                        <div class="mb-3">
                            <label for="bookmarkNote" class="form-label">ملاحظة (اختياري)</label>
                            <textarea class="form-control" id="bookmarkNote" rows="3"></textarea>
                        </div>
                        <input type="hidden" id="bookmarkPage">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="saveBookmark">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Note Modal -->
    <div class="modal fade" id="noteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة ملاحظة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="noteForm">
                        <div class="mb-3">
                            <label for="noteContent" class="form-label">المحتوى</label>
                            <textarea class="form-control" id="noteContent" rows="4" required></textarea>
                        </div>
                        <input type="hidden" id="notePage">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="saveNote">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- PDF.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <!-- Turn.js for 3D FlipBook -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/turn.js/4.1.0/turn.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/reader.js"></script>
</body>
</html>
