#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خادم ويب بسيط لتشغيل مكتبتي الحرة
Simple web server for running My Free Library
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

# إعدادات الخادم
PORT = 8000
HOST = 'localhost'

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """معالج مخصص لطلبات HTTP"""
    
    def end_headers(self):
        # إضافة headers للأمان وتحسين الأداء
        self.send_header('X-Content-Type-Options', 'nosniff')
        self.send_header('X-Frame-Options', 'DENY')
        self.send_header('X-XSS-Protection', '1; mode=block')
        super().end_headers()
    
    def guess_type(self, path):
        """تحديد نوع الملف"""
        mimetype, encoding = super().guess_type(path)
        
        # إضافة أنواع ملفات إضافية
        if path.endswith('.js'):
            return 'application/javascript'
        elif path.endswith('.css'):
            return 'text/css'
        elif path.endswith('.json'):
            return 'application/json'
        elif path.endswith('.svg'):
            return 'image/svg+xml'
        
        return mimetype, encoding
    
    def log_message(self, format, *args):
        """تسجيل الرسائل مع الوقت"""
        import datetime
        timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {format % args}")

def check_files():
    """التحقق من وجود الملفات الأساسية"""
    required_files = [
        'index.html',
        'assets/css/style.css',
        'assets/js/main.js'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("⚠️  الملفات التالية مفقودة:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        print("\nيرجى التأكد من وجود جميع ملفات المشروع.")
        return False
    
    return True

def start_server():
    """بدء تشغيل الخادم"""
    try:
        # التحقق من وجود الملفات
        if not check_files():
            return
        
        # إنشاء الخادم
        with socketserver.TCPServer((HOST, PORT), CustomHTTPRequestHandler) as httpd:
            print("🚀 مكتبتي الحرة - خادم التطوير")
            print("=" * 50)
            print(f"📍 العنوان: http://{HOST}:{PORT}")
            print(f"📁 المجلد: {os.getcwd()}")
            print("=" * 50)
            print("💡 نصائح:")
            print("   - اضغط Ctrl+C لإيقاف الخادم")
            print("   - استخدم F5 لتحديث الصفحة")
            print("   - تحقق من وحدة التحكم للأخطاء")
            print("=" * 50)
            
            # فتح المتصفح تلقائياً
            try:
                webbrowser.open(f'http://{HOST}:{PORT}')
                print("🌐 تم فتح المتصفح تلقائياً")
            except:
                print("⚠️  لم يتم فتح المتصفح تلقائياً")
                print(f"   يرجى فتح: http://{HOST}:{PORT}")
            
            print("\n✅ الخادم يعمل... (اضغط Ctrl+C للإيقاف)")
            
            # تشغيل الخادم
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n\n🛑 تم إيقاف الخادم بواسطة المستخدم")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ المنفذ {PORT} مستخدم بالفعل")
            print("   جرب منفذ آخر أو أوقف الخادم الآخر")
        else:
            print(f"❌ خطأ في تشغيل الخادم: {e}")
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")

def show_help():
    """عرض المساعدة"""
    print("مكتبتي الحرة - خادم التطوير")
    print("=" * 30)
    print("الاستخدام:")
    print("  python start-server.py [OPTIONS]")
    print("\nالخيارات:")
    print("  -h, --help     عرض هذه المساعدة")
    print("  -p, --port     تحديد المنفذ (افتراضي: 8000)")
    print("  --host         تحديد العنوان (افتراضي: localhost)")
    print("\nأمثلة:")
    print("  python start-server.py")
    print("  python start-server.py -p 3000")
    print("  python start-server.py --host 0.0.0.0 -p 8080")

def main():
    """الدالة الرئيسية"""
    global PORT, HOST
    
    # معالجة المعاملات
    args = sys.argv[1:]
    i = 0
    while i < len(args):
        arg = args[i]
        
        if arg in ['-h', '--help']:
            show_help()
            return
        elif arg in ['-p', '--port']:
            if i + 1 < len(args):
                try:
                    PORT = int(args[i + 1])
                    i += 1
                except ValueError:
                    print("❌ رقم المنفذ غير صحيح")
                    return
            else:
                print("❌ يرجى تحديد رقم المنفذ")
                return
        elif arg == '--host':
            if i + 1 < len(args):
                HOST = args[i + 1]
                i += 1
            else:
                print("❌ يرجى تحديد العنوان")
                return
        else:
            print(f"❌ معامل غير معروف: {arg}")
            print("استخدم -h للمساعدة")
            return
        
        i += 1
    
    # بدء الخادم
    start_server()

if __name__ == '__main__':
    main()
