/**
 * Service Worker - مكتبتي الحرة
 * Service Worker - My Free Library
 */

const CACHE_NAME = 'my-free-library-v1.0.0';
const STATIC_CACHE = 'static-v1.0.0';
const DYNAMIC_CACHE = 'dynamic-v1.0.0';
const API_CACHE = 'api-v1.0.0';

// الملفات الأساسية للتخزين المؤقت
const STATIC_FILES = [
    '/',
    '/index.html',
    '/books.html',
    '/login.html',
    '/register.html',
    '/assets/css/bootstrap.min.css',
    '/assets/css/style.css',
    '/assets/js/bootstrap.bundle.min.js',
    '/assets/js/config.js',
    '/assets/js/main.js',
    '/assets/js/auth.js',
    '/assets/js/books.js',
    '/assets/js/performance.js',
    '/assets/images/logo.png',
    '/assets/images/default-book.png',
    '/assets/fonts/Cairo-Regular.woff2',
    '/assets/fonts/Amiri-Regular.woff2'
];

// الملفات التي يجب تحديثها دائماً
const NETWORK_FIRST = [
    '/api/',
    '/admin/',
    '/profile.html'
];

// الملفات التي يمكن تخزينها مؤقتاً لفترة طويلة
const CACHE_FIRST = [
    '/assets/',
    '/covers/',
    '.woff2',
    '.woff',
    '.ttf',
    '.jpg',
    '.jpeg',
    '.png',
    '.webp',
    '.svg'
];

/**
 * تثبيت Service Worker
 */
self.addEventListener('install', (event) => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then((cache) => {
                console.log('Service Worker: Caching static files');
                return cache.addAll(STATIC_FILES);
            })
            .then(() => {
                console.log('Service Worker: Installed successfully');
                return self.skipWaiting();
            })
            .catch((error) => {
                console.error('Service Worker: Installation failed', error);
            })
    );
});

/**
 * تفعيل Service Worker
 */
self.addEventListener('activate', (event) => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys()
            .then((cacheNames) => {
                return Promise.all(
                    cacheNames.map((cacheName) => {
                        // حذف التخزين المؤقت القديم
                        if (cacheName !== STATIC_CACHE && 
                            cacheName !== DYNAMIC_CACHE && 
                            cacheName !== API_CACHE) {
                            console.log('Service Worker: Deleting old cache', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker: Activated successfully');
                return self.clients.claim();
            })
    );
});

/**
 * اعتراض طلبات الشبكة
 */
self.addEventListener('fetch', (event) => {
    const { request } = event;
    const url = new URL(request.url);
    
    // تجاهل طلبات غير HTTP
    if (!request.url.startsWith('http')) {
        return;
    }
    
    // تحديد استراتيجية التخزين المؤقت
    if (isNetworkFirst(request.url)) {
        event.respondWith(networkFirst(request));
    } else if (isCacheFirst(request.url)) {
        event.respondWith(cacheFirst(request));
    } else if (isAPIRequest(request.url)) {
        event.respondWith(apiStrategy(request));
    } else {
        event.respondWith(staleWhileRevalidate(request));
    }
});

/**
 * استراتيجية الشبكة أولاً
 */
async function networkFirst(request) {
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.log('Service Worker: Network failed, trying cache', error);
        const cachedResponse = await caches.match(request);
        
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // إرجاع صفحة offline إذا لم توجد في التخزين المؤقت
        if (request.destination === 'document') {
            return caches.match('/offline.html');
        }
        
        throw error;
    }
}

/**
 * استراتيجية التخزين المؤقت أولاً
 */
async function cacheFirst(request) {
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
        return cachedResponse;
    }
    
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            const cache = await caches.open(STATIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.error('Service Worker: Cache and network failed', error);
        
        // إرجاع صورة افتراضية للصور
        if (request.destination === 'image') {
            return caches.match('/assets/images/default-book.png');
        }
        
        throw error;
    }
}

/**
 * استراتيجية API
 */
async function apiStrategy(request) {
    // للطلبات GET، جرب التخزين المؤقت أولاً
    if (request.method === 'GET') {
        const cachedResponse = await caches.match(request);
        
        if (cachedResponse) {
            // تحديث التخزين المؤقت في الخلفية
            fetch(request)
                .then(response => {
                    if (response.ok) {
                        const cache = caches.open(API_CACHE);
                        cache.then(c => c.put(request, response.clone()));
                    }
                })
                .catch(() => {});
            
            return cachedResponse;
        }
    }
    
    try {
        const networkResponse = await fetch(request);
        
        // تخزين مؤقت للطلبات الناجحة GET فقط
        if (networkResponse.ok && request.method === 'GET') {
            const cache = await caches.open(API_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        // للطلبات غير GET، حفظ في قائمة الانتظار
        if (request.method !== 'GET') {
            await saveRequestForLater(request);
            return new Response(
                JSON.stringify({
                    success: false,
                    message: 'تم حفظ الطلب للمزامنة لاحقاً',
                    offline: true
                }),
                {
                    status: 202,
                    headers: { 'Content-Type': 'application/json' }
                }
            );
        }
        
        throw error;
    }
}

/**
 * استراتيجية Stale While Revalidate
 */
async function staleWhileRevalidate(request) {
    const cachedResponse = await caches.match(request);
    
    const networkResponsePromise = fetch(request)
        .then(response => {
            if (response.ok) {
                const cache = caches.open(DYNAMIC_CACHE);
                cache.then(c => c.put(request, response.clone()));
            }
            return response;
        })
        .catch(() => {});
    
    return cachedResponse || networkResponsePromise;
}

/**
 * حفظ الطلب للمزامنة لاحقاً
 */
async function saveRequestForLater(request) {
    const requestData = {
        url: request.url,
        method: request.method,
        headers: Object.fromEntries(request.headers.entries()),
        body: request.method !== 'GET' ? await request.text() : null,
        timestamp: Date.now()
    };
    
    // حفظ في IndexedDB
    const db = await openDB();
    const transaction = db.transaction(['requests'], 'readwrite');
    const store = transaction.objectStore('requests');
    await store.add(requestData);
}

/**
 * فتح قاعدة بيانات IndexedDB
 */
function openDB() {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('OfflineRequests', 1);
        
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);
        
        request.onupgradeneeded = (event) => {
            const db = event.target.result;
            if (!db.objectStoreNames.contains('requests')) {
                const store = db.createObjectStore('requests', { 
                    keyPath: 'id', 
                    autoIncrement: true 
                });
                store.createIndex('timestamp', 'timestamp');
            }
        };
    });
}

/**
 * مزامنة الطلبات المحفوظة
 */
self.addEventListener('sync', (event) => {
    if (event.tag === 'background-sync') {
        event.waitUntil(syncOfflineRequests());
    }
});

async function syncOfflineRequests() {
    try {
        const db = await openDB();
        const transaction = db.transaction(['requests'], 'readonly');
        const store = transaction.objectStore('requests');
        const requests = await store.getAll();
        
        for (const requestData of requests) {
            try {
                const response = await fetch(requestData.url, {
                    method: requestData.method,
                    headers: requestData.headers,
                    body: requestData.body
                });
                
                if (response.ok) {
                    // حذف الطلب بعد المزامنة الناجحة
                    const deleteTransaction = db.transaction(['requests'], 'readwrite');
                    const deleteStore = deleteTransaction.objectStore('requests');
                    await deleteStore.delete(requestData.id);
                }
            } catch (error) {
                console.error('فشل في مزامنة الطلب:', error);
            }
        }
    } catch (error) {
        console.error('فشل في مزامنة الطلبات:', error);
    }
}

/**
 * معالجة الرسائل من الصفحة الرئيسية
 */
self.addEventListener('message', (event) => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
    
    if (event.data && event.data.type === 'CACHE_URLS') {
        event.waitUntil(
            caches.open(DYNAMIC_CACHE)
                .then(cache => cache.addAll(event.data.urls))
        );
    }
});

/**
 * وظائف مساعدة
 */
function isNetworkFirst(url) {
    return NETWORK_FIRST.some(pattern => url.includes(pattern));
}

function isCacheFirst(url) {
    return CACHE_FIRST.some(pattern => url.includes(pattern));
}

function isAPIRequest(url) {
    return url.includes('/api/');
}

/**
 * تنظيف التخزين المؤقت القديم
 */
self.addEventListener('periodicsync', (event) => {
    if (event.tag === 'cache-cleanup') {
        event.waitUntil(cleanupOldCache());
    }
});

async function cleanupOldCache() {
    const cacheNames = await caches.keys();
    
    for (const cacheName of cacheNames) {
        const cache = await caches.open(cacheName);
        const requests = await cache.keys();
        
        for (const request of requests) {
            const response = await cache.match(request);
            const cacheDate = new Date(response.headers.get('date'));
            const now = new Date();
            const daysDiff = (now - cacheDate) / (1000 * 60 * 60 * 24);
            
            // حذف الملفات المخزنة لأكثر من 7 أيام
            if (daysDiff > 7) {
                await cache.delete(request);
            }
        }
    }
}

console.log('Service Worker: Loaded successfully');
