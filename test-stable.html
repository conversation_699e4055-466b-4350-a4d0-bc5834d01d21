<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الاستقرار - مكتبتي الحرة</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <style>
        /* CSS إضافي لضمان الاستقرار */
        * {
            box-sizing: border-box;
        }
        
        body {
            margin: 0;
            padding: 0;
            overflow-x: hidden;
            position: relative;
        }
        
        .stable-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: relative;
        }
        
        .test-card:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }
        
        .stable-btn {
            background: #4e73df;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        
        .stable-btn:hover {
            background: #3d5aa3;
        }
        
        .no-animation {
            animation: none !important;
            transform: none !important;
            transition: none !important;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-book-open me-2"></i>
                مكتبتي الحرة
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.html">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="books.html">الكتب</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.html">حسابي</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="stable-container">
        <div class="text-center mb-5">
            <h1 class="display-4 mb-3">اختبار الاستقرار</h1>
            <p class="lead">هذه الصفحة لاختبار استقرار الموقع بدون حركة مزعجة</p>
        </div>

        <!-- بطاقات الاختبار -->
        <div class="row">
            <div class="col-md-4">
                <div class="test-card">
                    <h5><i class="fas fa-book text-primary me-2"></i>كتاب تجريبي 1</h5>
                    <p>هذا نص تجريبي لاختبار استقرار البطاقة. يجب أن تكون ثابتة ولا تتحرك.</p>
                    <button class="stable-btn">قراءة المزيد</button>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="test-card">
                    <h5><i class="fas fa-star text-warning me-2"></i>كتاب تجريبي 2</h5>
                    <p>هذا نص تجريبي آخر لاختبار الاستقرار. البطاقة يجب أن تبقى في مكانها.</p>
                    <button class="stable-btn">قراءة المزيد</button>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="test-card">
                    <h5><i class="fas fa-heart text-danger me-2"></i>كتاب تجريبي 3</h5>
                    <p>نص تجريبي ثالث للتأكد من أن جميع العناصر مستقرة وثابتة.</p>
                    <button class="stable-btn">قراءة المزيد</button>
                </div>
            </div>
        </div>

        <!-- قسم النص الطويل -->
        <div class="test-card">
            <h3>نص طويل لاختبار الاستقرار</h3>
            <p>
                هذا نص طويل لاختبار استقرار الصفحة عند وجود محتوى كثير. يجب أن تبقى الصفحة ثابتة 
                ولا تتحرك أو تهتز. النص يجب أن يكون واضحاً ومقروءاً بدون أي تأثيرات مزعجة.
            </p>
            <p>
                الهدف من هذا الاختبار هو التأكد من أن جميع العناصر في الصفحة مستقرة وثابتة، 
                وأن المستخدم يمكنه القراءة والتفاعل مع المحتوى بشكل مريح بدون أي إزعاج بصري.
            </p>
            <p>
                إذا كانت الصفحة تتحرك أو تهتز، فهذا يعني أن هناك مشكلة في CSS أو JavaScript 
                تحتاج إلى إصلاح. الهدف هو تجربة مستخدم سلسة ومريحة.
            </p>
        </div>

        <!-- أزرار الاختبار -->
        <div class="test-card text-center">
            <h4>اختبار التفاعل</h4>
            <div class="d-flex justify-content-center gap-3 flex-wrap">
                <button class="stable-btn" onclick="testStability()">
                    <i class="fas fa-check me-2"></i>اختبار الاستقرار
                </button>
                <button class="stable-btn" onclick="toggleTheme()">
                    <i class="fas fa-moon me-2"></i>تبديل الوضع
                </button>
                <button class="stable-btn" onclick="showMessage()">
                    <i class="fas fa-info me-2"></i>رسالة تجريبية
                </button>
            </div>
        </div>

        <!-- منطقة الرسائل -->
        <div id="messageArea" class="test-card" style="display: none;">
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                تم الاختبار بنجاح! الصفحة مستقرة وثابتة.
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // وظائف الاختبار
        function testStability() {
            const messageArea = document.getElementById('messageArea');
            messageArea.style.display = 'block';
            
            // إخفاء الرسالة بعد 3 ثوان
            setTimeout(() => {
                messageArea.style.display = 'none';
            }, 3000);
        }
        
        function toggleTheme() {
            document.body.classList.toggle('dark-theme');
        }
        
        function showMessage() {
            alert('هذه رسالة تجريبية لاختبار التفاعل');
        }
        
        // منع أي انيميشن غير مرغوب فيه
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة فئة منع الانيميشن لجميع العناصر
            if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
                document.body.classList.add('no-animation');
            }
            
            // منع التمرير الأفقي
            document.body.style.overflowX = 'hidden';
            
            console.log('تم تحميل صفحة الاختبار بنجاح - الصفحة يجب أن تكون مستقرة');
        });
        
        // مراقبة تغيير حجم النافذة
        window.addEventListener('resize', function() {
            // منع أي حركة أثناء تغيير الحجم
            document.body.style.transition = 'none';
            setTimeout(() => {
                document.body.style.transition = '';
            }, 100);
        });
    </script>
</body>
</html>
